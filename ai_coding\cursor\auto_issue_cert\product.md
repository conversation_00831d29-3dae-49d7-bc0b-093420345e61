# SSL证书自动化申请系统设计文档

## 项目概述
本项目旨在实现证书申请的自动化流程，通过Python3.8和Shell脚本在CentOS7服务器上完成域名获取、检测、申请证书及同步部署生产的全流程自动化操作。

## 技术栈
- 编程语言：Python 3.8+、Shell脚本
- 运行环境：CentOS 7
- 数据存储：Redis 3.2.12
- 通知机制：Telegram机器人（先不实现）
- 同步工具：rsync

## 设计思路
系统分为三个主要任务模块：
### 任务一：域名接收与处理模块
读取域名接口，对域名进行过虑，符合要求的域名加入指定队列。
sleep N秒后，再次循环上面步骤(请求接口--域名过虑--写入队列)

### 任务二：读取队列生成证书模块
从任务一写入队列后，需要开启两个线程处理对应消息队列(分别是domains_queue_autoa和domains_queue_autob)
两组中，各自对队列内的消息进行消费，每次只读取一条，消费完成才可以继续消费下一条，执行结果需要判断：
- 执行失败时进入判断，查询redis队列是否有相同{num}，比如当前任务是autoa3，如果下一条有autoa3则直接消费，没有则再重试一次消费任务。
- 任务处理成功，则继续下一条消费，无任务则等待后，继续循环上面操作。

### 任务三：证书续签模块(暂不实现)
读取宝塔bt所有证书小于10天的站点，顺序单个续签证书
  - 检查{site}域名状态，删除无效域名并更新至宝塔站点，然后提交生成证书
  - 成功则进入下一个{site}继续生成
  - 失败则进入第2次重试

## 详细设计
### 任务一：域名接收与处理模块

#### 1. 域名接收与保存
- **功能描述**：通过HTTP接口获取需要申请证书的域名列表
- **实现方式**：
  - 从配置的API URL（`http://38.145.216.14/222.txt`）获取域名文本
  - 域名格式为空格分隔的文本，如：`a.com b.com c.com d.com e.com`
  - 移除获取的域名文本首尾空白字符
  - 保存到{domains}变量中，进入下一步处理
  - 请求失败时记录错误日志

#### 2. 域名过滤与解析检测
- **功能描述**：对域名进行两轮过滤，确保只处理有效且需要申请证书的域名
  - 读取上一步传递过来的{domains}变量
- **实现方式**：
  - **第一轮nginx过滤**：
    - 检查域名是否已存在nginx配置中，存在域名则移除列表中域名，
    - 并写入redis集合(domains_exists)，如果已经存在则不添加
      - 判断命令：`grep -roP "(^|\s){domain}(\s|;|$)" /www/cloud_waf/nginx/conf.d/vhost/`
    - 其余域名写入redis排序集合(domains_cert)并初始化计数为0，如果已存在域名则不添加
  - **第二轮dig解析过滤**：
    - 读取redis排序集合(domains_cert)，保存域名到临时文件
    - 调用外部脚本`sh /root/ajie/yunwei/auto_domains_check.sh`进行域名解析检测
      - 使用两个变量接收解析成功和失败的域名(脚本中域名解析失败或IP解析不正确都属于失败)
    - 读取失败变量，循环对域名失败计数+1
      - 若计数达到30，则从排序集合(domains_cert)移除并加入集合(domains_remove)
    - 读取成功变量，移除(domains_cert)内的域名，同时传递给下一步处理
  [注释]：(domains_cert)使用排序集合，域名为成员，失败次数为分数，方便增减和查询。

#### 3. 添加域名到Redis队列
- **功能描述**：添加域名并写入Redis队列，同时更新宝塔bt站点信息
- **实现方式**：
  - **提取waf中nginx指定站点**：
    - 设置站点a/b两组进行轮询，避免短时间同一站点频繁提交：autoa{num}(如autoa1/autoa2)、autob{num}(如autob1/autob2)
    - 设置初始值为a：current_group = 'a' ，下一轮再切换为b组
    - 拼接后调用`sh /root/ajie/yunwei/auto_get_key_values.sh auto{current_group}`
    - 获取最新站点ID`site_id`和域名列表`domains`，解析JSON数据
  - **域名添加流程**
    - 循环处理待添加域名：
      - 计算当前站点可添加域名数量(上限100个)，判断如果等于0则调用脚本创建新站点
        - 创建逻辑：站点{num}+1（自动递增，如autoa1 → autoa2）
        - 执行`/root/ajie/yunwei/auto_create_waf_site.sh {new_site}`创建新WAF站点
      - 调用脚本(auto_get_key_values.sh)获取新站点信息，继续添加域名
      - 如果未达到100上限，同步更新宝塔bt站点信息后(执行auto_bt_api.py脚本)，提交Redis队列
      - 如果达到上限，也是先同步更新bt站点，再提交Redis队列，剩余域名继续下一轮循环处理
    [注释]：使用JSON格式存储和传递站点信息，确保数据一致性
  - **异常处理**：
    - 处理过程中的异常会被捕获并记录到日志

#### 4. sleep停顿后继续循环第1点
- **功能描述**：等待一段时间后继续循环以上步骤
- **实现方式**：
  - sleep 120
  - 切换 current_group = 'b'
  - 继续执行第1点请求域名接口

### 任务二：读取队列生成证书模块

#### 1. 证书生成任务处理
- **功能描述**：从Redis队列中读取站点信息，调用脚本生成证书，并处理成功/失败场景
- **实现方式**：
  - 启动两个独立线程，分别处理A组和B组队列（domains_queue_autoa和domains_queue_autob）
  - 每个线程循环执行以下操作：
    - 使用BLPOP命令从队列左侧阻塞读取一条记录（站点任务数据），超时时间设置为30秒
    - 解析队列中的JSON数据，获取站点ID(site_id)和关联域名列表(domains)
    - 记录任务开始处理时间和状态到Redis（key格式：cert_task_{site_id}status）
    - 调用证书生成脚本：/root/ajie/yunwei/apply_cert.sh {site_id}，同时发送生成站点消息通知(先用print()代替)
    - 根据脚本执行结果进行后续处理：
  - 失败场景处理：
    - 更新Redis中任务状态为"失败"，记录失败日志
    - 失败后进入判断，查询redis队列是否有相同{num}(比如autoa3，是否还有3)
    - 如果下一条有相同{num}则直接消费下一条，
    - 没有则间隔2分钟后再重试一次当前消费任务，最大重试3次，同时发送重试消息通知(先用print代替)
    - 3次再失败则丢弃当前任务，继续下一个队列任务，同时发送通知({site_id}3次重试失败，人工确认)
    - 如果执行成功，发送成功通知（包含站点ID、处理时间、成功域名数量）。
    - 申请完成则请求success接口发送site_id及证书文本(先pass留空)。
    - 继续处理队列中的下一条任务
  - 成功场景处理：
    - 更新Redis中任务状态为"成功"，文本（/root/.acme.sh/{site_id}/cert.pem、/root/.acme.sh/{site_id}/privkey.pem）
    - 生成的证书记录到Redis（key格式：cert_info_{site_id}）
    - 发送成功通知（包含站点ID、处理时间、成功域名数量）
    - 调用success接口，上报站点ID和证书文本信息
    - 继续处理队列中的下一条任务
  - 进入等待直到新的redis队列任务。


### 任务三：证书同步模块（先不生成）

#### 1. 执行同步

- **功能描述**：将生成的证书同步到各主机
- **实现方式**：
  - 证书成功生成并配置好WAF和宝塔后
  - 执行rsync同步命令到各主机（已实现）

## 已实现功能

- `domains_check.sh`：域名解析检测
- `issue_cert.sh`：申请证书脚本
- rsync同步功能

## 先不生成功能

- Telegram机器人告警逻辑
- 写入WAF功能
- 写入宝塔功能
- 证书生成失败后重新加入队列的逻辑

## 流程图

```
接口获取域名 → 域名过滤与检测 → 写入Redis队列 → 生成证书 → 配置WAF和宝塔 → 同步到各主机
```

## 注意事项

- Redis队列管理需要考虑并发情况
- 域名数量上限为每个站点100个
- 证书生成失败需要有完善的告警和重试机制