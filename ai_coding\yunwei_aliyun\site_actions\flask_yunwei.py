from flask import Flask, request, Response, render_template
import subprocess

app = Flask(__name__)

@app.route('/yunwei/')
def index():
    return render_template('index.html')
    
@app.route('/yunwei/<param>/<server_id>', methods=['GET'])
def execute_script(param, server_id):
    def generate():
        process = subprocess.Popen(
            ["sh", "/root/ajie/tongbu_site.sh", param, server_id], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        
        for line in process.stdout:
            yield f"{line}\n"
        for line in process.stderr:
            yield f"ERROR: {line}\n"
        
        process.wait()
        yield f"操作执行完毕！响应码是:{process.returncode} (0:正常 1:异常)\n"

    return Response(generate(), mimetype='text/plain')

if __name__ == "__main__":
    app.run(host='127.0.0.1', port=50000, debug=True)
