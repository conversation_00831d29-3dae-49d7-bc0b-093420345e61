function dns($params, $error = 1){
  $secretId = "AKIDe6MQHuJVUG0Ys0znPo8UsBIvSmRxGNHx";
  $secretKey = "XqwoSt66tSoAMNZjneIl38zc2FE0hOwH";
  $host = "dnspod.tencentcloudapi.com";
  /*
  $action = "DescribeRecordFilterList";
  $version = "2021-03-23";
  $region = "";
  $SubDomain = 'oscar';
  $params = array(
    "Action" => $action,
    "Version" => $version,
    'Domain' => 'buyusdt.me',
    'SubDomain' => $SubDomain,
  );
  */
  $timestamp = time();
  $params["Timestamp"] = $timestamp;
  $params["Nonce"] = mt_rand(1000000, 9999999);
  $params["SecretId"] = $secretId;
  $params["SignatureMethod"] = "HmacSHA256";
  ksort($params);
  $paramStr = "GET" . $host . "/?" . http_build_query($params);
  $signature = base64_encode(hash_hmac("sha256", $paramStr, $secretKey, true));
  $url = "https://" . $host . "/?" . http_build_query($params) . "&Signature=" . urlencode($signature);
  $change_domain_log = curl_post($url);
  $change_domain_log['body'] = json_decode($change_domain_log['body'], true);
  if(!empty($change_domain_log['body']['Response']) && empty($change_domain_log['body']['Response']['Error'])){
    return $change_domain_log;
  }else{
      return $change_domain_log;
    if($error >= 3){
      return $change_domain_log;
    }else{
      $error += 1;
      return dns($params, $error);
    }
  }
}