# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_nlb20220430.client import Client as Nlb20220430Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_nlb20220430 import models as nlb_20220430_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client(
        access_key_id: str,
        access_key_secret: str,
    ) -> Nlb20220430Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 必填，您的 AccessKey ID,
            access_key_id='LTAI5tEkAxhEdVfpH16X9i5B',
            # 必填，您的 AccessKey Secret,
            access_key_secret='******************************'
        )
        # 访问的域名
        config.endpoint = f'nlb.cn-chengdu.aliyuncs.com'
        return Nlb20220430Client(config)

    @staticmethod
    def create_client_with_sts(
        access_key_id: str,
        access_key_secret: str,
        security_token: str,
    ) -> Nlb20220430Client:
        """
        使用STS鉴权方式初始化账号Client，推荐此方式。
        @param access_key_id:
        @param access_key_secret:
        @param security_token:
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 必填，您的 AccessKey ID,
            access_key_id='LTAI5tEkAxhEdVfpH16X9i5B',
            # 必填，您的 AccessKey Secret,
            access_key_secret='******************************',
            # 必填，您的 Security Token,
            security_token=security_token,
            # 必填，表明使用 STS 方式,
            type='sts'
        )
        # 访问的域名
        config.endpoint = f'nlb.cn-chengdu.aliyuncs.com'
        return Nlb20220430Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        # 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html
        client = Sample.create_client('LTAI5tEkAxhEdVfpH16X9i5B', '******************************')
        load_balancer_billing_config = nlb_20220430_models.CreateLoadBalancerRequestLoadBalancerBillingConfig(
            # String, 可选, 网络型负载均衡实例的计费类型。  仅取值**PostPay**：表示按量计费。,
            pay_type='PostPay'
        )
        zone_mappings_0 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            # String, 必填, 可用区对应的交换机，每个可用区只能使用一台交换机和一个子网。至少需要添加2个可用区，最多支持添加10个可用区。,
            v_switch_id='vsw-2vck72fhhi7sivr21pc93',
            # String, 必填, 网络型负载均衡实例的可用区ID。至少需要添加2个可用区，最多支持添加10个可用区。  您可以通过调用[DescribeZones](~~443890~~)接口获取可用区ID。,
            zone_id='cn-chengdu-a'
        )
        zone_mappings_1 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            # String, 必填, 可用区对应的交换机，每个可用区只能使用一台交换机和一个子网。至少需要添加2个可用区，最多支持添加10个可用区。,
            v_switch_id='vsw-2vcumkdfi29haga025obi',
            # String, 必填, 网络型负载均衡实例的可用区ID。至少需要添加2个可用区，最多支持添加10个可用区。  您可以通过调用[DescribeZones](~~443890~~)接口获取可用区ID。,
            zone_id='cn-chengdu-b'
        )
        create_load_balancer_request = nlb_20220430_models.CreateLoadBalancerRequest(
            load_balancer_type='network',
            load_balancer_name='客户1',
            address_type='Internet',
            address_ip_version='ipv4',
            vpc_id='vpc-2vcrj45y9n2fzjqvcnr8o',
            # Array, 可选,
            zone_mappings=[
                zone_mappings_0,
                zone_mappings_1
            ],
            # Object, 可选,
            load_balancer_billing_config=load_balancer_billing_config,
            region_id='cn-chengdu'
        )
        runtime = util_models.RuntimeOptions()
        resp = client.create_load_balancer_with_options(create_load_balancer_request, runtime)
        ConsoleClient.log(UtilClient.to_jsonstring(resp))

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        # 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html
        client = Sample.create_client(os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'], os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'])
        load_balancer_billing_config = nlb_20220430_models.CreateLoadBalancerRequestLoadBalancerBillingConfig(
            # String, 可选, 网络型负载均衡实例的计费类型。  仅取值**PostPay**：表示按量计费。,
            pay_type='PostPay'
        )
        zone_mappings_0 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            # String, 必填, 可用区对应的交换机，每个可用区只能使用一台交换机和一个子网。至少需要添加2个可用区，最多支持添加10个可用区。,
            v_switch_id='vsw-2vck72fhhi7sivr21pc93',
            # String, 必填, 网络型负载均衡实例的可用区ID。至少需要添加2个可用区，最多支持添加10个可用区。  您可以通过调用[DescribeZones](~~443890~~)接口获取可用区ID。,
            zone_id='cn-chengdu-a'
        )
        zone_mappings_1 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            # String, 必填, 可用区对应的交换机，每个可用区只能使用一台交换机和一个子网。至少需要添加2个可用区，最多支持添加10个可用区。,
            v_switch_id='vsw-2vcumkdfi29haga025obi',
            # String, 必填, 网络型负载均衡实例的可用区ID。至少需要添加2个可用区，最多支持添加10个可用区。  您可以通过调用[DescribeZones](~~443890~~)接口获取可用区ID。,
            zone_id='cn-chengdu-b'
        )
        create_load_balancer_request = nlb_20220430_models.CreateLoadBalancerRequest(
            load_balancer_type='network',
            load_balancer_name='客户1',
            address_type='Internet',
            address_ip_version='ipv4',
            vpc_id='vpc-2vcrj45y9n2fzjqvcnr8o',
            # Array, 可选,
            zone_mappings=[
                zone_mappings_0,
                zone_mappings_1
            ],
            # Object, 可选,
            load_balancer_billing_config=load_balancer_billing_config,
            region_id='cn-chengdu'
        )
        runtime = util_models.RuntimeOptions()
        resp = await client.create_load_balancer_with_options_async(create_load_balancer_request, runtime)
        ConsoleClient.log(UtilClient.to_jsonstring(resp))


if __name__ == '__main__':
    Sample.main(sys.argv[1:])
