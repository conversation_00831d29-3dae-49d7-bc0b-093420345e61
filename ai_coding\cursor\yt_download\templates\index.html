<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube 视频下载器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 标题部分 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">YouTube 视频下载器</h1>
            <p class="text-gray-600">输入 YouTube 视频链接，轻松下载高质量视频</p>
        </div>

        <!-- 下载表单 -->
        <div class="max-w-xl mx-auto bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex gap-4">
                <input type="text" 
                       id="videoUrl" 
                       class="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="请输入 YouTube 视频链接">
                <button onclick="downloadVideo()"
                        class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200">
                    下载
                </button>
            </div>
            <div id="downloadStatus" class="mt-4 text-center hidden">
                <div class="animate-spin inline-block w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                <p class="text-gray-600 mt-2">正在下载中...</p>
            </div>
        </div>

        <!-- 下载列表 -->
        <div class="max-w-4xl mx-auto">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">已下载视频</h2>
            <div id="downloadsList" class="space-y-4">
                {% for video in downloads %}
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ video.title }}</h3>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <p class="text-gray-600"><span class="font-medium">时长：</span>{{ video.duration }}</p>
                            <p class="text-gray-600"><span class="font-medium">作者：</span>{{ video.author }}</p>
                            <p class="text-gray-600"><span class="font-medium">文件大小：</span>{{ video.file_size }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600"><span class="font-medium">下载时间：</span>{{ video.download_date }}</p>
                            <p class="text-gray-600"><span class="font-medium">存储路径：</span>{{ video.file_path }}</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4"><span class="font-medium">描述：</span>{{ video.description }}</p>
                    <video controls class="w-full rounded-lg">
                        <source src="/{{ video.file_path }}" type="video/mp4">
                        您的浏览器不支持 HTML5 视频播放。
                    </video>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        async function downloadVideo() {
            const videoUrl = document.getElementById('videoUrl').value;
            const downloadStatus = document.getElementById('downloadStatus');
            const downloadsList = document.getElementById('downloadsList');

            if (!videoUrl) {
                alert('请输入视频链接！');
                return;
            }

            try {
                downloadStatus.classList.remove('hidden');

                const response = await fetch('/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: videoUrl })
                });

                const data = await response.json();

                if (response.ok) {
                    // 创建新的视频卡片
                    const videoCard = `
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">${data.title}</h3>
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <p class="text-gray-600"><span class="font-medium">时长：</span>${data.duration}</p>
                                    <p class="text-gray-600"><span class="font-medium">作者：</span>${data.author}</p>
                                    <p class="text-gray-600"><span class="font-medium">文件大小：</span>${data.file_size}</p>
                                </div>
                                <div>
                                    <p class="text-gray-600"><span class="font-medium">下载时间：</span>${data.download_date}</p>
                                    <p class="text-gray-600"><span class="font-medium">存储路径：</span>${data.file_path}</p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4"><span class="font-medium">描述：</span>${data.description}</p>
                            <video controls class="w-full rounded-lg">
                                <source src="/${data.file_path}" type="video/mp4">
                                您的浏览器不支持 HTML5 视频播放。
                            </video>
                        </div>
                    `;
                    downloadsList.insertAdjacentHTML('afterbegin', videoCard);
                    document.getElementById('videoUrl').value = '';
                } else {
                    alert(`下载失败：${data.error}`);
                }
            } catch (error) {
                alert('下载过程中发生错误！');
                console.error(error);
            } finally {
                downloadStatus.classList.add('hidden');
            }
        }
    </script>
</body>
</html> 