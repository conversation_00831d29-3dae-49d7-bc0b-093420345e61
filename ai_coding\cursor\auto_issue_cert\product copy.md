# SSL证书自动化申请系统设计文档

## 项目概述
本项目旨在实现证书申请的自动化流程，通过Python3.8和Shell脚本在CentOS7服务器上完成域名获取、检测、申请证书及同步部署生产的全流程自动化操作。

## 技术栈
- 编程语言：Python 3.8+、Shell脚本
- 运行环境：CentOS 7
- 数据存储：Redis 3.2.12
- 通知机制：Telegram机器人（先不实现）
- 同步工具：rsync

## 设计思路
系统分为三个主要任务模块：
### 任务一：域名接收与处理模块
读取域名接口，对域名进行过虑，符合要求的域名加入指定队列。
sleep N秒后，再次循环上面步骤(请求接口--域名过虑--写入队列)

### 任务二：读取队列生成证书模块



开启两个线程同时执行两条队列任务，redis队列有两组不同组site需要生成证书(如autoa{num}、autob{num}两组)，同时标记当前各自site组为锁状态
  - 开始生成{site}证书前，tg发送消息通知(开始生成{site}证书)，然后调用执行颁发证书脚本(auto_issue_cert.sh)
  - 成功则调用同步waf脚本(bt和waf)，同时tg发送通知({site}证书申请完成)
  - 失败则进入判断，查询redis队列中是否有当前{site}组队列：
    - 有则丢弃当前{site}任务并直接开始下一个相同{site}任务，
        同时tg发送消息通知({site}申请失败，进入下一个相同{site}任务)
        成功则执行同步脚本，失败则继续判断。
    - 无则进入第二次重试，同时tg发送消息({site}申请失败，进入第2次重试)
  - 第2次再失败则解除{site}锁并丢弃当前任务，继续下一个队列任务，tg通知({site}第2次重试失败，人工确认)
  - 申请完成则请求success接口告之{site}及证书文件。
  - 进入等待直到新的redis队列任务。

### 任务三：证书续签模块(暂不实现)
读取宝塔bt所有证书小于10天的站点，顺序单个续签证书
  - 检查{site}域名状态，删除无效域名并更新至宝塔站点，然后提交生成证书
  - 成功则进入下一个{site}继续生成
  - 失败则进入第2次重试

## 详细设计
### 任务一：域名接收与处理模块

#### 1. 域名接收与保存
- **功能描述**：通过HTTP接口获取需要申请证书的域名列表
- **实现方式**：
  - 从配置的API URL（`http://38.145.216.14/222.txt`）获取域名文本
  - 域名格式为空格分隔的文本，如：`a.com b.com c.com d.com e.com`
  - 移除获取的域名文本首尾空白字符
  - 保存到{domains}变量中，进入下一步处理
  - 请求失败时记录错误日志

#### 2. 域名过滤与解析检测
- **功能描述**：对域名进行两轮过滤，确保只处理有效且需要申请证书的域名
  - 读取上一步传递过来的{domains}变量
- **实现方式**：
  - **第一轮nginx过滤**：
    - 检查域名是否已存在nginx配置中，存在域名则移除列表中域名，
    - 并写入redis集合(domains_exists)，如果已经存在则不添加
      - 判断命令：`grep -roP "(^|\s){domain}(\s|;|$)" /www/cloud_waf/nginx/conf.d/vhost/`
    - 其余域名写入redis排序集合(domains_cert)并初始化计数为0，如果已存在域名则不添加
  - **第二轮dig解析过滤**：
    - 读取redis排序集合(domains_cert)，保存域名到临时文件
    - 调用外部脚本`sh /root/ajie/yunwei/auto_domains_check.sh`进行域名解析检测
      - 使用两个变量接收解析成功和失败的域名(脚本中域名解析失败或IP解析不正确都属于失败)
    - 读取失败变量，循环对域名失败计数+1
      - 若计数达到30，则从排序集合(domains_cert)移除并加入集合(domains_remove)
    - 读取成功变量，移除(domains_cert)内的域名，同时传递给下一步处理
  [注释]：(domains_cert)使用排序集合，域名为成员，失败次数为分数，方便增减和查询。

#### 3. 添加域名到Redis队列
- **功能描述**：添加域名并写入Redis队列，同时更新宝塔bt站点信息
- **实现方式**：
  - **提取waf中nginx指定站点**：
    - 设置站点a/b两组进行轮询，避免短时间同一站点频繁提交：autoa{num}(如autoa1/autoa2)、autob{num}(如autob1/autob2)
    - 设置初始值为a：current_group = 'a' ，下一轮再切换为b组
    - 拼接后调用`sh /root/ajie/yunwei/auto_get_key_values.sh auto{current_group}`
    - 获取最新站点ID`site_id`和域名列表`domains`，解析JSON数据
  - **域名添加流程**
    - 循环处理待添加域名：
      - 计算当前站点可添加域名数量(上限100个)，判断如果等于0则调用脚本创建新站点
        - 创建逻辑：站点{num}+1（自动递增，如autoa1 → autoa2）
        - 执行`/root/ajie/yunwei/auto_create_waf_site.sh {new_site}`创建新WAF站点
      - 调用脚本(auto_get_key_values.sh)获取新站点信息，继续添加域名
      - 如果未达到100上限，同步更新宝塔bt站点信息后(执行auto_bt_api.py脚本)，提交Redis队列
      - 如果达到上限，也是先同步更新bt站点，再提交Redis队列，剩余域名继续下一轮循环处理
    [注释]：使用JSON格式存储和传递站点信息，确保数据一致性
  - **异常处理**：
    - 处理过程中的异常会被捕获并记录到日志

#### 4. sleep停顿后继续循环第1点
- **功能描述**：等待一段时间后继续循环以上步骤
- **实现方式**：
  - sleep 120
  - 切换 current_group = 'b'
  - 继续执行第1点请求域名接口

### 任务二：证书生成模块

#### 1. 取Redis参数生成证书

- **功能描述**：监测Redis队列，取出队列任务生成证书
- **实现方式**：
  - 实时监测Redis队列
  - 发现新任务时，从右取出一条队列中的key和values
  - 将values中的域名写入`${SITE}`临时文件
  - 使用key作为参数执行申请证书脚本`sh issue_cert.sh key`（已实现）
  - 成功后去除队列标记

#### 2. 写入WAF

#### 3. 写入宝塔

#### 4. 失败处理


### 任务三：证书同步模块（先不生成）

#### 1. 执行同步

- **功能描述**：将生成的证书同步到各主机
- **实现方式**：
  - 证书成功生成并配置好WAF和宝塔后
  - 执行rsync同步命令到各主机（已实现）

## 已实现功能

- `domains_check.sh`：域名解析检测
- `issue_cert.sh`：申请证书脚本
- rsync同步功能

## 先不生成功能

- Telegram机器人告警逻辑
- 写入WAF功能
- 写入宝塔功能
- 证书生成失败后重新加入队列的逻辑

## 流程图

```
接口获取域名 → 域名过滤与检测 → 写入Redis队列 → 生成证书 → 配置WAF和宝塔 → 同步到各主机
```

## 注意事项

- Redis队列管理需要考虑并发情况
- 域名数量上限为每个站点100个
- 证书生成失败需要有完善的告警和重试机制