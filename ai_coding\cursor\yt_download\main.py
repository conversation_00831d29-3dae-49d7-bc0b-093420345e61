# -*- coding: utf-8 -*-
from fastapi import FastAP<PERSON>, Request
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import yt_dlp
import os
import asyncio
from datetime import datetime, timedelta
import humanize
import json
import re

app = FastAPI()

# 代理设置
PROXY = {
    'http': 'socks5://127.0.0.1:10808',
    'https': 'socks5://127.0.0.1:10808'
}

# 静态文件和模板配置
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 下载目录配置
DOWNLOAD_DIR = "static/downloads"
os.makedirs(DOWNLOAD_DIR, exist_ok=True)

# 存储下载信息的文件
DOWNLOADS_INFO_FILE = "static/downloads/downloads.json"

def load_downloads():
    if os.path.exists(DOWNLOADS_INFO_FILE):
        with open(DOWNLOADS_INFO_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def save_downloads(downloads):
    with open(DOWNLOADS_INFO_FILE, 'w', encoding='utf-8') as f:
        json.dump(downloads, f, ensure_ascii=False, indent=2)

def clean_filename(filename):
    # 移除表情符号和其他特殊字符
    filename = filename.encode('ascii', 'ignore').decode('ascii')
    # 只保留字母、数字、空格和一些基本标点
    filename = re.sub(r'[^\w\s-]', '', filename)
    # 替换空格为下划线
    filename = re.sub(r'\s+', '_', filename.strip())
    return filename if filename else f"video_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

@app.get("/")
async def home(request: Request):
    downloads = load_downloads()
    return templates.TemplateResponse("index.html", {"request": request, "downloads": downloads})

@app.post("/download")
async def download_video(request: Request):
    data = await request.json()
    url = data.get("url")
    
    if not url:
        return JSONResponse({"error": "URL不能为空"}, status_code=400)
    
    ydl_opts = {
        'format': 'best[ext=mp4]/best',  # 简化格式选择
        'outtmpl': '%(title)s.%(ext)s',
        'quiet': True,
        'proxy': PROXY['http'],
        'socket_timeout': 30,
        'nocheckcertificate': True,
        'ignoreerrors': True,
        'no_warnings': True,
        'restrictfilenames': True,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # 首先获取视频信息
            info = ydl.extract_info(url, download=False)
            if not info:
                return JSONResponse({"error": "无法获取视频信息"}, status_code=500)
            
            # 安全地获取视频信息
            title = str(info.get("title", "未知标题"))
            duration = int(info.get("duration", 0))
            uploader = str(info.get("uploader", "未知作者"))
            description = str(info.get("description", "暂无描述"))
            
            # 生成安全的文件名
            safe_title = clean_filename(title)
            output_file = f"{safe_title}.mp4"
            full_path = os.path.join(DOWNLOAD_DIR, output_file)
            
            # 更新下载选项
            ydl_opts.update({
                'outtmpl': full_path,
            })
            
            # 重新初始化下载器并下载视频
            with yt_dlp.YoutubeDL(ydl_opts) as downloader:
                try:
                    downloader.download([url])
                except Exception as e:
                    print(f"Download error: {str(e)}")
                    return JSONResponse({"error": f"下载视频时出错: {str(e)}"}, status_code=500)
            
            # 验证文件是否成功下载
            if not os.path.exists(full_path):
                return JSONResponse({"error": "文件下载失败"}, status_code=500)
            
            # 创建下载信息
            duration_td = timedelta(seconds=duration)
            download_info = {
                "title": title,
                "duration": str(duration_td),
                "author": uploader,
                "description": description[:200] + "..." if len(description) > 200 else description,
                "file_path": os.path.join("static/downloads", output_file),
                "file_size": humanize.naturalsize(os.path.getsize(full_path)),
                "download_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 保存下载记录
            downloads = load_downloads()
            downloads.append(download_info)
            save_downloads(downloads)
            
            return JSONResponse(download_info)
            
    except Exception as e:
        print(f"Error in processing: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return JSONResponse({"error": f"处理视频时出错: {str(e)}"}, status_code=500)

@app.get("/downloads")
async def get_downloads():
    return JSONResponse(load_downloads()) 