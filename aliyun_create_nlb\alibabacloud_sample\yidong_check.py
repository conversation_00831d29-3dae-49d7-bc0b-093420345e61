#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
import requests
import time
import logging
import hmac
import hashlib
import base64
import urllib.parse

'''
## nohup python /root/ajie/domain_monitor.py &
钉钉机器人告警
'''

domains = [
    # 'http://45.91.226.12/123.txt', ##x61
    'http://192.197.113.25/123.txt', ##x62
    # 'http://2.59.155.81/123.txt',    ##x63
    # 'http://192.197.113.30/123.txt', ##x64
    # 'http://2.59.155.52/123.txt',    ##x65
    # 'http://2.59.155.17/123.txt',    ##x66

    # 'http://2.59.155.92/123.txt',   ##x111
    'http://192.197.113.156/123.txt', ##X112
    # 'http://2.59.155.82/123.txt',     ##x113
    'http://193.239.150.36/123.txt',  ##x114
    # 'http://2.59.155.99/123.txt',     ##x115
    # 'http://2.59.155.100/123.txt',     ##x116

    # 'http://45.115.124.162/123.txt',  ####x131
    'http://45.115.124.163/123.txt',
    'http://45.115.124.164/123.txt',
    'http://45.115.124.165/123.txt',
    'http://45.115.124.166/123.txt',
    'http://192.250.241.2/123.txt',
    'http://192.250.241.3/123.txt',
    # 'http://192.250.241.4/123.txt',
    'http://192.250.241.5/123.txt',
    'http://192.250.241.6/123.txt',
    'http://192.250.241.7/123.txt',
    # 'http://192.250.241.8/123.txt',
    'http://192.250.241.9/123.txt',
    'http://192.250.241.10/123.txt',
    'http://192.250.241.11/123.txt',
    'http://192.250.241.12/123.txt',
    'http://192.250.241.13/123.txt',
    'http://192.250.241.14/123.txt',
    'http://192.250.241.15/123.txt',
    'http://192.250.241.16/123.txt',
    'http://192.250.241.17/123.txt',
    'http://192.250.241.18/123.txt',
    'http://192.250.241.19/123.txt',
    'http://192.250.241.20/123.txt',
    'http://192.250.241.21/123.txt',
    'http://192.250.241.22/123.txt',
    'http://192.250.241.23/123.txt',
    # 'http://192.250.241.24/123.txt',
    'http://192.250.241.25/123.txt',
    'http://192.250.241.26/123.txt',
    'http://192.250.241.27/123.txt',
    'http://192.250.241.28/123.txt',
    'http://192.250.241.29/123.txt',
    # 'http://192.250.241.30/123.txt',
    'http://192.250.241.31/123.txt',
    'http://192.250.241.32/123.txt',
    'http://192.250.241.33/123.txt',
    'http://192.250.241.34/123.txt',
    'http://192.250.241.35/123.txt',
    'http://192.250.241.36/123.txt',
    'http://192.250.241.37/123.txt',
    'http://192.250.241.38/123.txt',
    'http://192.250.241.39/123.txt',
    'http://192.250.241.40/123.txt',
    'http://192.250.241.41/123.txt',
    'http://192.250.241.42/123.txt',
    'http://192.250.241.43/123.txt',
    # 'http://192.250.241.44/123.txt',
    'http://192.250.241.45/123.txt',
    'http://192.250.241.46/123.txt',
    'http://192.250.241.47/123.txt',
    'http://192.250.241.48/123.txt',
    'http://192.250.241.49/123.txt',
    # 'http://192.250.241.50/123.txt',
    'http://192.250.241.51/123.txt',
    'http://192.250.241.52/123.txt',
    'http://192.250.241.53/123.txt',
    # 'http://192.250.241.54/123.txt',
    'http://192.250.241.55/123.txt',
    'http://192.250.241.56/123.txt',
    'http://192.250.241.57/123.txt',
    'http://192.250.241.58/123.txt',
    'http://192.250.241.59/123.txt',
    'http://192.250.241.60/123.txt',
    'http://192.250.241.61/123.txt',
    'http://192.250.241.62/123.txt',
    'http://192.250.241.63/123.txt',
    'http://192.250.241.64/123.txt',
    # 'http://192.250.241.65/123.txt',
    'http://192.250.241.66/123.txt',
    'http://192.250.241.67/123.txt',
    'http://192.250.241.68/123.txt',
    'http://192.250.241.69/123.txt',
    'http://192.250.241.70/123.txt',
    'http://192.250.241.71/123.txt',
    'http://192.250.241.72/123.txt',
    'http://192.250.241.73/123.txt',
    'http://192.250.241.74/123.txt',
    'http://192.250.241.75/123.txt',
    'http://192.250.241.76/123.txt',
    # 'http://192.250.241.77/123.txt',
    # 'http://192.250.241.78/123.txt',
    'http://192.250.241.79/123.txt',
    'http://192.250.241.80/123.txt',
    'http://192.250.241.81/123.txt',
    'http://192.250.241.82/123.txt',
    'http://192.250.241.83/123.txt',
    'http://192.250.241.84/123.txt',
    'http://192.250.241.85/123.txt',
    # 'http://192.250.241.86/123.txt',
    'http://192.250.241.87/123.txt',
    'http://192.250.241.88/123.txt',
    'http://192.250.241.89/123.txt',
    'http://192.250.241.90/123.txt',
    'http://192.250.241.91/123.txt',
    'http://192.250.241.92/123.txt',
    'http://192.250.241.93/123.txt',
    'http://192.250.241.94/123.txt',
    'http://192.250.241.95/123.txt',
    'http://192.250.241.96/123.txt',
    'http://192.250.241.97/123.txt',
    'http://192.250.241.98/123.txt',
    'http://192.250.241.99/123.txt',
    'http://192.250.241.100/123.txt',
    'http://192.250.241.101/123.txt',
    'http://192.250.241.102/123.txt',
    'http://192.250.241.103/123.txt',
    'http://192.250.241.104/123.txt',
    'http://192.250.241.105/123.txt',
    'http://192.250.241.106/123.txt',
    'http://192.250.241.107/123.txt',
    'http://192.250.241.108/123.txt',
    'http://192.250.241.109/123.txt',
    'http://192.250.241.110/123.txt',
    'http://192.250.241.111/123.txt',
    'http://192.250.241.112/123.txt',
    'http://192.250.241.113/123.txt',
    'http://192.250.241.114/123.txt',
    'http://192.250.241.115/123.txt',
    'http://192.250.241.116/123.txt',
    'http://192.250.241.117/123.txt',
    'http://192.250.241.118/123.txt',
    'http://192.250.241.119/123.txt',
    'http://192.250.241.120/123.txt',
    'http://192.250.241.121/123.txt',
    'http://192.250.241.122/123.txt',
    'http://192.250.241.123/123.txt',
    'http://192.250.241.124/123.txt',
    'http://192.250.241.125/123.txt',
    'http://192.250.241.126/123.txt',
    'http://192.250.241.127/123.txt',
    'http://192.250.241.128/123.txt',
    'http://192.250.241.129/123.txt',
    'http://192.250.241.130/123.txt',
    # 'http://192.250.241.131/123.txt',
    'http://192.250.241.132/123.txt',
    'http://192.250.241.133/123.txt',
    'http://192.250.241.134/123.txt',
    'http://192.250.241.135/123.txt',
    'http://192.250.241.136/123.txt',
    'http://192.250.241.137/123.txt',
    'http://192.250.241.138/123.txt',
    'http://192.250.241.139/123.txt',
    'http://192.250.241.140/123.txt',
    'http://192.250.241.141/123.txt',
    'http://192.250.241.142/123.txt',
    'http://192.250.241.143/123.txt',
    'http://192.250.241.144/123.txt',
    'http://192.250.241.145/123.txt',
    'http://192.250.241.146/123.txt',
    'http://192.250.241.147/123.txt',
    'http://192.250.241.148/123.txt',
    'http://192.250.241.149/123.txt',
    'http://192.250.241.150/123.txt',
    'http://192.250.241.151/123.txt',
    'http://192.250.241.152/123.txt',
    'http://192.250.241.153/123.txt',
    'http://192.250.241.154/123.txt',
    'http://192.250.241.155/123.txt',
    'http://192.250.241.156/123.txt',
    'http://192.250.241.157/123.txt',
    'http://192.250.241.158/123.txt',
    'http://192.250.241.159/123.txt',
    'http://192.250.241.160/123.txt',
    'http://192.250.241.161/123.txt',
    'http://192.250.241.162/123.txt',
    'http://192.250.241.163/123.txt',
    'http://192.250.241.164/123.txt',
    'http://192.250.241.165/123.txt',
    'http://192.250.241.166/123.txt',
    'http://192.250.241.167/123.txt',
    'http://192.250.241.168/123.txt',
    'http://192.250.241.169/123.txt',
    'http://192.250.241.170/123.txt',
    'http://192.250.241.171/123.txt',
    'http://192.250.241.172/123.txt',
    'http://192.250.241.173/123.txt',
    'http://192.250.241.174/123.txt',
    'http://192.250.241.175/123.txt',
    'http://192.250.241.176/123.txt',
    'http://192.250.241.177/123.txt',
    'http://192.250.241.178/123.txt',
    'http://192.250.241.179/123.txt',
    'http://192.250.241.180/123.txt',
    'http://192.250.241.181/123.txt',
    'http://192.250.241.182/123.txt',
    'http://192.250.241.183/123.txt',
    'http://192.250.241.184/123.txt',
    # 'http://192.250.241.185/123.txt',
    # 'http://192.250.241.186/123.txt',
    'http://192.250.241.187/123.txt',
    'http://192.250.241.188/123.txt',
    'http://192.250.241.189/123.txt',
    'http://192.250.241.190/123.txt',
    'http://192.250.241.191/123.txt',
    'http://192.250.241.192/123.txt',
    'http://192.250.241.193/123.txt',
    'http://192.250.241.194/123.txt',
    # 'http://192.250.241.195/123.txt',
    # 'http://192.250.241.196/123.txt',
    # 'http://192.250.241.197/123.txt',
    # 'http://192.250.241.198/123.txt',
    # 'http://192.250.241.199/123.txt',
    # 'http://192.250.241.200/123.txt',
    'http://192.250.241.201/123.txt',
    'http://192.250.241.202/123.txt',
    # 'http://192.250.241.203/123.txt',
    'http://192.250.241.204/123.txt',
    'http://192.250.241.205/123.txt',
    'http://192.250.241.206/123.txt',
    'http://192.250.241.207/123.txt',
    # 'http://192.250.241.208/123.txt',
    'http://192.250.241.209/123.txt',
    # 'http://192.250.241.210/123.txt',
    # 'http://192.250.241.211/123.txt',
    # 'http://192.250.241.212/123.txt',
    # 'http://192.250.241.213/123.txt',
    # 'http://192.250.241.214/123.txt',
    # 'http://192.250.241.215/123.txt',
    # 'http://192.250.241.216/123.txt',
    # 'http://192.250.241.217/123.txt',
    # 'http://192.250.241.218/123.txt',
    'http://192.250.241.219/123.txt',
    # 'http://192.250.241.220/123.txt',
    'http://192.250.241.221/123.txt',
    'http://192.250.241.222/123.txt',
    # 'http://192.250.241.223/123.txt',
    # 'http://192.250.241.224/123.txt',
    # 'http://192.250.241.225/123.txt',
    # 'http://192.250.241.226/123.txt',
    # 'http://192.250.241.227/123.txt',
    # 'http://192.250.241.228/123.txt',
    'http://192.250.241.229/123.txt',
    # 'http://192.250.241.230/123.txt',
    # 'http://192.250.241.231/123.txt',
    # 'http://192.250.241.232/123.txt',
    # 'http://192.250.241.233/123.txt',
    # 'http://192.250.241.234/123.txt',
    # 'http://192.250.241.235/123.txt',
    'http://192.250.241.236/123.txt',
    # 'http://192.250.241.237/123.txt',
    # 'http://192.250.241.238/123.txt',
    'http://192.250.241.239/123.txt',
    'http://192.250.241.240/123.txt',
    'http://192.250.241.241/123.txt',
    'http://192.250.241.242/123.txt',
    'http://192.250.241.243/123.txt',
    'http://192.250.241.244/123.txt',
    'http://192.250.241.245/123.txt',
    'http://192.250.241.246/123.txt',
    'http://192.250.241.247/123.txt',
    'http://192.250.241.248/123.txt',
    # 'http://192.250.241.249/123.txt',
    'http://192.250.241.250/123.txt',
    'http://192.250.241.251/123.txt',
    'http://192.250.241.252/123.txt',
    'http://192.250.241.253/123.txt',
    'http://192.250.241.254/123.txt',

    # 'http://43.249.25.50/123.txt', ###x241
    'http://43.249.25.51/123.txt',
    'http://43.249.25.52/123.txt',
    'http://43.249.25.53/123.txt',
    'http://43.249.25.54/123.txt',

    'http://23.231.166.194/123.txt', ###x251
    'http://23.231.166.195/123.txt',
    'http://23.231.166.196/123.txt',
    'http://23.231.166.197/123.txt',
    'http://23.231.166.198/123.txt',
    'http://23.231.166.199/123.txt',
    'http://23.231.166.200/123.txt',
    'http://23.231.166.201/123.txt',
    'http://23.231.166.202/123.txt',
    'http://23.231.166.203/123.txt',
    'http://23.231.166.204/123.txt',
    'http://23.231.166.205/123.txt',
    'http://23.231.166.206/123.txt',
    'http://23.231.166.207/123.txt',
    'http://23.231.166.208/123.txt',
    'http://23.231.166.209/123.txt',
    'http://23.231.166.210/123.txt',
    # 'http://23.231.166.211/123.txt',
    'http://23.231.166.212/123.txt',
    'http://23.231.166.213/123.txt',
    'http://23.231.166.214/123.txt',
    'http://23.231.166.215/123.txt',
    'http://23.231.166.216/123.txt',
    'http://23.231.166.217/123.txt',
    'http://23.231.166.218/123.txt',
    'http://23.231.166.219/123.txt',
    'http://23.231.166.220/123.txt',
    'http://23.231.166.221/123.txt',
    'http://23.231.166.222/123.txt',
    'http://23.231.166.223/123.txt',
    'http://23.231.166.224/123.txt',
    'http://23.231.166.225/123.txt',
    'http://23.231.166.226/123.txt',
    'http://23.231.166.227/123.txt',
    'http://23.231.166.228/123.txt',
    'http://23.231.166.229/123.txt',
    'http://23.231.166.230/123.txt',
    'http://23.231.166.231/123.txt',
    'http://23.231.166.232/123.txt',
    'http://23.231.166.233/123.txt',
    'http://23.231.166.234/123.txt',
    'http://23.231.166.235/123.txt',
    'http://23.231.166.236/123.txt',
    'http://23.231.166.237/123.txt',
    'http://23.231.166.238/123.txt',
    'http://23.231.166.239/123.txt',
    'http://23.231.166.240/123.txt',
    'http://23.231.166.241/123.txt',
    'http://23.231.166.242/123.txt',
    'http://23.231.166.243/123.txt',
    'http://23.231.166.244/123.txt',
    'http://23.231.166.245/123.txt',
    'http://23.231.166.246/123.txt',
    'http://23.231.166.247/123.txt',
    'http://23.231.166.248/123.txt',
    'http://23.231.166.249/123.txt',
    'http://23.231.166.250/123.txt',
    'http://23.231.166.251/123.txt',
    'http://23.231.166.252/123.txt',
    'http://23.231.166.253/123.txt',
    'http://23.231.166.254/123.txt',

    # 'http://23.231.168.194/123.txt', ###x261
    # 'http://23.231.168.195/123.txt',
    # 'http://23.231.168.196/123.txt',
    # 'http://23.231.168.197/123.txt',
    # 'http://23.231.168.198/123.txt',
    # 'http://23.231.168.199/123.txt',
    # 'http://23.231.168.200/123.txt',
    # 'http://23.231.168.201/123.txt',
    # 'http://23.231.168.202/123.txt',
    # 'http://23.231.168.203/123.txt',
    # 'http://23.231.168.204/123.txt',
    # 'http://23.231.168.205/123.txt',
    # 'http://23.231.168.206/123.txt',
    # 'http://23.231.168.207/123.txt',
    # 'http://23.231.168.208/123.txt',
    # 'http://23.231.168.209/123.txt',
    # 'http://23.231.168.210/123.txt',
    # 'http://23.231.168.211/123.txt',
    # 'http://23.231.168.212/123.txt',
    # 'http://23.231.168.213/123.txt',
    # 'http://23.231.168.214/123.txt',
    # 'http://23.231.168.215/123.txt',
    # 'http://23.231.168.216/123.txt',
    # 'http://23.231.168.217/123.txt',
    # 'http://23.231.168.218/123.txt',
    # 'http://23.231.168.219/123.txt',
    # 'http://23.231.168.220/123.txt',
    # 'http://23.231.168.221/123.txt',
    # 'http://23.231.168.222/123.txt',
    # 'http://23.231.168.223/123.txt',
    # 'http://23.231.168.224/123.txt',
    # 'http://23.231.168.225/123.txt',
    # 'http://23.231.168.226/123.txt',
    # 'http://23.231.168.227/123.txt',
    # 'http://23.231.168.228/123.txt',
    # 'http://23.231.168.229/123.txt',
    # 'http://23.231.168.230/123.txt',
    # 'http://23.231.168.231/123.txt',
    # 'http://23.231.168.232/123.txt',
    # 'http://23.231.168.233/123.txt',
    # 'http://23.231.168.234/123.txt',
    # 'http://23.231.168.235/123.txt',
    # 'http://23.231.168.236/123.txt',
    # 'http://23.231.168.237/123.txt',
    # 'http://23.231.168.238/123.txt',
    # 'http://23.231.168.239/123.txt',
    # 'http://23.231.168.240/123.txt',
    # 'http://23.231.168.241/123.txt',
    # 'http://23.231.168.242/123.txt',
    # 'http://23.231.168.243/123.txt',
    # 'http://23.231.168.244/123.txt',
    # 'http://23.231.168.245/123.txt',
    # 'http://23.231.168.246/123.txt',
    # 'http://23.231.168.247/123.txt',
    # 'http://23.231.168.248/123.txt',
    # 'http://23.231.168.249/123.txt',
    # 'http://23.231.168.250/123.txt',
    # 'http://23.231.168.251/123.txt',
    # 'http://23.231.168.252/123.txt',
    # 'http://23.231.168.253/123.txt',
    # 'http://23.231.168.254/123.txt',

    # 'http://107.163.134.194/123.txt', #x271
    # 'http://107.163.134.195/123.txt',
    # 'http://107.163.134.196/123.txt',
    # 'http://107.163.134.197/123.txt',
    # 'http://107.163.134.198/123.txt',
    # 'http://107.163.134.199/123.txt',
    # 'http://107.163.134.200/123.txt',
    # 'http://107.163.134.201/123.txt',
    # 'http://107.163.134.202/123.txt',
    # 'http://107.163.134.203/123.txt',
    # 'http://107.163.134.204/123.txt',
    # 'http://107.163.134.205/123.txt',
    # 'http://107.163.134.206/123.txt',
    # 'http://107.163.134.207/123.txt',
    # 'http://107.163.134.208/123.txt',
    # 'http://107.163.134.209/123.txt',
    # 'http://107.163.134.210/123.txt',
    # 'http://107.163.134.211/123.txt',
    # 'http://107.163.134.212/123.txt',
    # 'http://107.163.134.213/123.txt',
    # 'http://107.163.134.214/123.txt',
    # 'http://107.163.134.215/123.txt',
    # 'http://107.163.134.216/123.txt',
    # 'http://107.163.134.217/123.txt',
    # 'http://107.163.134.218/123.txt',
    # 'http://107.163.134.219/123.txt',
    # 'http://107.163.134.220/123.txt',
    # 'http://107.163.134.221/123.txt',
    # 'http://107.163.134.222/123.txt',
    # 'http://107.163.134.223/123.txt',
    # 'http://107.163.134.224/123.txt',
    # 'http://107.163.134.225/123.txt',
    # 'http://107.163.134.226/123.txt',
    # 'http://107.163.134.227/123.txt',
    # 'http://107.163.134.228/123.txt',
    # 'http://107.163.134.229/123.txt',
    # 'http://107.163.134.230/123.txt',
    # 'http://107.163.134.231/123.txt',
    # 'http://107.163.134.232/123.txt',
    # 'http://107.163.134.233/123.txt',
    # 'http://107.163.134.234/123.txt',
    # 'http://107.163.134.235/123.txt',
    # 'http://107.163.134.236/123.txt',
    # 'http://107.163.134.237/123.txt',
    # 'http://107.163.134.238/123.txt',
    # 'http://107.163.134.239/123.txt',
    # 'http://107.163.134.240/123.txt',
    # 'http://107.163.134.241/123.txt',
    # 'http://107.163.134.242/123.txt',
    # 'http://107.163.134.243/123.txt',
    # 'http://107.163.134.244/123.txt',
    # 'http://107.163.134.245/123.txt',
    # 'http://107.163.134.246/123.txt',
    # 'http://107.163.134.247/123.txt',
    # 'http://107.163.134.248/123.txt',
    # 'http://107.163.134.249/123.txt',
    # 'http://107.163.134.250/123.txt',
    # 'http://107.163.134.251/123.txt',
    # 'http://107.163.134.252/123.txt',
    # 'http://107.163.134.253/123.txt',
    # 'http://107.163.134.254/123.txt',

    'http://107.163.136.194/123.txt', ##x281
    'http://107.163.136.195/123.txt', ##x282
    'http://107.163.136.196/123.txt', ##x283
    'http://107.163.136.197/123.txt', ##x284
    'http://107.163.136.198/123.txt', ##x285
    'http://107.163.136.199/123.txt',
    'http://107.163.136.200/123.txt',
    'http://107.163.136.201/123.txt',
    'http://107.163.136.202/123.txt',
    'http://107.163.136.203/123.txt',
    'http://107.163.136.204/123.txt',
    'http://107.163.136.205/123.txt',
    'http://107.163.136.206/123.txt',
    'http://107.163.136.207/123.txt',
    'http://107.163.136.208/123.txt',
    'http://107.163.136.209/123.txt',
    'http://107.163.136.210/123.txt',
    'http://107.163.136.211/123.txt',
    'http://107.163.136.212/123.txt',
    'http://107.163.136.213/123.txt',
    'http://107.163.136.214/123.txt',
    'http://107.163.136.215/123.txt',
    'http://107.163.136.216/123.txt',
    'http://107.163.136.217/123.txt',
    'http://107.163.136.218/123.txt',
    'http://107.163.136.219/123.txt',
    'http://107.163.136.220/123.txt',
    'http://107.163.136.221/123.txt',
    'http://107.163.136.222/123.txt',
    'http://107.163.136.223/123.txt',
    'http://107.163.136.224/123.txt',
    'http://107.163.136.225/123.txt',
    'http://107.163.136.226/123.txt',
    'http://107.163.136.227/123.txt',
    'http://107.163.136.228/123.txt',
    'http://107.163.136.229/123.txt',
    'http://107.163.136.230/123.txt',
    'http://107.163.136.231/123.txt',
    'http://107.163.136.232/123.txt',
    'http://107.163.136.233/123.txt',
    'http://107.163.136.234/123.txt',
    'http://107.163.136.235/123.txt',
    'http://107.163.136.236/123.txt',
    'http://107.163.136.237/123.txt',
    'http://107.163.136.238/123.txt',
    'http://107.163.136.239/123.txt',
    'http://107.163.136.240/123.txt',
    'http://107.163.136.241/123.txt',
    'http://107.163.136.242/123.txt',
    'http://107.163.136.243/123.txt',
    'http://107.163.136.244/123.txt',
    'http://107.163.136.245/123.txt',
    'http://107.163.136.246/123.txt',
    'http://107.163.136.247/123.txt',
    'http://107.163.136.248/123.txt',
    'http://107.163.136.249/123.txt',
    # 'http://107.163.136.250/123.txt',
    # 'http://107.163.136.251/123.txt',
    'http://107.163.136.252/123.txt',
    'http://107.163.136.253/123.txt',
    'http://107.163.136.254/123.txt',

    # 'http://211.174.59.136/123.txt',   ####H61
    'http://211.174.59.154/123.txt',
    # 'http://211.174.59.221/123.txt',
    'http://211.233.25.170/123.txt',
    'http://211.174.59.152/123.txt',
    'http://211.174.59.222/123.txt',
    'http://211.174.59.223/123.txt',

    # 'http://39.106.57.216:8888/123.txt', ####tongbu

]

# 配置日志输出到文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("monitor.log"),  # 文件日志
        logging.StreamHandler()              # 终端日志
    ]
)
logger = logging.getLogger("yidong_check")  # 可以自定义 Logger 名称


def get_digest():  # 钉钉告警拼接
    # 取毫秒级别时间戳，round(x, n) 取x小数点后n位的结果，默认取整
    timestamp = str(round(time.time() * 1000))
    secret = 'SECcadb06773f5af50923dd2f63f5f61aef2aa3cf98da5f2fd03cf834297869ec6c'
    secret_enc = secret.encode('utf-8')  # utf-8编码
    string_to_sign = '{}\n{}'.format(timestamp, secret)  # 字符串格式化拼接
    string_to_sign_enc = string_to_sign.encode('utf-8')  # utf-8编码
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()  # HmacSHA256算法计算签名
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))  # Base64编码后进行urlEncode
    #  返回时间戳和计算好的编码拼接字符串，后面直接拼接到Webhook即可
    return f"&timestamp={timestamp}&sign={sign}"


# 钉钉告警
def warning_bot(message):
    data = {
        "msgtype": "text",
        "text": {
            "content": message
        },
    }
    proxies = {
        'http': 'http://127.0.0.1:10809',
        'https': 'http://127.0.0.1:10809',
    }
    # 机器人链接地址，发post请求 向钉钉机器人传递指令
    webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token' \
                  '=9fb7741f17e86008209eee7a3db93dd32caa9b26369fa6633a10c3d25de2cb8f '
    # 利用requests发送post请求
    req = requests.post(webhook_url + get_digest(), proxies=proxies, json=data)
    print(req.status_code)


def check_domain(domain):  # 检查网站状态
    try:
        response = requests.get(domain, timeout=5)
        response_code = response.status_code
        response_time = response.elapsed.total_seconds()
        return response_code, response_time
    except (requests.exceptions.RequestException, requests.exceptions.HTTPError):
        return None, None


def monitor_domains():
    alert_interval = 1800  # 时间间隔秒, 6时=21600秒
    retry_count = 2  # 尝试次数，秒
    domain_status = {domain: {"last_alert": 0, "fail_count": 0} for domain in domains}  # 使用字典跟踪每个域名的告警状态
    ## http://zs.buyusdt.me/service.php?password=nixcha20ihd&act=service_status&service=x41&status=1
    # 定义一个正则表达式，匹配 http:// 或 https:// 后紧接的任意字母数字组合，直到点号为止
    pattern = r'https?://([a-zA-Z0-9]+)\.'

    while True:
        for domain in domains:
            response_code, response_time = check_domain(domain)
            # 使用 re.search() 查找匹配的部分
            service = re.search(pattern, domain).group(1)

            if response_code != 200:  # 如果网站状态不是200
                domain_status[domain]['fail_count'] += 1
                current_time = int(time.time())  # 1711250765
                elapsed_time = current_time - domain_status[domain]['last_alert']  # 经过的时间
                logging.info(f"{domain} 移动线路第 {domain_status[domain]['fail_count']} 次请求失败啦！！")

                # 经过3小时仍然未恢复，发送告警
                if domain_status[domain]['last_alert'] != 0 and elapsed_time > alert_interval:
                    domain_status[domain]['last_alert'] = current_time  # 重置当前时间
                    logging.warning(f"移动线路未恢复: {domain} ")
                    message = f"移动线路未恢复: {domain} "
                    warning_bot(message)
                    continue

                # 检测失败达到3次，发送告警
                if domain_status[domain]['fail_count'] >= retry_count and domain_status[domain]['last_alert'] <= alert_interval:
                    domain_status[domain]['last_alert'] = current_time  # 重置当前时间
                    logging.warning(f"请检查: {domain} 移动线路!!")
                    message = f"请检查: {domain} 移动线路!!"
                    warning_bot(message)
                    # try:
                    #     requests.get(f"http://zs.buyusdt.me/service.php?password=nixcha20ihd&act=service_status&service={service}&status=2",timeout=8)   # status =1 表示正常 2表示异常
                    # except Exception as e:
                    #     logging.warning(f"发生异常：{e}")

            else:
                if domain_status[domain]['fail_count'] >= retry_count:  # 告警恢复
                    logging.warning(f"移动线路恢复: {domain}")
                    message = f"移动线路恢复: {domain}"
                    warning_bot(message)
                # 网站状态正常，重置计算次数
                domain_status[domain]['last_alert'] = 0
                domain_status[domain]['fail_count'] = 0
                # try:
                #     requests.get(f"http://zs.buyusdt.me/service.php?password=nixcha20ihd&act=service_status&service={service}&status=1", timeout=8)   # status =1 表示正常 2表示异常
                # except Exception as e:
                #     logging.warning(f"发生异常：{e}")
                logging.info(f"域名 {domain}，响应码：{response_code}，响应时间：{response_time} 秒")
            # 休眠一段时间再继续下一轮检测
            time.sleep(0.9)


if __name__ == '__main__':
    monitor_domains()
