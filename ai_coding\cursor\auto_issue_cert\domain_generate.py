#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 读取队列生成证书模块 - 任务二
# 脚本有几个地方要改：auto_fetch_domains、auto_push_cert.sh、acme.sh、
## 需要安装的依赖：pip3.8 install python-telegram-bot

import os
import sys
import subprocess
import redis
import logging
import time
import json
import threading
import telegram
import asyncio

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("domain_generate.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("DomainCert")

# Redis配置
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB = 1
REDIS_PASSWORD = 'BBMFqw8uqw65'

# 队列配置
QUEUE_AUTO_A = 'domains_queue_autoa'  # A组队列
QUEUE_AUTO_B = 'domains_queue_autob'  # B组队列

# 脚本路径
APPLY_CERT_SCRIPT = '/root/ajie/yunwei/auto_apply_cert.sh'  # 证书生成脚本，使用绝对路径

# 证书信息键名前缀（包含任务状态和证书内容）
CERT_INFO_PREFIX = 'cert_info_'    # 证书信息键前缀（合并了原cert_task_前缀）

# 重试配置
MAX_RETRIES = 3           # 最大重试次数
RETRY_INTERVAL = 120      # 重试间隔（秒）
BLPOP_TIMEOUT = 30        # BLPOP超时时间（秒）

# ==========================================================
# Telegram 通知模块
# ==========================================================
BOT_TOKEN = '8075564479:AAECGBBDzaMvVOT0SbvCRNVgtgJn1NVJxwc'
CHAT_ID = '-1002759112051'
#CHAT_ID = '6748130737'

async def send_telegram_notification(message):
    try:
        bot = telegram.Bot(token=BOT_TOKEN)
        # 使用纯文本格式，不指定parse_mode
        await bot.send_message(chat_id=CHAT_ID, text=message)
        logger.debug("Telegram通知发送成功")
    except Exception as e:
        logger.error(f"Telegram 通知发送失败: {e}")

def notify(message):
    """同步函数的包装器，方便在非异步代码中调用"""
    try:
        asyncio.run(send_telegram_notification(message))
    except Exception as e:
        logger.error(f"执行Telegram通知时出错: {e}")
# ==========================================================

class CertificateGenerator:
    """证书生成器类，处理证书生成任务"""
    
    def __init__(self):
        """初始化证书生成器"""
        try:
            self.redis_client = redis.Redis(
                host=REDIS_HOST,
                port=REDIS_PORT,
                db=REDIS_DB,
                password=REDIS_PASSWORD,
                decode_responses=True
            )
            # 测试Redis连接
            self.redis_client.ping()
            logger.debug("Redis连接成功")
        except redis.RedisError as e:
            logger.error(f"Redis连接失败: {str(e)}")
            raise
        
        logger.debug("证书生成器初始化完成")
    
    def _read_output_stream(self, stream, is_error, output_list, log_prefix):
        """
        在独立线程中读取输出流并记录日志
        
        参数:
        - stream: 输出流对象 (process.stdout 或 process.stderr)
        - is_error: 是否是错误输出流
        - output_list: 用于存储输出行的列表
        - log_prefix: 日志前缀
        """
        try:
            for line in iter(stream.readline, ''):
                if not line:
                    break
                    
                line = line.strip()
                if line:
                    # 根据输出类型使用不同的日志级别
                    if is_error:
                        logger.error(f"{log_prefix}: {line}")
                    else:
                        logger.info(f"{log_prefix}: {line}")
                    
                    # 将输出添加到列表中
                    output_list.append(line)
        except Exception as e:
            logger.error(f"读取输出流时出错: {str(e)}")
    
    def update_task_status(self, site_id, status, message=None):
        """
        更新任务状态
        
        参数:
        - site_id: 站点ID
        - status: 状态 (processing, success, failed)
        - message: 附加消息
        """
        try:
            key = f"{CERT_INFO_PREFIX}{site_id}"
            data = {
                "status": status,
                "timestamp": time.time(),
                "message": message or ""
            }
            # 检查是否已存在证书信息，避免覆盖
            if self.redis_client.exists(key):
                # 只更新状态相关字段，不影响其他字段
                self.redis_client.hmset(key, data)
                logger.debug(f"站点 {site_id} 任务状态更新为 {status}（保留现有证书信息）")
            else:
                # 键不存在，直接设置
                self.redis_client.hmset(key, data)
                logger.debug(f"站点 {site_id} 任务状态更新为 {status}（新建记录）")
            return True
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")
            return False
    
    def save_cert_info(self, site_id, cert_path, key_path):
        """
        保存证书信息
        
        参数:
        - site_id: 站点ID
        - cert_path: 证书路径
        - key_path: 私钥路径
        """
        try:
            key = f"{CERT_INFO_PREFIX}{site_id}"
            
            # 读取证书文件内容
            cert_content = ""
            key_content = ""
            
            try:
                with open(cert_path, 'r') as f:
                    cert_content = f.read()
            except Exception as e:
                logger.error(f"读取证书文件失败: {str(e)}")
            
            try:
                with open(key_path, 'r') as f:
                    key_content = f.read()
            except Exception as e:
                logger.error(f"读取私钥文件失败: {str(e)}")
            
            # 准备证书信息数据
            data = {
                "cert_path": cert_path,
                "key_path": key_path,
                "cert_content": cert_content,
                "key_content": key_content,
                "created_at": time.time()
            }
            
            # 检查是否已存在状态信息，如果存在则保留
            if self.redis_client.exists(key):
                # 获取现有状态信息
                status_fields = ["status", "timestamp", "message"]
                status_data = self.redis_client.hmget(key, status_fields)
                
                # 如果状态字段存在，则保留
                for i, field in enumerate(status_fields):
                    if status_data[i]:
                        data[field] = status_data[i]
            
            # 使用hmset更新所有字段
            self.redis_client.hmset(key, data)
            logger.debug(f"站点 {site_id} 证书信息已保存（保留任务状态）")
            return True
        except Exception as e:
            logger.error(f"保存证书信息失败: {str(e)}")
            return False
    
    def check_same_site_in_queue(self, queue_name, site_num):
        """
        检查队列中是否有相同站点编号的任务
        
        参数:
        - queue_name: 队列名称
        - site_num: 站点编号 (如 'autoa3' 中的 '3')
        
        返回:
        - 如果找到相同编号的任务，返回True，否则返回False
        """
        try:
            # 获取队列中的所有任务
            queue_length = self.redis_client.llen(queue_name)
            if queue_length == 0:
                return False
            
            # 检查队列中的每个任务
            for i in range(queue_length):
                # 使用LINDEX获取任务但不移除
                task_data = self.redis_client.lindex(queue_name, i)
                if not task_data:
                    continue
                
                try:
                    task = json.loads(task_data)
                    if 'site_id' in task:
                        # 提取站点编号
                        current_site_id = task['site_id']
                        current_site_num = ''.join(filter(str.isdigit, current_site_id))
                        
                        if current_site_num == site_num:
                            logger.info(f"在队列 {queue_name} 中找到相同编号的站点任务: {current_site_id}")
                            return True
                except json.JSONDecodeError:
                    logger.warning(f"解析队列任务数据失败: {task_data}")
                except Exception as e:
                    logger.warning(f"检查站点编号时出错: {str(e)}")
            
            return False
        except Exception as e:
            logger.error(f"检查队列中的站点任务时出错: {str(e)}")
            return False
            
    def process_certificate_task(self, site_id, domains):
        """
        处理证书生成任务
        
        参数:
        - site_id: 站点ID
        - domains: 域名列表
        
        返回:
        - success: 是否成功
        - message: 消息
        """
        try:
            logger.debug(f"开始处理站点 {site_id} 的证书生成任务")
            
            # 更新任务状态为处理中
            self.update_task_status(site_id, "processing", "开始处理证书生成任务")
            
            # 检查domains类型并适当处理
            domains_count = 0
            if isinstance(domains, list):
                domains_count = len(domains)
            elif isinstance(domains, str):
                domains_count = len(domains.split(','))
            
            # 发送Telegram通知
            notify(f"开始为站点 {site_id} 生成证书\n包含 {domains_count} 个域名")
            
            # 使用本地变量存储脚本路径
            script_path = APPLY_CERT_SCRIPT
            
            # 检查脚本是否存在
            if not os.path.exists(script_path):
                error_msg = f"证书生成脚本不存在: {script_path}"
                logger.error(error_msg)
                self.update_task_status(site_id, "failed", error_msg)
                return False, error_msg
                
            # 确保脚本具有执行权限
            if not os.access(script_path, os.X_OK):
                logger.warning(f"脚本 {script_path} 没有执行权限，尝试添加执行权限")
                try:
                    os.chmod(script_path, 0o755)
                    logger.info(f"已成功为脚本 {script_path} 添加执行权限")
                except Exception as e:
                    error_msg = f"无法为脚本添加执行权限: {str(e)}"
                    logger.error(error_msg)
                    self.update_task_status(site_id, "failed", error_msg)
                    return False, error_msg
            
            # 调用证书生成脚本
            start_time = time.time()
            cmd = f"{script_path} {site_id}"
            logger.info(f"执行证书生成命令: {cmd}")
            
            # 使用Popen替代run，以便实时捕获和记录输出
            process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=dict(os.environ, PYTHONUNBUFFERED="1")  # 禁用Python子进程的输出缓冲
            )
            
            # 收集所有输出
            all_output = []
            error_output = []
            
            # 创建线程读取stdout和stderr
            stdout_thread = threading.Thread(
                target=self._read_output_stream, 
                args=(process.stdout, False, all_output, "证书脚本输出")
            )
            stderr_thread = threading.Thread(
                target=self._read_output_stream, 
                args=(process.stderr, True, error_output, "证书脚本错误")
            )
            
            # 设置为守护线程，这样即使主线程退出，这些线程也会终止
            stdout_thread.daemon = True
            stderr_thread.daemon = True
            
            # 启动线程
            stdout_thread.start()
            stderr_thread.start()
            
            # 等待进程完成
            return_code = process.wait()
            
            # 等待输出读取线程完成（给予合理的超时时间）
            stdout_thread.join(timeout=5)
            stderr_thread.join(timeout=5)
            
            # 创建一个类似subprocess.run的结果对象，以保持代码结构一致性
            result = subprocess.CompletedProcess(
                args=cmd,
                returncode=return_code,
                stdout='\n'.join(all_output),
                stderr='\n'.join(error_output)
            )
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 检查执行结果
            if result.returncode == 0:
                # 成功处理
                logger.info(f"站点 {site_id} 证书生成成功，耗时 {process_time:.2f} 秒")
                
                # 更新任务状态
                self.update_task_status(site_id, "success", f"证书生成成功，耗时 {process_time:.2f} 秒")
                
                # 保存证书信息
                cert_path = f"/root/.acme.sh/{site_id}/cert.pem"
                key_path = f"/root/.acme.sh/{site_id}/privkey.pem"
                self.save_cert_info(site_id, cert_path, key_path)
                
                # 发送Telegram成功通知
                success_msg = f"站点 {site_id} 证书生成成功\n" \
                              f"耗时: {process_time:.2f} 秒\n" \
                              f"域名数: {domains_count}"
                notify(success_msg)
                
                # 调用success接口（暂不实现）
                # self.report_success(site_id, cert_path, key_path)
                
                return True, "证书生成成功"
            else:
                # 失败处理
                error_msg = result.stderr
                logger.error(f"站点 {site_id} 证书生成失败: {error_msg}")
                
                # 更新任务状态
                self.update_task_status(site_id, "failed", f"证书生成失败: {error_msg}")
                
                # 发送Telegram失败通知
                escaped_error = error_msg[:200] + "..." if len(error_msg) > 200 else error_msg
                failure_msg = f"站点 {site_id} 证书生成失败\n" \
                              f"耗时: {process_time:.2f} 秒\n" \
                              f"域名数: {domains_count}\n" \
                              f"错误信息: {escaped_error}"
                notify(failure_msg)
                
                return False, f"证书生成失败: {error_msg}"
                
        except Exception as e:
            logger.error(f"处理证书生成任务时出错: {str(e)}")
            self.update_task_status(site_id, "failed", f"处理出错: {str(e)}")
            # 发送异常错误通知
            notify(f"站点 {site_id} 处理出错\n错误: {str(e)}")
            return False, f"处理出错: {str(e)}"


class QueueWorker(threading.Thread):
    """队列处理线程类"""
    
    def __init__(self, queue_name, cert_generator):
        """
        初始化队列处理线程
        
        参数:
        - queue_name: 队列名称
        - cert_generator: 证书生成器实例
        """
        threading.Thread.__init__(self)
        self.queue_name = queue_name
        self.cert_generator = cert_generator
        self.running = True
        logger.debug(f"队列处理线程 {queue_name} 初始化完成")
    
    def run(self):
        """线程运行方法"""
        logger.debug(f"队列处理线程 {self.queue_name} 开始运行")
        
        while self.running:
            try:
                # 从队列左侧阻塞读取一条记录
                result = self.cert_generator.redis_client.blpop(self.queue_name, BLPOP_TIMEOUT)
                
                if result is None:
                    # 队列为空，继续等待
                    continue
                
                # 解析队列数据
                _, task_data = result
                
                try:
                    # 解析JSON数据
                    task = json.loads(task_data)
                    site_id = task.get('site_id')
                    domains = task.get('domains')
                    
                    if not site_id or not domains:
                        logger.warning(f"任务数据缺少必要字段: {task_data}")
                        continue
                    
                    logger.info(f"从队列 {self.queue_name} 获取任务: 站点 {site_id}")
                    
                    # 处理证书生成任务
                    success, message = self.cert_generator.process_certificate_task(site_id, domains)
                    
                    if success:
                        # 成功处理，继续下一个任务
                        logger.debug(f"站点 {site_id} 任务处理成功: {message}")
                        
                        # 删除重试计数键，确保下次失败时有完整的重试机会
                        retry_key = f"cert_retry_{site_id}"
                        # Redis的delete操作对不存在的键是安全的，即使是首次成功（没有重试记录）也不会出错
                        self.cert_generator.redis_client.delete(retry_key)
                        logger.debug(f"站点 {site_id} 的重试计数已重置")
                        
                        continue
                    else:
                        # 失败处理
                        logger.warning(f"站点 {site_id} 任务处理失败: {message}")
                        
                        # 提取站点编号
                        site_num = ''.join(filter(str.isdigit, site_id))
                        
                        # 检查队列中是否有相同编号的任务
                        has_same_site = self.cert_generator.check_same_site_in_queue(self.queue_name, site_num)
                        
                        if has_same_site:
                            # 有相同编号的任务，直接处理下一个
                            logger.info(f"队列中存在相同编号的站点任务，跳过重试")
                            continue
                        else:
                            # 没有相同编号的任务，进行重试
                            # 获取当前重试次数
                            retry_key = f"cert_retry_{site_id}"
                            retry_count = self.cert_generator.redis_client.get(retry_key)
                            retry_count = int(retry_count) if retry_count else 0
                            
                            if retry_count < MAX_RETRIES:
                                # 增加重试次数
                                retry_count += 1
                                self.cert_generator.redis_client.setex(retry_key, 3600, retry_count)  # 设置1小时key值过期
                                
                                # 发送重试通知
                                retry_msg = f"站点 {site_id} 处理失败\n" \
                                           f"将在 {RETRY_INTERVAL} 秒后进行第 {retry_count} 次重试"
                                notify(retry_msg)
                                
                                # 等待一段时间后重试
                                logger.info(f"站点 {site_id} 将在 {RETRY_INTERVAL} 秒后进行第 {retry_count} 次重试")
                                time.sleep(RETRY_INTERVAL)
                                
                                # 重新将任务加入队列（左侧，保证优先处理）
                                self.cert_generator.redis_client.lpush(self.queue_name, task_data)
                                logger.debug(f"站点 {site_id} 已重新加入队列左侧，将优先重试")
                            else:
                                # 超过最大重试次数，放弃任务
                                logger.error(f"站点 {site_id} 已达到最大重试次数 {MAX_RETRIES}，放弃处理")
                                
                                # 发送达到最大重试次数通知
                                max_retry_msg = f"站点 {site_id} 已达到最大重试次数\n" \
                                              f"最大重试次数: {MAX_RETRIES}\n" \
                                              f"需要人工确认处理"
                                notify(max_retry_msg)
                                
                                # 删除重试计数器
                                self.cert_generator.redis_client.delete(retry_key)
                    
                except json.JSONDecodeError:
                    logger.error(f"解析队列任务数据失败: {task_data}")
                except Exception as e:
                    logger.error(f"处理队列任务时出错: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                
            except redis.RedisError as e:
                logger.error(f"Redis操作错误: {str(e)}")
                time.sleep(5)  # 发生错误时等待5秒再重试
            except Exception as e:
                logger.error(f"队列处理线程运行时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                time.sleep(5)  # 发生错误时等待5秒再重试
        
        logger.debug(f"队列处理线程 {self.queue_name} 已停止")
    
    def stop(self):
        """停止线程"""
        self.running = False
        logger.info(f"队列处理线程 {self.queue_name} 收到停止信号")


def main():
    """主函数"""
    try:
        logger.info("证书生成服务启动...")
        
        # 初始化证书生成器
        cert_generator = CertificateGenerator()
        
        # 创建队列处理线程
        worker_a = QueueWorker(QUEUE_AUTO_A, cert_generator)
        worker_b = QueueWorker(QUEUE_AUTO_B, cert_generator)
        
        # 启动线程
        worker_a.start()
        worker_b.start()
        
        logger.debug("所有队列处理线程已启动")
        
        # 主线程等待，可以通过Ctrl+C中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，准备停止服务...")
            
            # 停止线程
            worker_a.stop()
            worker_b.stop()
            
            # 等待线程结束
            worker_a.join()
            worker_b.join()
            
            logger.info("所有线程已停止，服务退出")
    
    except Exception as e:
        logger.error(f"服务运行时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main() 