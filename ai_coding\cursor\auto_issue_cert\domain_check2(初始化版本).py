#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 域名接收与处理模块 - 任务一
# 部署新环境几个地方需要注意：auto_domains_check.sh、auto_bt_api.py、domain_submit_server.py、auto_apply_cert.sh

import os
import sys
import tempfile
import subprocess
import redis
import requests
import logging
import time
import json

# 证书自动化申请系统配置文件
# 宝塔主机
SSH_BT_HOST = '**************'  # 小舅1:**************  小舅2:************
# Redis配置
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB = 1
REDIS_PASSWORD = 'BBMFqw8uqw65'
# 队列配置
REDIS_DOMAIN_HASH = 'domains_add_history'  # 存储站点ID和域名的Hash
REDIS_CERT_QUEUE = 'domains_cert_queue'    # 存储待处理站点ID的List
REDIS_CURRENT_SITE_KEY = 'current_site_id'  # 存储当前使用的站点ID
# 新增Redis键名
REDIS_DOMAINS_CERT = 'domains_cert_sort_set'     # 排序集合，存储待处理域名和失败计数
REDIS_DOMAINS_EXISTS = 'domains_exists_set'  # 集合，存储已存在于nginx的域名
REDIS_DOMAINS_REMOVE = 'domains_remove_set'  # 集合，存储解析失败次数达到上限的域名
REDIS_DOMAINS_KEY = 'get_domains'  # 存储用户提交的域名列表
# 域名配置
MAX_DOMAINS_PER_SITE = 100  # 每个站点最大域名数量
MAX_FAILURE_COUNT = 60     # 域名解析失败的最大次数
# NGINX_CONF_PATH = '/www/cloud_waf/nginx/conf.d/vhost/'  # Nginx配置路径
NGINX_CONF_PATH = '/tmp/nginx/'  # xiaojiu2 bt Nginx配置路径
# NGINX_CONF_PATH = '/www/server/panel/vhost/nginx/'  # xiaojiu2 bt Nginx配置路径
# 脚本配置
DOMAINS_CHECK_SCRIPT = '/root/ajie/yunwei/auto_domains_check.sh'  # 域名检测脚本路径
DOMAINS_GET_SCRIPT = '/root/ajie/yunwei/auto_get_bt_domains.sh' # 查询nginx中conf文件域名
ISSUE_CERT_SCRIPT = '/path/to/issue_cert.sh'        # 证书申请脚本路径
# API配置 - 不再使用
# DOMAIN_API_URL = 'http://*************:8888/222.txt'  # 域名获取API地址 

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("domain_check.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("DomainProcessor")

class DomainProcessor:
    def __init__(self):
        """初始化域名处理器"""
        self.redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        # 设置当前使用的站点组，默认为a组
        self.current_group = 'a'
        logger.info("域名处理器初始化完成，当前使用站点组: %s", self.current_group)
        # 执行队列状态检查
        self.check_queue_status()

    def check_queue_status(self):
        """检查各队列状态，用于诊断"""
        try:
            domains_queue_autoa_len = self.redis_client.llen('domains_queue_autoa')
            domains_queue_autob_len = self.redis_client.llen('domains_queue_autob')
            domains_cert_queue_len = self.redis_client.llen(REDIS_CERT_QUEUE)
            
            logger.info(f"队列状态检查 - domains_queue_autoa: {domains_queue_autoa_len}项")
            logger.info(f"队列状态检查 - domains_queue_autob: {domains_queue_autob_len}项")
            logger.info(f"队列状态检查 - {REDIS_CERT_QUEUE}: {domains_cert_queue_len}项")
            
            # 检查使用的队列名称
            logger.info(f"当前配置的REDIS_CERT_QUEUE值: {REDIS_CERT_QUEUE}")
        except Exception as e:
            logger.error(f"检查队列状态出错: {str(e)}")

    def fetch_domains(self):
        """从Redis获取域名列表"""
        try:
            # 从Redis获取域名列表
            domains = self.redis_client.get(REDIS_DOMAINS_KEY)
            if domains:
                logger.info(f"成功从Redis获取域名: {domains}")
                # 获取后清除Redis中的数据，避免重复处理
                self.redis_client.delete(REDIS_DOMAINS_KEY)
                logger.info("已清除Redis中的域名数据，避免重复处理")
                return domains
            else:
                logger.info("Redis中没有待处理的域名")
                return None
        except Exception as e:
            logger.error(f"从Redis获取域名失败: {str(e)}")
            return None

    def filter_domains(self, domains):
        """
        过滤域名，按照两个步骤进行：
        1. 第一轮nginx配置过滤：检查域名是否已存在于nginx配置
           - 已存在的域名写入domains_exists集合
           - 不存在的域名写入domains_cert排序集合，设置初始失败计数为0
        2. 第二轮解析检测过滤：读取domains_cert中的域名进行解析检测
           - 解析成功的域名保存到结果列表，用于后续处理
           - 解析失败的域名失败计数+1，若达到上限则移入domains_remove集合
        """
        if not domains:
            logger.warning("没有域名需要处理")
            return []

        try:
            # 第一轮过滤前，先一次性同步nginx配置文件
            logger.info("开始同步宝塔nginx配置到本地...")
            sync_cmd = f"rsync -az --timeout=20 --delete {SSH_BT_HOST}:/www/server/panel/vhost/nginx /tmp/"
            sync_result = subprocess.run(sync_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if sync_result.returncode != 0:
                logger.error(f"同步nginx配置文件失败: {sync_result.stderr.decode('utf-8')}")
                return []
            logger.info("同步nginx配置文件成功")

            # 第一轮过滤：检查nginx配置中是否已存在该域名
            domain_list = domains.split()
            new_domains = []  # 存储不在nginx配置中的新域名
            
            for domain in domain_list:
                # 检查域名是否已存在于domains_exists集合
                if self.redis_client.sismember(REDIS_DOMAINS_EXISTS, domain):
                    # logger.info(f"域名 {domain} 已在已存在域名集合中，跳过")
                    continue
                
                # 检查域名是否已存在于domains_cert排序集合
                if self.redis_client.zscore(REDIS_DOMAINS_CERT, domain) is not None:
                    # logger.info(f"域名 {domain} 已在待处理排序集合中，跳过")
                    continue
                
                # 执行grep命令检查域名是否已存在于本地的nginx配置
                cmd = f"grep -roP \"(^|\\s){domain}(\\s|;|$)\" {NGINX_CONF_PATH}"
                result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                if result.returncode != 0:
                    # 域名不存在于nginx配置，添加到排序集合，失败计数为0
                    self.redis_client.zadd(REDIS_DOMAINS_CERT, {domain: 0})
                    new_domains.append(domain)
                    # logger.info(f"域名 {domain} 不存在nginx配置，添加到待处理排序集合")
                else:
                    # 域名存在于nginx配置，添加到domains_exists集合
                    self.redis_client.sadd(REDIS_DOMAINS_EXISTS, domain)
                    # logger.info(f"域名 {domain} 已存在nginx配置，添加到已存在域名集合")
            
            # 记录第一轮过滤结果
            logger.info(f"第一轮过滤完成，添加了 {len(new_domains)} 个新域名到待处理排序集合")
            
            # 继续执行第二轮解析检测过滤
            return self._filter_by_dns_resolution()
            
        except Exception as e:
            logger.error(f"过滤域名时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def _filter_by_dns_resolution(self):
        """
        第二轮解析检测过滤：读取domains_cert中的域名进行解析检测
        - 解析成功的域名保存到结果列表，用于后续处理
        - 解析失败的域名失败计数+1，若达到上限则移入domains_remove集合
        """
        try:
            # 创建临时文件保存域名
            temp_file = tempfile.mktemp()
            try:
                # 读取domains_cert中的域名
                domain_list = self.redis_client.zrange(REDIS_DOMAINS_CERT, 0, -1)
                if not domain_list:
                    logger.info("domains_cert中没有待处理的域名")
                    return []
                
                # 将域名写入临时文件
                with open(temp_file, 'w') as f:
                    f.write(' '.join(domain_list))
                
                # 使用auto_domains_check.sh进行解析检测
                # 该脚本需要返回两个变量：成功域名和失败域名
                logger.info("开始对域名进行解析检测....")
                cmd = f"sh {DOMAINS_CHECK_SCRIPT} {temp_file}"
                result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if result.returncode != 0:
                    logger.error(f"域名检测失败: {result.stderr.decode('utf-8')}")
                    return []
                
                # 解析脚本输出，获取成功和失败的域名
                # 脚本输出格式为：SUCCESS:domain1,domain2|FAIL:domain3,domain4
                # SUCCESS:m31.jieruitech.info,m32.jieruitech.info|FAIL:xxxxxx.com
                output = result.stdout.decode('utf-8').strip()
                success_domains = []
                fail_domains = []
                
                if '|' in output:
                    parts = output.split('|')
                    for part in parts:
                        if part.startswith('SUCCESS:'):
                            success_str = part.replace('SUCCESS:', '')
                            if success_str:
                                success_domains = success_str.split(',')
                        elif part.startswith('FAIL:'):
                            fail_str = part.replace('FAIL:', '')
                            if fail_str:
                                fail_domains = fail_str.split(',')
                
                # 处理解析成功的域名
                for domain in success_domains:
                    domain = domain.strip()
                    if domain:
                        # 从domains_cert排序集合中移除成功域名
                        self.redis_client.zrem(REDIS_DOMAINS_CERT, domain)
                        # logger.info(f"域名 {domain} 解析成功，从待处理排序集合中移除")
                
                # 处理解析失败的域名
                for domain in fail_domains:
                    domain = domain.strip()
                    if domain:
                        # 获取当前失败计数
                        current_count = self.redis_client.zscore(REDIS_DOMAINS_CERT, domain)
                        if current_count is not None:
                            # 失败计数加1
                            new_count = current_count + 1
                            self.redis_client.zadd(REDIS_DOMAINS_CERT, {domain: new_count})
                            # logger.info(f"域名 {domain} 解析失败，失败计数增加到 {new_count}")
                            
                            # 检查失败计数是否达到上限
                            if new_count >= MAX_FAILURE_COUNT:
                                # 从domains_cert移除，加入domains_remove
                                self.redis_client.zrem(REDIS_DOMAINS_CERT, domain)
                                self.redis_client.sadd(REDIS_DOMAINS_REMOVE, domain)
                                logger.info(f"域名 {domain} 失败计数达到上限 {MAX_FAILURE_COUNT}，移入移除集合")
                
                logger.info(f"第二轮过滤完成，成功域名 {len(success_domains)} 个，失败域名 {len(fail_domains)} 个")
                
                # 返回解析成功的域名列表，用于后续处理
                return success_domains
                
            except Exception as e:
                logger.error(f"处理域名解析结果时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                return []
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                
        except Exception as e:
            logger.error(f"执行第二轮解析检测过滤时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []

    def update_bt_site_domains(self, site_id, domains):
        """
        调用宝塔API更新站点域名信息
        """
        try:
            # 将域名列表转换为逗号分隔的字符串
            domains_str = ", ".join(domains)
            
            # 构建宝塔API调用命令(如果ip有变需要在auto_bt_api.py修改)
            cmd = f"python3.8 auto_bt_api.py 'bt.web_add_domain(\"{site_id}\",\"{domains_str}\")'"
            logger.info(f"执行宝塔API更新站点域名: {cmd}")
            
            # 执行命令
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            if result.returncode != 0:
                logger.error(f"宝塔API更新站点域名失败: {result.stderr.decode('utf-8')}")
                return False
            
            logger.info(f"宝塔API更新站点域名成功: {result.stdout.decode('utf-8')}")
            return True
            
        except Exception as e:
            logger.error(f"调用宝塔API更新站点域名时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def manage_domain_queue(self, domains):
        """
        管理域名队列，将域名添加到Redis中，并更新宝塔站点信息
        - 使用a/b两组站点进行轮询
        - 每个站点最多添加MAX_DOMAINS_PER_SITE个域名
        - 如果站点域名数量已满，创建新站点
        """
        if not domains:
            logger.warning("没有域名需要添加到队列")
            return
        
        try:
            # 获取当前使用的站点组
            current_site_group = f"auto{self.current_group}"
            
            # 调用脚本获取当前站点ID和域名信息
            # cmd = f"sh {DOMAINS_GET_SCRIPT} {current_site_group}"
            cmd = f'rsync -az {DOMAINS_GET_SCRIPT} {SSH_BT_HOST}:{DOMAINS_GET_SCRIPT} && ssh root@{SSH_BT_HOST} "sh {DOMAINS_GET_SCRIPT} {current_site_group}"'
            result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if result.returncode != 0:
                logger.error(f"获取当前站点信息失败: {result.stderr.decode('utf-8')}")
                return
            
            # 解析返回的JSON数据
            site_data = json.loads(result.stdout.decode('utf-8'))
            current_site_id = site_data["site_id"]
            current_domains = site_data["domains"]
            
            # 检查站点ID格式
            logger.info(f"站点ID格式检查: '{current_site_id}', 是否包含autoa: {'autoa' in current_site_id}, 是否包含autob: {'autob' in current_site_id}")
            
            logger.info(f"当前站点ID: {current_site_id}, 已有域名数量: {len(current_domains)}")
            
            # 开始处理新域名
            remaining_domains = domains.copy()  # 创建域名副本，用于跟踪剩余待处理域名
            
            while remaining_domains:
                # 检查当前站点域名是否达到上限
                current_count = len(current_domains)
                space_left = MAX_DOMAINS_PER_SITE - current_count
                
                if space_left <= 0:
                    # 当前站点域名已满，先提交队列，然后创建新站点
                    # 先更新宝塔站点域名信息
                    self.update_bt_site_domains(current_site_id, current_domains)
                    
                    # 将当前站点信息写入Redis队列
                    site_info = json.dumps({
                        "site_id": current_site_id,
                        "domains": current_domains
                    })
                    # 根据站点ID确定使用哪个队列
                    if 'autoa' in current_site_id:
                        queue_name = 'domains_queue_autoa'
                        logger.info(f"站点ID包含autoa，使用队列: {queue_name}")
                    elif 'autob' in current_site_id:
                        queue_name = 'domains_queue_autob'
                        logger.info(f"站点ID包含autob，使用队列: {queue_name}")
                    else:
                        queue_name = REDIS_CERT_QUEUE
                        logger.info(f"站点ID不包含autoa或autob，使用默认队列: {queue_name}")
                    
                    self.redis_client.rpush(queue_name, site_info)
                    logger.info(f"站点 {current_site_id} 域名数量已达上限，已加入证书生成队列 {queue_name}")
                    
                    # 创建新站点ID，提取数字部分并加1
                    site_num = int(current_site_id.replace(f"auto{self.current_group}", ""))
                    new_site_id = f"auto{self.current_group}{site_num + 1}"
                    
                    # 执行创建WAF站点脚本
                    # create_cmd = f"sh /root/ajie/yunwei/auto_create_waf_site.sh {new_site_id}"    ## 创建waf站点
                    site_params = json.dumps({"webname": new_site_id})
                    create_cmd = f"python3.8 auto_bt_api.py 'bt.add_site({site_params})'"  # 外层双引号，内层单引号
                    create_result = subprocess.run(create_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    if create_result.returncode != 0:
                        logger.error(f"创建新站点失败: {create_result.stderr.decode('utf-8')}")
                        return
                    logger.info(f"成功创建新站点: {new_site_id}")
                    
                    # 重新获取站点信息
                    get_cmd = f'ssh root@{SSH_BT_HOST} "sh {DOMAINS_GET_SCRIPT} {new_site_id}"'
                    get_result = subprocess.run(get_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    if get_result.returncode != 0:
                        logger.error(f"获取新站点信息失败: {get_result.stderr.decode('utf-8')}")
                        return
                    
                    # 更新当前站点信息
                    site_data = json.loads(get_result.stdout.decode('utf-8'))
                    current_site_id = site_data["site_id"]
                    current_domains = site_data["domains"]
                    
                    logger.info(f"切换到新站点: {current_site_id}, 初始域名数量: {len(current_domains)}")
                    
                    # 继续下一轮循环处理剩余域名
                    continue
                
                # 计算本次可添加的域名数量
                domains_to_add = remaining_domains[:space_left]
                remaining_domains = remaining_domains[space_left:]
                
                # 添加域名到当前站点
                current_domains.extend(domains_to_add)
                
                # 更新Redis中的域名信息
                site_info = json.dumps({
                    "site_id": current_site_id,
                    "domains": current_domains
                })
                self.redis_client.hset(REDIS_DOMAIN_HASH, current_site_id, site_info)
                
                logger.info(f"已向站点 {current_site_id} 添加 {len(domains_to_add)} 个域名，当前总数: {len(current_domains)}")
            
            # 所有域名处理完毕，将最后一个站点信息提交到队列
            # 先更新宝塔站点域名信息
            self.update_bt_site_domains(current_site_id, current_domains)
            
            site_info = json.dumps({
                "site_id": current_site_id,
                "domains": current_domains
            })
            # 根据站点ID确定使用哪个队列
            if 'autoa' in current_site_id:
                queue_name = 'domains_queue_autoa'
                logger.info(f"站点ID包含autoa，使用队列: {queue_name}")
            elif 'autob' in current_site_id:
                queue_name = 'domains_queue_autob'
                logger.info(f"站点ID包含autob，使用队列: {queue_name}")
            else:
                queue_name = REDIS_CERT_QUEUE
                logger.info(f"站点ID不包含autoa或autob，使用默认队列: {queue_name}")
            
            self.redis_client.rpush(queue_name, site_info)
            logger.info(f"所有域名处理完毕，站点 {current_site_id} 已加入证书生成队列 {queue_name}")
            
        except Exception as e:
            logger.error(f"管理域名队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def process(self):
        """
        执行完整的域名处理流程
        1. 从Redis获取域名
        2. 进行两轮过滤处理
        3. 将过滤后的域名添加到队列
        4. 切换站点组，准备下一轮处理
        """
        try:
            logger.info("开始处理域名，当前使用站点组: %s", self.current_group)
            
            # 1. 从Redis获取域名
            domains = self.fetch_domains()
            filtered_domains = []
            
            if not domains:
                logger.info("Redis中未获取到域名，直接检查排序集合中的域名")
                # 即使没有新域名，也检查domains_cert_sort_set中的域名
                filtered_domains = self._filter_by_dns_resolution()
                if filtered_domains:
                    logger.info(f"从排序集合中发现 {len(filtered_domains)} 个解析成功的域名")
            else:
                # 如果有新域名，进行完整的两轮过滤
                filtered_domains = self.filter_domains(domains)
            
            # 3. 将过滤后的域名添加到队列
            if filtered_domains:
                self.manage_domain_queue(filtered_domains)
                # 只有成功添加域名后才切换站点组
                self.current_group = 'b' if self.current_group == 'a' else 'a'
                logger.info("本轮处理完成，切换站点组为: %s", self.current_group)
            else:
                logger.info("未发现有效域名，跳过队列管理")
            
        except Exception as e:
            logger.error(f"处理域名时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

def main():
    """主函数，创建处理器并循环执行处理流程"""
    try:
        processor = DomainProcessor()
        logger.info("域名处理器已启动，开始循环执行...")
        
        while True:
            # 执行一次完整的处理流程
            processor.process()
            
            # 等待30秒再次执行
            logger.info("等待60秒后再次执行...")
            time.sleep(60)
            
    except KeyboardInterrupt:
        logger.info("接收到中断信号，程序退出")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 
