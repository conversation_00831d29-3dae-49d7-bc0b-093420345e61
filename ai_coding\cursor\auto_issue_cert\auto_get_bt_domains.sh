#!/bin/bash
# 20250417 用于提取bt中的nginx最新conf配置，和waf区别只是nginx.conf配置命名不同
# 需要先确认yum install -y jq

# 检查是否安装了jq
if ! command -v jq &> /dev/null; then
    echo "正在安装必要JSON处理器jq..."
    yum install -y jq
    if [ $? -ne 0 ]; then
        echo "安装失败，请检查yum配置或网络连接"
        exit 1
    fi
fi

NGINX_CONF_DIR="/www/server/panel/vhost/nginx/"  # 定义nginx配置目录
PREFIX=${1:-"auto"}  # 可配置的前缀变量（默认为auto）
# 第一步：找出所有匹配的文件
file_list=$(ls ${NGINX_CONF_DIR} | grep -E "${PREFIX}[0-9]*.com\.conf")
if [[ -z "$file_list" ]]; then
    echo '{"error":"未找到符合条件的'"${PREFIX}"'{x}.com.conf文件"}'
    exit 1
fi
# 提取所有数字并找出最大值对应的完整前缀+数字
site_id_with_prefix=$(echo "$file_list" | grep -oP "${PREFIX}\K[0-9]*" | sort -n | tail -1)
site_id="${PREFIX}${site_id_with_prefix}"
# 获取最大数字对应的文件名
max_file="${site_id}.com.conf"

# 第二步：提取server_name并过滤掉自动添加的域名
domains=$(grep -h 'server_name' "${NGINX_CONF_DIR}/${max_file}" | \
           sed -e 's/server_name//' -e 's/;//' -e 's/^\s*\|\s*$//g' | \
           tr ' ' '\n' | \
           grep -v -E "${site_id}.com|${site_id}\.com" | \
           grep -v '^$' | \
           sort -u)

# 使用jq -c参数生成紧凑的单行JSON格式
# 收集有效域名
valid_domains=()
for domain in $domains; do
    if [ -n "$domain" ]; then  # 只添加非空字符串
        valid_domains+=("$domain")
    fi
done

# 根据是否有有效域名处理JSON输出
if [ ${#valid_domains[@]} -eq 0 ]; then
    # 没有域名时直接使用空数组
    json_output=$(jq -c -n \
      --arg site_id "$site_id" \
      '{
        site_id: $site_id,
        domains: []
      }')
else
    # 有域名时正常处理
    json_output=$(jq -c -n \
      --arg site_id "$site_id" \
      --argjson domains "$(printf '%s\n' "${valid_domains[@]}" | jq -R . | jq -s .)" \
      '{
        site_id: $site_id,
        domains: $domains
      }')
fi

# 输出单行JSON结果
echo "$json_output"
