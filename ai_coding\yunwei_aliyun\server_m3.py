import asyncio
import json
import subprocess
import os
from pathlib import Path
from typing import Dict
from aiohttp import web
import aiohttp
from aiohttp_cors import setup as cors_setup, ResourceOptions
import time
from asyncio import Queue
import logging

class CommandExecutor:
    CERT_SCRIPT = "/root/ajie/yunwei/apply_cert.sh"
    CHECK_SCRIPT = "/root/ajie/yunwei/domains_check.sh"
    RSYNC_SCRIPT = "ssh -p 17888 root@************* \"sh /root/ajie/tongbu_site.sh {} 1\""
    
    async def execute_command(self, site_id: str, command_type: str, output_queue: Queue):
        if not self._validate_site_id(site_id):
            raise ValueError("无效的站点ID格式")
        
        # 根据命令类型选择脚本
        if command_type == 'cert':
            command = f"sh {self.CERT_SCRIPT} {site_id}"
            action = "证书申请"
        elif command_type == 'check':
            command = f"sh {self.CHECK_SCRIPT} {site_id}"
            action = "域名检查"
        elif command_type == 'rsync_cert':
            command = self.RSYNC_SCRIPT.format(site_id)
            action = "证书同步"
        else:
            raise ValueError(f"不支持的命令类型: {command_type}")
        
        # 立即发送开始执行的消息
        await output_queue.put(f"开始执行{action}脚本...")
        
        process = await asyncio.create_subprocess_shell(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 创建输出监控任务
        async def monitor_process():
            start_time = time.time()
            last_update_time = start_time
            
            while True:
                # 检查进程是否结束
                try:
                    # 非阻塞方式检查进程状态
                    process_complete = process.returncode is not None
                    current_time = time.time()
                    
                    # 每15秒发送一次状态更新
                    if current_time - last_update_time >= 15:
                        elapsed_time = int(current_time - start_time)
                        minutes = elapsed_time // 60
                        seconds = elapsed_time % 60
                        await output_queue.put(
                            f"脚本正在执行中，已运行 {minutes}分{seconds}秒，请耐心等待..."
                        )
                        last_update_time = current_time
                    
                    if process_complete:
                        break
                        
                    await asyncio.sleep(1)  # 短暂休眠，避免CPU过度使用
                    
                except Exception as e:
                    await output_queue.put(f"监控任务出错: {str(e)}")
                    break

        try:
            # 启动监控任务
            monitor_task = asyncio.create_task(monitor_process())
            
            # 读取stdout
            while True:
                line = await process.stdout.readline()
                if not line:
                    break
                await output_queue.put(line.decode('utf-8').strip())
            
            # 读取stderr
            while True:
                line = await process.stderr.readline()
                if not line:
                    break
                await output_queue.put(f"[错误] {line.decode('utf-8').strip()}")
            
            # 等待进程完成
            await process.wait()
            
            # 取消监控任务
            monitor_task.cancel()
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
            
            if process.returncode != 0:
                await output_queue.put(f"命令执行失败，退出码: {process.returncode}")
            else:
                await output_queue.put("命令执行完成")
                
        except Exception as e:
            await output_queue.put(f"执行命令时出错: {str(e)}")
            raise
        finally:
            # 确保进程被终止
            if process.returncode is None:
                try:
                    process.terminate()
                    await process.wait()
                except:
                    pass
    
    def _validate_site_id(self, site_id: str) -> bool:
        import re
        return bool(re.match(r'^[a-zA-Z0-9-]{1,50}$', site_id))

class CombinedServer:
    def __init__(self, ws_port: int = 8765, http_port: int = 50005):
        self.base_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.ws_port = ws_port
        self.http_port = http_port
        self.executor = CommandExecutor()
        self.app = web.Application(
            client_max_size=1024**2,
        )
        
        cors = cors_setup(self.app, defaults={
            "*": ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        self.app.router.add_get('/zhengshu/', self.handle_index)
        self.app.router.add_get('/zhengshu/ws/', self.handle_websocket)
        
        for route in list(self.app.router.routes()):
            cors.add(route)
        
        self.active_tasks = {}  # 存储活动任务 {task_id: (execute_task, output_queue)}
        self.ws_connections = {}  # 存储WebSocket连接 {site_id: [ws1, ws2, ...]}  # 修改为列表
        self.task_outputs = {}  # 存储任务输出历史 {task_id: [outputs]}
        self.task_site_mapping = {}  # 新增：任务ID到站点ID的映射 {task_id: site_id}

    async def handle_websocket(self, request):
        ws = web.WebSocketResponse(
            heartbeat=15,      # 15秒发送一次心跳
            # heartbeat=3600,      # 60分钟发送一次心跳
            timeout=7200,        # 120分钟总超时
            receive_timeout=3600, # 60分钟接收超时
            autoclose=False,     # 禁止自动关闭
            autoping=True,       # 自动响应ping
            protocols=['websocket']  # 明确指定WebSocket协议
        )
        await ws.prepare(request)
        
        print("新的WebSocket连接已建立")
        current_task_id = None
        current_site_id = None
        
        try:
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        print(f"收到消息: {data}")
                        
                        # 处理心跳和ping消息
                        if data.get('type') in ['heartbeat', 'ping']:
                            await ws.send_json({"type": data['type'], "status": "ok"})
                            continue
                        
                        # 只有执行命令时才需要site_id
                        if data.get('type') == 'execute':
                            site_id = data.get('site_id')
                            command_type = data.get('command_type', 'cert')  # 默认为证书申请
                            
                            if not site_id:
                                raise ValueError("站点ID不能为空")
                            
                            current_site_id = site_id
                            
                            # 修改连接管理逻辑，改为列表存储同一站点的多个连接
                            if site_id not in self.ws_connections:
                                self.ws_connections[site_id] = []
                            if ws not in self.ws_connections[site_id]:
                                self.ws_connections[site_id].append(ws)
                            
                            # 生成任务ID
                            task_id = f"{site_id}_{command_type}_{int(time.time())}"
                            current_task_id = task_id
                            self.task_site_mapping[task_id] = site_id  # 记录任务与站点的映射关系
                            
                            print(f"开始执行命令，站点ID: {site_id}, 任务ID: {task_id}, 类型: {command_type}")
                            
                            # 创建输出队列
                            output_queue = Queue()
                            
                            # 启动命令执行任务
                            execute_task = asyncio.create_task(
                                self.executor.execute_command(site_id, command_type, output_queue)
                            )
                            
                            # 存储任务信息
                            self.active_tasks[task_id] = (execute_task, output_queue)
                            
                            # 添加任务完成后的清理回调
                            execute_task.add_done_callback(
                                lambda _: asyncio.create_task(self.cleanup_task(task_id))
                            )
                            
                            # 处理输出队列
                            asyncio.create_task(self.process_output_queue(task_id, output_queue, site_id))
                            
                            # 继续处理新的消息
                            continue
                            
                    except ValueError as ve:
                        print(f"值错误: {ve}")
                        if not ws.closed:
                            await ws.send_json({
                                "status": "error",
                                "output": str(ve)
                            })
                        # 继续处理新的消息
                        continue
                        
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    print(f'WebSocket连接错误: {ws.exception()}')
                
        except Exception as e:
            print(f"WebSocket处理时出错: {e}")
        finally:
            print("WebSocket连接关闭")
            # 从连接列表中移除当前连接
            if current_site_id and current_site_id in self.ws_connections:
                if ws in self.ws_connections[current_site_id]:
                    self.ws_connections[current_site_id].remove(ws)
                if not self.ws_connections[current_site_id]:
                    del self.ws_connections[current_site_id]
            if not ws.closed:
                await ws.close()
        
        return ws

    async def handle_index(self, request):
        index_path = self.base_dir / 'templates' / 'index.html'
        if not index_path.exists():
            raise web.HTTPNotFound(text="找不到index.html文件")
        return web.FileResponse(index_path)

    async def start(self):
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, '127.0.0.1', self.http_port)
        await site.start()
        print(f"HTTP服务器启动在 http://127.0.0.1:{self.http_port}")

    # 新增：处理输出队列的方法
    async def process_output_queue(self, task_id: str, output_queue: Queue, site_id: str):
        try:
            execute_task, _ = self.active_tasks.get(task_id, (None, None))
            if not execute_task:
                return
                
            while True:
                try:
                    output = await output_queue.get()
                    print(f"任务 {task_id} 输出: {output}")
                    
                    # 向该站点的所有连接发送消息
                    active_connections = self.ws_connections.get(site_id, [])
                    if not active_connections:
                        print(f"站点 {site_id} 没有活动的WebSocket连接")
                        continue
                        
                    for connection in active_connections[:]:  # 使用副本迭代
                        if not connection.closed:
                            try:
                                await connection.send_json({
                                    "status": "running",
                                    "output": output
                                })
                            except Exception as e:
                                print(f"发送消息到WebSocket失败: {e}")
                                # 如果发送失败，从活动连接中移除
                                if connection in active_connections:
                                    active_connections.remove(connection)
                        else:
                            # 如果连接已关闭，从活动连接中移除
                            if connection in active_connections:
                                active_connections.remove(connection)
                    
                    # 检查任务是否完成
                    if execute_task.done():
                        print(f"任务 {task_id} 执行完成")
                        
                        # 向该站点的所有连接发送完成消息
                        for connection in active_connections[:]:
                            if not connection.closed:
                                try:
                                    await connection.send_json({
                                        "status": "completed",
                                        "output": "任务执行完成"
                                    })
                                except Exception:
                                    pass
                        break
                        
                except Exception as e:
                    print(f"处理输出时出错: {e}")
                    break
                    
        except Exception as e:
            print(f"处理输出队列时出错: {e}")
        finally:
            print(f"输出队列处理完成: {task_id}")
    
    # 新增：任务清理方法
    async def cleanup_task(self, task_id: str):
        print(f"清理任务: {task_id}")
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        if task_id in self.task_site_mapping:
            del self.task_site_mapping[task_id]
        print(f"任务 {task_id} 已清理")

def create_app():
    server = CombinedServer()
    return server.app

def run_server():
    app = create_app()
    web.run_app(app, host='127.0.0.1', port=50005)

if __name__ == "__main__":
    import sys
    from watchgod import run_process, PythonWatcher
    
    logging.basicConfig(level=logging.INFO)
    
    # 默认开启调试模式，除非明确设置 PRODUCTION=1
    if not os.environ.get('PRODUCTION'):
        # 开发模式：使用 watchgod 实现自动重载
        print("开发模式：启用自动重载")
        try:
            run_process(
                os.path.dirname(os.path.abspath(__file__)),  # 使用当前文件所在目录
                run_server,
                args=(),
                kwargs={},
                watcher_cls=PythonWatcher,  # 只监控 Python 文件
            )
        except KeyboardInterrupt:
            print("\n正在关闭服务器...")
            sys.exit(0)
    else:
        # 生产模式：普通运行
        print("生产模式：禁用自动重载")
        run_server()
