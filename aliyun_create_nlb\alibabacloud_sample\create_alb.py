#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/7/12 9:26
# @Name    : create_alb.py
# @email   : <EMAIL>
# <AUTHOR> 钢铁知识库
# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_alb20200616.client import Client as Alb20200616Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_alb20200616 import models as alb_20200616_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client(
        access_key_id: str,
        access_key_secret: str,
    ) -> Alb20200616Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 必填，您的 AccessKey ID,
            access_key_id=access_key_id,
            # 必填，您的 AccessKey Secret,
            access_key_secret=access_key_secret
        )
        # Endpoint 请参考 https://api.aliyun.com/product/Alb
        config.endpoint = f'alb.cn-chengdu.aliyuncs.com'
        return Alb20200616Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        # 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html
        client = Sample.create_client(os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'], os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'])
        load_balancer_billing_config = alb_20200616_models.CreateLoadBalancerRequestLoadBalancerBillingConfig(
            pay_type='PostPay'
        )
        zone_mappings_0 = alb_20200616_models.CreateLoadBalancerRequestZoneMappings(
            zone_id='vsw-2vcix8kxii3b358nf5lae',
            v_switch_id='vsw-2vc0kq9e2dy4mvnrbfnxh'
        )
        create_load_balancer_request = alb_20200616_models.CreateLoadBalancerRequest(
            vpc_id='vpc-2vcgvke98duchx9283bvf',
            address_type='Internet',
            load_balancer_name='客户1',
            zone_mappings=[
                zone_mappings_0
            ],
            address_allocated_mode='Dynamic',
            load_balancer_edition='StandardWithWaf',
            load_balancer_billing_config=load_balancer_billing_config,
            address_ip_version='IPv4'
        )
        runtime = util_models.RuntimeOptions()
        try:
            # 复制代码运行请自行打印 API 的返回值
            client.create_load_balancer_with_options(create_load_balancer_request, runtime)
        except Exception as error:
            # 如有需要，请打印 error
            UtilClient.assert_as_string(error.message)

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        # 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html
        client = Sample.create_client(os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'], os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET'])
        load_balancer_billing_config = alb_20200616_models.CreateLoadBalancerRequestLoadBalancerBillingConfig(
            pay_type='PostPay'
        )
        zone_mappings_0 = alb_20200616_models.CreateLoadBalancerRequestZoneMappings(
            zone_id='vsw-2vcix8kxii3b358nf5lae',
            v_switch_id='vsw-2vc0kq9e2dy4mvnrbfnxh'
        )
        create_load_balancer_request = alb_20200616_models.CreateLoadBalancerRequest(
            vpc_id='vpc-2vcgvke98duchx9283bvf',
            address_type='Internet',
            load_balancer_name='客户1',
            zone_mappings=[
                zone_mappings_0
            ],
            address_allocated_mode='Dynamic',
            load_balancer_edition='StandardWithWaf',
            load_balancer_billing_config=load_balancer_billing_config,
            address_ip_version='IPv4'
        )
        runtime = util_models.RuntimeOptions()
        try:
            # 复制代码运行请自行打印 API 的返回值
            await client.create_load_balancer_with_options_async(create_load_balancer_request, runtime)
        except Exception as error:
            # 如有需要，请打印 error
            UtilClient.assert_as_string(error.message)


if __name__ == '__main__':
    Sample.main(sys.argv[1:])
