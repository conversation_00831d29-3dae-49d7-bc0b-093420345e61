<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>科技感双侧对比图表</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #0a0a14;
      font-family: 'Arial', sans-serif;
    }
    .container {
      width: 1000px;
      height: 300px;
      margin: 20px;
    }
    .controls {
      background-color: rgba(30, 30, 50, 0.8);
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      color: #fff;
      box-shadow: 0 0 15px rgba(0, 140, 255, 0.3);
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #00ccff;
    }
    input, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #2a3a5a;
      background-color: rgba(10, 20, 40, 0.6);
      color: #fff;
      border-radius: 4px;
    }
    .row {
      display: flex;
      gap: 20px;
    }
    .col {
      flex: 1;
    }
    button {
      background: linear-gradient(135deg, #0066cc, #00ccff);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s ease;
      margin-top: 10px;
    }
    button:hover {
      background: linear-gradient(135deg, #00ccff, #0066cc);
      box-shadow: 0 0 10px rgba(0, 204, 255, 0.5);
    }
    #svgOutput {
      background-color: rgba(20, 20, 30, 0.5);
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 0 20px rgba(0, 140, 255, 0.2);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="controls">
      <h2 style="text-align: center; color: #00ccff; margin-top: 0;">科技感双侧对比图表生成器</h2>
      
      <div class="form-group">
        <label for="mainTitle">主标题</label>
        <input type="text" id="mainTitle" value="科技感双侧对比">
      </div>
      
      <div class="row">
        <div class="col">
          <div class="form-group">
            <label for="leftTitle">左侧标题</label>
            <input type="text" id="leftTitle" value="传统方案">
          </div>
          
          <div class="form-group">
            <label for="leftColor">左侧颜色</label>
            <input type="text" id="leftColor" value="#ff3366">
          </div>
          
          <div class="form-group">
            <label for="leftPoints">左侧要点 (每行一个)</label>
            <textarea id="leftPoints" rows="3">要点1
要点2
要点3</textarea>
          </div>
          
          <div class="form-group">
            <label for="leftConclusion">左侧结论</label>
            <input type="text" id="leftConclusion" value="传统方案结论">
          </div>
          
          <div class="form-group">
            <label for="leftNote">左侧备注</label>
            <input type="text" id="leftNote" value="额外说明信息">
          </div>
        </div>
        
        <div class="col">
          <div class="form-group">
            <label for="rightTitle">右侧标题</label>
            <input type="text" id="rightTitle" value="创新方案">
          </div>
          
          <div class="form-group">
            <label for="rightColor">右侧颜色</label>
            <input type="text" id="rightColor" value="#00ccff">
          </div>
          
          <div class="form-group">
            <label for="rightPoints">右侧要点 (每行一个)</label>
            <textarea id="rightPoints" rows="3">要点1
要点2
要点3</textarea>
          </div>
          
          <div class="form-group">
            <label for="rightConclusion">右侧结论</label>
            <input type="text" id="rightConclusion" value="创新方案结论">
          </div>
          
          <div class="form-group">
            <label for="rightNote">右侧备注</label>
            <input type="text" id="rightNote" value="额外说明信息">
          </div>
        </div>
      </div>
      
      <div class="form-group">
        <label for="bottomText">底部文字</label>
        <input type="text" id="bottomText" value="总结性文字 · 关键词1 · 关键词2 · 关键词3">
      </div>
      
      <button onclick="generateSVG()">生成图表</button>
    </div>
    
    <div id="svgOutput"></div>
  </div>

  <script>
    // 页面加载时生成默认SVG
    window.onload = function() {
      generateSVG();
    };

    function generateSVG() {
      // 获取所有输入值
      const mainTitle = document.getElementById('mainTitle').value;
      const leftTitle = document.getElementById('leftTitle').value;
      const leftColor = document.getElementById('leftColor').value;
      const leftPoints = document.getElementById('leftPoints').value.split('\n');
      const leftConclusion = document.getElementById('leftConclusion').value;
      const leftNote = document.getElementById('leftNote').value;
      
      const rightTitle = document.getElementById('rightTitle').value;
      const rightColor = document.getElementById('rightColor').value;
      const rightPoints = document.getElementById('rightPoints').value.split('\n');
      const rightConclusion = document.getElementById('rightConclusion').value;
      const rightNote = document.getElementById('rightNote').value;
      
      const bottomText = document.getElementById('bottomText').value;
      
      // 创建SVG
      const svgWidth = 1000;
      const svgHeight = 300;
      
      let svg = `
      <svg width="${svgWidth}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <!-- 背景渐变 -->
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#0a0a14" />
            <stop offset="100%" stop-color="#141428" />
          </linearGradient>
          
          <!-- 网格图案 -->
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(50, 130, 240, 0.1)" stroke-width="0.5"/>
          </pattern>
          
          <!-- 左侧光晕 -->
          <radialGradient id="leftGlow" cx="25%" cy="50%" r="50%" fx="25%" fy="50%">
            <stop offset="0%" stop-color="${leftColor}" stop-opacity="0.15" />
            <stop offset="100%" stop-color="${leftColor}" stop-opacity="0" />
          </radialGradient>
          
          <!-- 右侧光晕 -->
          <radialGradient id="rightGlow" cx="75%" cy="50%" r="50%" fx="75%" fy="50%">
            <stop offset="0%" stop-color="${rightColor}" stop-opacity="0.15" />
            <stop offset="100%" stop-color="${rightColor}" stop-opacity="0" />
          </radialGradient>
          
          <!-- 文字阴影滤镜 -->
          <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur" />
            <feOffset in="blur" dx="1" dy="1" result="offsetBlur" />
            <feFlood flood-color="#000" flood-opacity="0.7" result="offsetColor"/>
            <feComposite in="offsetColor" in2="offsetBlur" operator="in" result="offsetBlur"/>
            <feMerge>
              <feMergeNode in="offsetBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
          
          <!-- 发光效果滤镜 -->
          <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur stdDeviation="5" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
          </filter>
        </defs>
        
        <!-- 背景 -->
        <rect width="${svgWidth}" height="${svgHeight}" fill="url(#bgGradient)" />
        <rect width="${svgWidth}" height="${svgHeight}" fill="url(#grid)" />
        
        <!-- 光晕效果 -->
        <rect x="0" y="0" width="${svgWidth/2}" height="${svgHeight}" fill="url(#leftGlow)" />
        <rect x="${svgWidth/2}" y="0" width="${svgWidth/2}" height="${svgHeight}" fill="url(#rightGlow)" />
        
        <!-- 中间分隔线 -->
        <line x1="${svgWidth/2}" y1="30" x2="${svgWidth/2}" y2="${svgHeight-30}" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="5,3" />
        
        <!-- 主标题 -->
        <text x="${svgWidth/2}" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white" text-anchor="middle" filter="url(#textShadow)">${mainTitle}</text>
        
        <!-- 左侧内容 -->
        <text x="20" y="60" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="${leftColor}" filter="url(#textShadow)">${leftTitle}</text>
`;

      // 左侧要点
      leftPoints.forEach((point, index) => {
        if (point.trim()) {
          svg += `        <text x="40" y="${95 + index * 25}" font-family="Arial, sans-serif" font-size="12" fill="white">• ${point}</text>\n`;
        }
      });

      // 左侧结论和备注
      svg += `
        <text x="20" y="${svgHeight - 60}" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="${leftColor}" filter="url(#textShadow)">${leftConclusion}</text>
        <text x="20" y="${svgHeight - 40}" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.7)">${leftNote}</text>
        
        <!-- 右侧内容 -->
        <text x="${svgWidth - 20}" y="60" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="${rightColor}" text-anchor="end" filter="url(#textShadow)">${rightTitle}</text>
`;

      // 右侧要点
      rightPoints.forEach((point, index) => {
        if (point.trim()) {
          svg += `        <text x="${svgWidth - 40}" y="${95 + index * 25}" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="end">• ${point}</text>\n`;
        }
      });

      // 右侧结论和备注
      svg += `
        <text x="${svgWidth - 20}" y="${svgHeight - 60}" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="${rightColor}" text-anchor="end" filter="url(#textShadow)">${rightConclusion}</text>
        <text x="${svgWidth - 20}" y="${svgHeight - 40}" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.7)" text-anchor="end">${rightNote}</text>
        
        <!-- 底部文字 -->
        <text x="${svgWidth/2}" y="${svgHeight - 15}" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">${bottomText}</text>
        
        <!-- 装饰元素 -->
        <path d="M 10,10 L 30,10 L 30,30" stroke="${leftColor}" stroke-width="2" fill="none" />
        <path d="M ${svgWidth-10},10 L ${svgWidth-30},10 L ${svgWidth-30},30" stroke="${rightColor}" stroke-width="2" fill="none" />
        <path d="M 10,${svgHeight-10} L 30,${svgHeight-10} L 30,${svgHeight-30}" stroke="${leftColor}" stroke-width="2" fill="none" />
        <path d="M ${svgWidth-10},${svgHeight-10} L ${svgWidth-30},${svgHeight-10} L ${svgWidth-30},${svgHeight-30}" stroke="${rightColor}" stroke-width="2" fill="none" />
        
        <!-- 中间箭头 -->
        <path d="M ${svgWidth/2-15},${svgHeight/2} L ${svgWidth/2+15},${svgHeight/2} M ${svgWidth/2+10},${svgHeight/2-5} L ${svgWidth/2+15},${svgHeight/2} L ${svgWidth/2+10},${svgHeight/2+5}" stroke="white" stroke-width="1.5" fill="none" />
        
        <!-- 科技感装饰元素 -->
        <circle cx="${svgWidth/4}" cy="${svgHeight-20}" r="3" fill="${leftColor}" filter="url(#glow)" />
        <circle cx="${3*svgWidth/4}" cy="${svgHeight-20}" r="3" fill="${rightColor}" filter="url(#glow)" />
        
        <!-- 数据线条装饰 -->
        <path d="M 5,${svgHeight/2+50} Q ${svgWidth/4},${svgHeight/2+20} ${svgWidth/2-10},${svgHeight/2+40}" stroke="${leftColor}" stroke-width="1" fill="none" stroke-opacity="0.5" />
        <path d="M ${svgWidth-5},${svgHeight/2+50} Q ${3*svgWidth/4},${svgHeight/2+20} ${svgWidth/2+10},${svgHeight/2+40}" stroke="${rightColor}" stroke-width="1" fill="none" stroke-opacity="0.5" />
      </svg>
      `;
      
      // 将SVG输出到页面
      document.getElementById('svgOutput').innerHTML = svg;
    }
  </script>
</body>
</html>