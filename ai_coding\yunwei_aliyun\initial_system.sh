#!/bin/bash

#### 1.完成安装后，需要手动修改主机名、端口、密码
#### 2.手动修改同步机的配置：/etc/crontab /etc/ansible/hosts /root/ajie/tongbu_waf.sh
#### 3.手动添加证书的配置：domains_check.sh .acme.sh/acme.sh 

###执行前需要修改的变量
# x61:************  x111:***********  x13:**************  m3:*************
RSYNC_IP='*************'
RSYNC_WAF_PASS='7d6b0fcccf84eb5908e18209a44986bf'

# 设置错误立即退出
set -e

# 日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 错误处理函数
error_handler() {
    log "错误发生在第 $1 行"
    exit 1
}

trap 'error_handler ${LINENO}' ERR

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
   log "此脚本必须以root用户运行"
   exit 1
fi

log "开始系统初始化..."

# 防火墙配置
log "配置防火墙..."
firewall-cmd --zone=public --add-port=17888/tcp --permanent
firewall-cmd --zone=public --add-port=80/tcp --permanent
firewall-cmd --zone=public --add-port=443/tcp --permanent
firewall-cmd --reload && firewall-cmd --list-all

# 更换yum源
log "更换yum源为阿里云源..."
\cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.bak
curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo
yum clean all && yum makecache -y

# 基础环境安装
log "安装基础环境..."
sed -i 's/#   StrictHostKeyChecking ask/   StrictHostKeyChecking no/' /etc/ssh/ssh_config

# 安装expect（用于处理交互）
yum install -y expect

# 创建处理yum交互的expect脚本
cat > /tmp/yum_handler.exp << 'EOF'
#!/usr/bin/expect
set timeout 300
set command [lindex $argv 0]
spawn {*}$command
expect {
    "Is this ok" { send "y\r"; exp_continue }
    "Importing GPG key" { send "y\r"; exp_continue }
    eof
}
EOF
chmod +x /tmp/yum_handler.exp

# 检查并安装remi源
if ! rpm -q remi-release >/dev/null 2>&1; then
    log "安装remi源..."
    /tmp/yum_handler.exp "yum -y install https://rpms.remirepo.net/enterprise/remi-release-7.rpm"
else
    log "remi源已安装, 跳过..."
fi

# 检查并安装epel源
if ! rpm -q epel-release >/dev/null 2>&1; then
    log "安装epel源..."
    /tmp/yum_handler.exp "yum install epel-release -y"
else
    log "epel源已安装, 跳过..."
fi

# 安装yum-utils并启用PHP7.4
/tmp/yum_handler.exp "yum -y install yum-utils"
yum-config-manager --enable remi-php74

# 分批安装软件包以提高成功率
log "安装基础工具包..."
/tmp/yum_handler.exp "yum install -y rsync lrzsz zip unzip expect vim wget net-tools"
/tmp/yum_handler.exp "yum install -y atop nload telnet sysstat htop smartmontools iotop jq lsof iftop"
/tmp/yum_handler.exp "yum install -y screen mlocate tree sysstat nethogs bind-utils speedtest-cli"

log "安装PHP和Nginx相关包..."
/tmp/yum_handler.exp "yum install -y nginx"
/tmp/yum_handler.exp "yum install -y php php-devel php-fpm php-xml redis php-redis php-mbstring"

# 检查安装结果
if ! command -v php >/dev/null 2>&1 || ! command -v nginx >/dev/null 2>&1; then
    log "错误: PHP或Nginx安装失败"
    exit 1
fi

# SSH密钥配置
log "配置SSH密钥..."
#ssh-keygen -t rsa -N '' -f /root/.ssh/id_rsa -q
yes | ssh-keygen -t rsa -N '' -f /root/.ssh/id_rsa -q -C "auto-generated-key"

# 写入SSH密钥文件
cat > /root/.ssh/id_rsa << 'EOF'
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF

cat > /root/.ssh/id_rsa.pub << 'EOF'
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDOIUAPSz1qNXctFflQE2dp2rCarfWkhhuBu0ZDYsyW3CocsV2NZbZAd2QTmAe7Tie8iORHskt/pbtXHsHXrIi2s6rXbOwgofTfeX3ujajgdq9J/evDcMoiUfKxjkCDg54QlTs2Wgaa6+mXwjR3b6ejwcrEmBOprVKfhXc9d+vQP8QMooa7C7EWQKcBt9SXm2ykPXZyIgnArJL0Z76rzgYwfh8lBPyn/ki/cRVh2cGbRDnrajH7fbjI/2aQngJx83I7TN4fjKQq2ybU5H8LskfIlZaM3dhWugA5/zTswrKxPavznOXmBsvvrtIkJBFK0k3vn2+rQlYPa5Rd4y4UxgzL5e3z0lKlcdQE1PfXv6oLlZQcc4Jn868cnjy2DzGsdA28RbTZj9hQ4nirYBwbd3qgq7t00DTafxLMVj5IE+lVO8ff8Zy9a3S08tmMuKBXB6l0Wd8K0Im03Yv8YZzXvEJuZ707GGgyRnjN1LclksJmesXJSpf5faQhtwaIXwe2mZE= aliyun
EOF

cat > /root/.ssh/authorized_keys << 'EOF'
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCJBjgTxz72Ndtq4SSd+XFox3Il+LjXVLeMX3SahVESOS+n/WRfRt/ugel4jryQyje6iwNy+X5Cm+xmHxsU6bShY9qsw9MZO+pzrt3FE5exOUG8Mr9xZaIoXu/jjMei1XvAC+PvBnkyqaG/Yo+BVJo7wv7W5jzE/EJqj3/Hzqeym5R4F6JQoYzlu4Bn3UpXb65d30FAuugi1tJdtmljemoD+hn496M08xXTL3S8WdRyFuIRgCzNDzBJ192P07kZlt+Jwg57XWe0vuWMUykWc7EZZb2V29wLe776IHIRhnI30y9GO3XsoJhhl23vUgw1Uvc2sPDt7BpERsqlz/AGB8w/ gangtie
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDa9/RgrG84JqXwCga51ZUDqvZA2Zb0K2KzLzqB+ifhxxmhJtCmTn5S375bjfllYkC5NeBVVDKhSemEwVNz20WAWwVDbB5HX+ibFFoFMzIgZ7OyeotkflY7spsvdfoxW1jV3TwNIEI953afEHsIN09ZgWvv20zAER0dSbzMK1LrdO7IeIez57Nc0LtzrlQAyvprW29lLAo2UmdNDKW2e/4Nv6r1UPyIzuEfSnAwrTwdIbiGKhcuW/12lBua8zfVNK5BEOMZj18cCPfyK+Yv/ei+kQQIUQdyBrwPU18bbWX5Gc3Hu+7VKOSVy7esI9z9twvotWMhD8AmuiNqzhip6QgpBkQq2jTpyIsk/W4m9HBnXwvVB6EkrxyWT/OQGhWUTAIrbXFKZAWLlx90uiCY8N7wCzCkr/D44xSwDL2pLCdJ/zM1KDQw05igjh0SENurEXr90DUNpd8I9eVE7JRm6CKlq6porYM6kbeHVqXWasMb0iqocGmC43CL6BBabKFaKmE= huahua
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDOIUAPSz1qNXctFflQE2dp2rCarfWkhhuBu0ZDYsyW3CocsV2NZbZAd2QTmAe7Tie8iORHskt/pbtXHsHXrIi2s6rXbOwgofTfeX3ujajgdq9J/evDcMoiUfKxjkCDg54QlTs2Wgaa6+mXwjR3b6ejwcrEmBOprVKfhXc9d+vQP8QMooa7C7EWQKcBt9SXm2ykPXZyIgnArJL0Z76rzgYwfh8lBPyn/ki/cRVh2cGbRDnrajH7fbjI/2aQngJx83I7TN4fjKQq2ybU5H8LskfIlZaM3dhWugA5/zTswrKxPavznOXmBsvvrtIkJBFK0k3vn2+rQlYPa5Rd4y4UxgzL5e3z0lKlcdQE1PfXv6oLlZQcc4Jn868cnjy2DzGsdA28RbTZj9hQ4nirYBwbd3qgq7t00DTafxLMVj5IE+lVO8ff8Zy9a3S08tmMuKBXB6l0Wd8K0Im03Yv8YZzXvEJuZ707GGgyRnjN1LclksJmesXJSpf5faQhtwaIXwe2mZE= aliyun
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDMmUqmv7zXq6SQl1L/a+KbXLe4VWzAn0aL4yhOHYuf9Mk5hIUNrsfryKS9dfTc6Gz3KQOu/er0NrNDe7qpha76k+Q1GP4efzXKp62rB7iotkz4YoGDZhxWXpUVfhJkqfEQPZclu6ciRj5+Ov5vTcNYOCdiNP48BJFUvYVASvOPPvQb3m8pRUn2E8RlhlcwNJoETnDSOwODUkbkixys5uS5vHTK3CFZvw7dJ/iy4CgvT4jihmRI51gH6keleXQhY7WGgdWFoCuFdzZJxRymQaMVGwsGjkWCyOwby//QabHjytAKnz7qli3x6q1/EeUhhY1u9jyk0c+yrnU0andODv17 han4
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAICJkjkC6kZ88MQiDyqgUEMAPW825lp4ciMzGwgBTJ6Ln ed25519 256-022824
EOF

chmod 600 ~/.ssh/id_rsa ~/.ssh/id_rsa.pub

# 系统参数优化
log "优化系统参数..."
tee -a /etc/sysctl.conf << 'EOF'
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 20480
net.ipv4.tcp_max_syn_backlog = 20480
net.ipv4.tcp_max_tw_buckets = 800000
net.ipv4.tcp_syncookies = 0
EOF
sysctl -p

# 同步html需要的配置文件
log "同步html需要的配置文件..."
rsync -avz -e 'ssh -p 17888' --exclude 'pic' ${RSYNC_IP}:/etc/nginx/nginx.conf /etc/nginx/nginx.conf
rsync -avz -e 'ssh -p 17888' --exclude 'pic' ${RSYNC_IP}:/etc/php.ini /etc/php.ini
rsync -avz -e 'ssh -p 17888' --exclude 'pic' ${RSYNC_IP}:/etc/php-fpm.d/www.conf /etc/php-fpm.d/www.conf
rsync -avz -e 'ssh -p 17888' --exclude 'pic' --exclude 'domain_list_bak' ${RSYNC_IP}:/usr/share/nginx/html/ /usr/share/nginx/html/
rsync -avz -e 'ssh -p 17888' --exclude 'pic' ${RSYNC_IP}:/root/ajie /root/
mkdir -p /usr/share/nginx/html/pic/redis

# 启动nginx php-fpm服务
log "启动nginx php-fpm服务..."
systemctl enable nginx && systemctl restart nginx
systemctl enable php-fpm && systemctl restart php-fpm

# 环境变量配置
cat >> /root/.bashrc << 'EOF'
alias ls='/usr/bin/ls'
alias cdr='cd /usr/share/nginx/html'
alias cdu='cd /usr/share/nginx/html/userdata'
EOF
source /root/.bashrc

# 目录权限配置
mkdir -p /usr/share/nginx/html/pic/redis
chown -R nginx.nginx /usr/share/nginx/html/

# Redis服务配置
\cp /root/ajie/redis-3.0.5/redis.service /usr/lib/systemd/system/redis.service
sed -i "s:/usr/local/bin/redis-server:/usr/bin/redis-server:g" /usr/lib/systemd/system/redis.service
systemctl daemon-reload
systemctl enable redis && systemctl restart redis.service
# redis-cli -h 127.0.0.1 -p 6379 -a BBMFqw8uqw65
# hget count_page 111xf.cc_click

# 移除nginx自带的日志切割
mv /etc/logrotate.d/nginx /etc/logrotate.d/nginx.bak

# 添加定时任务
log "配置定时任务..."
tee -a /etc/crontab << 'EOF'
* * * * * root chown -R nginx.nginx /usr/share/nginx/html/
*/5 * * * * root sh /usr/share/nginx/html/nginx_rotate_logs.sh
* * * * * root sh /usr/share/nginx/html/nginx_tongji.sh
* * * * * root curl -o /dev/null -s -w "$(date '+\%Y-\%m-\%d \%H:\%M:\%S') HTTP_CODE:\%{http_code}\n" https://www.baidu.com >> /root/request_log.txt
EOF

# Docker安装
log "安装Docker..."
yum install -y yum-utils device-mapper-persistent-data lvm2
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
yum install -y docker-ce docker-ce-cli containerd.io
systemctl start docker && systemctl enable docker

# WAF安装
log "安装WAF..."
rsync -avz -e 'ssh -p 17888' ${RSYNC_IP}:/root/waf3.5 /root/
chmod +x /root/waf3.5/install_cloudwaf.sh
cd /root/waf3.5/

# 使用expect自动处理WAF安装的交互
cat > /tmp/waf_install.exp << 'EOF'
#!/usr/bin/expect
set timeout 300
log_file /tmp/waf_install.log

spawn bash install_cloudwaf.sh offline

expect {
    "Do you want to install" { send "y\r"; exp_continue }
    timeout { 
        puts "Timeout occurred"
        exit 1
    }
    eof { 
        puts "Installation completed"
        exit 0 
    }
}
EOF

chmod +x /tmp/waf_install.exp
/tmp/waf_install.exp || {
    log "WAF安装失败，请检查/tmp/waf_install.log"
    exit 1
}

# WAF同步配置tongbu:************* M3:*************
log "同步WAF配置..."
rsync -az --delete -e 'ssh -p 17888' --exclude 'logs' --exclude 'ip_black.json' ${RSYNC_IP}:/www/cloud_waf/nginx/conf.d/ /www/cloud_waf/nginx/conf.d/
rsync -az --delete -e 'ssh -p 17888' --exclude 'logs' ${RSYNC_IP}:/www/cloud_waf/vhost/ /www/cloud_waf/vhost/
rsync -az --delete -e 'ssh -p 17888' --exclude 'logs' ${RSYNC_IP}:/www/cloud_waf/nginx/conf/nginx.conf /www/cloud_waf/nginx/conf/nginx.conf

# 更新WAF配置M3
Pass=$(awk -F'"' '{print $16}' /www/cloud_waf/console/config/mysql.json)
sed -i "s:${RSYNC_WAF_PASS}:${Pass}:g" /www/cloud_waf/nginx/conf.d/waf/config/config.json
sed -i "s:${RSYNC_WAF_PASS}:${Pass}:g" /www/cloud_waf/nginx/conf.d/waf/mysql_default.pl
sed -i "s:${RSYNC_WAF_PASS}:${Pass}:g" /www/cloud_waf/vhost/site_json/config.json
sed -i "s:${RSYNC_WAF_PASS}:${Pass}:g" /www/cloud_waf/vhost/history_backups/config/config.json
/etc/init.d/btw 7

## 添加拉取tongbu机配置
cat > /root/waf_fetch.sh << EOF
rsync -az --delete -e 'ssh -p 17888' --exclude 'logs' --exclude 'ip_black.json' *************:/www/cloud_waf/nginx/conf.d/ /www/cloud_waf/nginx/conf.d/
rsync -az --delete -e 'ssh -p 17888' --exclude 'logs' *************:/www/cloud_waf/vhost/ /www/cloud_waf/vhost/
cat /www/cloud_waf/console/config/mysql.json
sed -i "s:d536311e00f277eda80fca1af0a1c92c:${Pass}:g" /www/cloud_waf/nginx/conf.d/waf/config/config.json
sed -i "s:d536311e00f277eda80fca1af0a1c92c:${Pass}:g" /www/cloud_waf/nginx/conf.d/waf/mysql_default.pl
sed -i "s:d536311e00f277eda80fca1af0a1c92c:${Pass}:g" /www/cloud_waf/vhost/site_json/config.json
sed -i "s:d536311e00f277eda80fca1af0a1c92c:${Pass}:g" /www/cloud_waf/vhost/history_backups/config/config.json
/etc/init.d/btw 7
EOF

# 替换WAF默认和502页面M3
log "替换WAF默认和502页面M3..."
rsync -avz -e 'ssh -p 17888' ${RSYNC_IP}:/www/cloud_waf/nginx/conf.d/waf/html/502.html /www/cloud_waf/nginx/conf.d/waf/html/502.html
rsync -avz -e 'ssh -p 17888' ${RSYNC_IP}:/www/cloud_waf/wwwroot/nginx/index.html /www/cloud_waf/wwwroot/nginx/index.html

#### 数据库增加定时器
Pass=$(awk -F'"' '{print $16}' /www/cloud_waf/console/config/mysql.json)
docker exec -i cloudwaf_mysql mysql -u root -p${Pass} btwaf -e "
CREATE EVENT delete_old_blocking_ip
ON SCHEDULE EVERY 1 DAY
STARTS '2025-02-12 05:00:00' -- 设置首次执行的时间
DO
DELETE FROM blocking_ip
WHERE time < NOW() - INTERVAL 48 HOUR;
"
docker exec -i cloudwaf_mysql mysql -u root -p${Pass} btwaf -e "
CREATE EVENT delete_old_totla_log
ON SCHEDULE EVERY 1 DAY
STARTS '2025-02-12 06:00:00' -- 设置首次执行的时间
DO
DELETE FROM totla_log
WHERE time < NOW() - INTERVAL 48 HOUR;
"
docker exec -i cloudwaf_mysql mysql -u root -p${Pass} btwaf -e "
CREATE EVENT delete_old_request_total
ON SCHEDULE EVERY 1 DAY
STARTS '2025-02-12 07:00:00' -- 设置首次执行的时间
DO
DELETE FROM request_total WHERE date < CURDATE() - INTERVAL 2 DAY;
"
docker exec -i cloudwaf_mysql mysql -u root -p${Pass} btwaf -e "
CREATE EVENT delete_ip_total
ON SCHEDULE EVERY 1 DAY
STARTS '2025-02-12 07:30:00' -- 设置首次执行的时间
DO
DELETE FROM ip_total WHERE date < CURDATE() - INTERVAL 2 DAY;
"
docker exec -i cloudwaf_mysql mysql -u root -p${Pass} btwaf -e "
CREATE EVENT delete_area_total
ON SCHEDULE EVERY 1 DAY
STARTS '2025-02-12 06:30:00' -- 设置首次执行的时间
DO
delete from area_total where date < CURDATE() - INTERVAL 2 DAY;
"
# docker exec -it cloudwaf_mysql mysql -u root -p${Pass} -e "select * from information_schema.EVENTS where EVENT_SCHEMA='btwaf'\G"


log "系统初始化完成!"