#!/bin/bash
# curl https://get.acme.sh | sh -s email=<EMAIL>
# mv /root/.acme.sh/acme.sh /tmp/
# ln -s /root/ajie/yunwei/acme.sh /root/.acme.sh/acme.sh
# chmod +x /root/ajie/yunwei/acme.sh

site=$1
# 检查执行携带参数是否正常
if [ $# -lt 1 ]; then
    echo "Usage: $0 <arg1>"
    exit 1
fi

#if ! sh fetch_domains.sh ${site};then
#    echo "fetch_domains.sh failed. exiting..."
#    exit 1
#fi
if ! sh domains_check.sh ${site};then
    echo "domains_check.sh failed. exiting..."
    exit 1
fi
if ! sh issue_cert.sh ${site};then
    echo "issue_cert.sh failed. exiting..."
    exit 1
fi
if ! sh push_cert.sh ${site};then
    echo "push_cert.sh failed. exiting..."
    exit 1
fi
## ssh -p 17888 root@39.106.57.216 "sh /root/ajie/tongbu_site.sh joke80 1"
if ! ssh -p 17888 root@39.106.57.216 "sh /root/ajie/tongbu_site.sh ${site} 1";then
    echo "tongbu_site failed. exiting..."
    exit 1
fi
if [ $? -eq 0 ]; then
    echo "失败域名记录:"
    echo "================"
    cat failed_${site}
    echo "================"
fi

