# SSL证书自动化申请系统设计文档

## 项目概述
本项目旨在实现证书申请的自动化流程，通过Python3.8和Shell脚本在CentOS7服务器上完成域名获取、检测、申请证书及同步部署生产的全流程自动化操作。

## 技术栈
- 编程语言：Python 3.8+、Shell脚本
- 运行环境：CentOS 7
- 数据存储：Redis 3.2.12
- 通知机制：Telegram机器人（先不实现）
- 同步工具：rsync

## 设计思路
系统分为三个主要任务模块：
### 任务一：域名接收与处理模块
每2分钟读取域名接口，格式化处理后写入redis排序集合(domains_cert)
读取redis排序集合(domains_cert)进行过虑
  - 如果nginx配置已存在，则移除并写入redis集合(domains_exists)
  - 解析成功的域名写入redis队列，同时更新宝塔site信息，移除redis排序集合(domains_cert)
sleep 120秒。再次循环上面操作(请求接口--写入redis--读取redis过虑--写入队列)
### 任务二：读取队列生成证书模块
开启两个线程读取redis队列中不同site生成证书，同时标记当前各自site为锁状态
  - 开始生成{site}证书前，tg发送消息通知(开始生成{site}证书)
  - 成功则执行同步脚本(bt和waf)，同时tg发送通知({site}证书申请完成)
  - 失败则进入判断，查询redis队列中是否有当前{site}队列：
    - 有则丢弃当前{site}任务并直接开始下一个相同{site}任务，
        同时tg发送消息通知({site}申请失败，进入下一个相同{site}任务)
        成功则执行同步脚本，失败则继续判断。
    - 无则进入第二次重试，同时tg发送消息({site}申请失败，进入第2次重试)
  - 第2次再失败则解除{site}锁并丢弃当前任务，继续下一个队列任务，tg通知({site}第2次重试失败，人工确认)
  - 申请完成则请求success接口告之{site}及证书文件。
  - 进入等待直到新的redis队列任务。
### 任务三：证书续签模块(暂不实现)
读取宝塔bt所有证书小于10天的站点，顺序单个续签证书
  - 检查{site}域名状态，删除无效域名并更新至宝塔站点，然后提交生成证书
  - 成功则进入下一个{site}继续生成
  - 失败则进入第2次重试

## 详细设计
### 任务一：域名接收与处理模块

#### 1. 接收并保存域名
- **功能描述**：通过HTTP接口获取需要申请证书的域名列表
- **实现方式**：
  - 从配置的API URL（`http://38.145.216.14/222.txt`）获取域名文本
  - 域名格式为空格分隔的文本，如：`a.com b.com c.com d.com e.com`
  - 移除获取的域名文本首尾空白字符
  - 格式化处理后写入
  - 请求失败时记录错误日志

#### 2. 域名过滤与解析检测
- **功能描述**：对域名进行两轮过滤，确保只处理有效且需要申请证书的域名
- **实现方式**：
  - **第一轮过滤**：
    - 检查nginx配置中是否已存在该域名，需要精确匹配
    - 使用`grep -roP "(^|\s){domain}(\s|;|$)" /www/cloud_waf/nginx/conf.d/vhost/`命令
    - 只保留不存在于nginx配置中的域名
  - **第二轮过滤**：
    - 将第一轮过滤后的域名保存到临时文件
    - 调用外部脚本`/root/ajie/yunwei/auto_domains_check.sh`进行域名解析检测
    - 该脚本会自动过滤掉解析失败或IP不符合要求的域名
    - 从临时文件读取过滤后的最终有效域名列表

#### 3. Redis队列管理
- **功能描述**：使用Redis管理域名队列，将站点信息写入Redis结构
- **实现方式**：
  - **Redis存储结构**：
    - `auto_cert_domains`（Hash）：存储站点ID和对应的域名JSON数据
    - `auto_cert_queue`（List）：存储待处理的站点ID队列
    - `current_site_id`（String）：存储当前使用的站点ID
  - **域名添加流程**：
    - 调用`/root/ajie/yunwei/auto_get_key_values.sh`获取当前站点ID和已有域名列表（JSON格式）
    - 解析JSON数据，获取`site_id`和`domains`信息
    - 循环处理待添加域名：
      - 检查当前站点域名数量是否达到上限（默认100个）
      - 如未达到上限：将域名添加到当前站点，更新Redis Hash
      - 如已达上限：
        1. 将当前站点信息（JSON格式）提交到Redis队列
        2. 创建新站点ID（自动递增，如auto1 → auto2）
        3. 执行`/root/ajie/yunwei/auto_create_waf_site.sh {new_site_id}`创建新WAF站点
        4. 获取新站点信息，继续添加剩余域名
    - 所有域名处理完成后，将最后一个站点信息提交到Redis队列
  - **异常处理**：
    - 处理过程中的异常会被捕获并记录到日志
    - 使用JSON格式存储和传递站点信息，确保数据一致性

### 任务二：证书生成模块

#### 1. 取Redis参数生成证书

- **功能描述**：监测Redis队列，取出队列任务生成证书
- **实现方式**：
  - 实时监测Redis队列
  - 发现新任务时，从右取出一条队列中的key和values
  - 将values中的域名写入`${SITE}`临时文件
  - 使用key作为参数执行申请证书脚本`sh issue_cert.sh key`（已实现）
  - 成功后去除队列标记

#### 2. 写入WAF（先不生成）

#### 3. 写入宝塔（先不生成）

#### 4. 失败处理

- 生成失败时发送Telegram机器人消息告警（先不生成）
- 重新加入到Redis队列后面等待下一次生成（先不生成）

### 任务三：证书同步模块（先不生成）

#### 1. 执行同步

- **功能描述**：将生成的证书同步到各主机
- **实现方式**：
  - 证书成功生成并配置好WAF和宝塔后
  - 执行rsync同步命令到各主机（已实现）

## 已实现功能

- `domains_check.sh`：域名解析检测
- `issue_cert.sh`：申请证书脚本
- rsync同步功能

## 先不生成功能

- Telegram机器人告警逻辑
- 写入WAF功能
- 写入宝塔功能
- 证书生成失败后重新加入队列的逻辑

## 流程图

```
接口获取域名 → 域名过滤与检测 → 写入Redis队列 → 生成证书 → 配置WAF和宝塔 → 同步到各主机
```

## 注意事项

- Redis队列管理需要考虑并发情况
- 域名数量上限为每个站点100个
- 证书生成失败需要有完善的告警和重试机制