#!/bin/bash
site_name=$1
server_id=$2
# 构建文件路径
file_path="/www/cloud_waf/nginx/conf.d/vhost/${site_name}_com.conf"

if [ $# -lt 1 ]; then
    echo "Usage: $0 <arg1>"
    exit 1
fi
# 检查文件是否存在
if [ ! -f "$file_path" ]; then
    #echo "站点不存在!! 请执行创建站点!!"
    #exit 1
    echo "站点不存在!! 执行创建站点!!"
    sh /root/ajie/create_site.sh ${site_name}
fi
# 判断使用小舅1还是小舅2
if [ "$server_id" -eq 1 ]; then
    # 小舅1
    rsync -avz root@**************:/www/server/panel/vhost/nginx/${site_name}.com.conf /tmp/${site_name}.com/
    rsync -avz root@**************:/www/server/panel/vhost/cert/${site_name}.com/fullchain.pem /tmp/${site_name}.com/
    rsync -avz root@**************:/www/server/panel/vhost/cert/${site_name}.com/privkey.pem /tmp/${site_name}.com/
elif [ "$server_id" -eq 2 ]; then
    # 小舅2
    rsync -avz root@************:/www/server/panel/vhost/nginx/${site_name}.com.conf /tmp/${site_name}.com/
    rsync -avz root@************:/www/server/panel/vhost/cert/${site_name}.com/fullchain.pem /tmp/${site_name}.com/
    rsync -avz root@************:/www/server/panel/vhost/cert/${site_name}.com/privkey.pem /tmp/${site_name}.com/
else
    echo "无效的 server_id: $server_id。请填写正确的 server_id"
    exit 1
fi

\cp /tmp/${site_name}.com/fullchain.pem /www/cloud_waf/nginx/conf.d/cert/${site_name}_com/fullchain.pem
\cp /tmp/${site_name}.com/privkey.pem /www/cloud_waf/nginx/conf.d/cert/${site_name}_com/privkey.pem
python3.11 /root/ajie/site.py ${site_name}
#btw 7
sh /root/ajie/tongbu_waf.sh
