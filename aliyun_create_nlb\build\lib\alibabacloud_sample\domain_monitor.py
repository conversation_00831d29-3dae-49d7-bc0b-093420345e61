#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/10/28 18:33
# @Name    : domain_monitor.py
# @email   : <EMAIL>
# <AUTHOR> 钢铁知识库
import requests
import time
import sys
import logging
import hmac
import hashlib
import base64
import urllib.parse
'''
## nohup python /root/ajie/domain_monitor.py &
钉钉机器人告警
'''

domains = [
    'http://a6.jieruitech.info/123.txt',
    'http://a62.jieruitech.info/123.txt',
    'http://a63.jieruitech.info/123.txt',
    'http://x71.jieruitech.info/123.txt',
    'http://x73.jieruitech.info/123.txt',
    'http://x73.jieruitech.info/123.txt',
    'http://x74.jieruitech.info/123.txt',
    'http://x75.jieruitech.info/123.txt',
    'http://hg1.jieruitech.info/123.txt',
    'http://hg2.jieruitech.info/123.txt',
    'http://hg3.jieruitech.info/123.txt',
    'http://hg4.jieruitech.info/123.txt',
    'http://h41.jieruitech.info/123.txt',
    'http://h42.jieruitech.info/123.txt',
    'http://h43.jieruitech.info/123.txt',
    'http://h44.jieruitech.info/123.txt',
    'http://h45.jieruitech.info/123.txt',
    'http://h51.jieruitech.info/123.txt',
    'http://h52.jieruitech.info/123.txt',
    'http://h53.jieruitech.info/123.txt',
    'http://h54.jieruitech.info/123.txt',
    'http://h55.jieruitech.info/123.txt',
    'http://h56.jieruitech.info/123.txt',
    'http://h57.jieruitech.info/123.txt',
    'http://h61.jieruitech.info/123.txt',
    'http://h62.jieruitech.info/123.txt',
    'http://h63.jieruitech.info/123.txt',
    'http://h64.jieruitech.info/123.txt',
    'http://h65.jieruitech.info/123.txt',
    'http://h66.jieruitech.info/123.txt',
    'http://h67.jieruitech.info/123.txt',
    'http://r11.jieruitech.info/123.txt',
    'http://r12.jieruitech.info/123.txt',
    'http://r13.jieruitech.info/123.txt',
    'http://r14.jieruitech.info/123.txt',
    'http://r15.jieruitech.info/123.txt',
    'http://r91.jieruitech.info/123.txt',
    'http://r92.jieruitech.info/123.txt',
    'http://r93.jieruitech.info/123.txt',
    'http://r94.jieruitech.info/123.txt',
    'http://r95.jieruitech.info/123.txt',
    'http://r41.jieruitech.info/123.txt',
    'http://r42.jieruitech.info/123.txt',
    'http://r43.jieruitech.info/123.txt',
    'http://r44.jieruitech.info/123.txt',
    'http://r45.jieruitech.info/123.txt',
    'http://m11.jieruitech.info/123.txt',
    'http://m12.jieruitech.info/123.txt',
    'http://m13.jieruitech.info/123.txt',
    'http://m14.jieruitech.info/123.txt',
    'http://m15.jieruitech.info/123.txt',
    'http://m21.jieruitech.info/123.txt',
    'http://hongkonga.buyusdt.me/123.txt',
    'http://hongkongb.buyusdt.me/123.txt',
    'http://oscarb.buyusdt.me/123.txt',
    'http://oscara.buyusdt.me/123.txt',
    'http://oscarbigad.buyusdt.me/123.txt',
    'http://oscarad.buyusdt.me/123.txt',
    'http://oscarcpa.buyusdt.me/123.txt',
    'http://oscarjiechi.buyusdt.me/123.txt',
    'http://zs.buyusdt.me/123.txt',
    'http://oscarc.buyusdt.me/123.txt',
    'http://oscarccpa.buyusdt.me/123.txt',
    'http://oscarcjiechi.buyusdt.me/123.txt',
    'http://ali.buyusdt.me/123.txt',
]

# 配置日志输出到文件
log_file = 'monitor.log'
logging.basicConfig(filename=log_file, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# 用于将日志打印到终端：
# logging.getLogger().addHandler(logging.StreamHandler())

def get_digest():
    # 取毫秒级别时间戳，round(x, n) 取x小数点后n位的结果，默认取整
    timestamp = str(round(time.time() * 1000))
    secret = 'SECcadb06773f5af50923dd2f63f5f61aef2aa3cf98da5f2fd03cf834297869ec6c'
    secret_enc = secret.encode('utf-8')  # utf-8编码
    string_to_sign = '{}\n{}'.format(timestamp, secret)  # 字符串格式化拼接
    string_to_sign_enc = string_to_sign.encode('utf-8')  # utf-8编码
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()  # HmacSHA256算法计算签名
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))  # Base64编码后进行urlEncode
    #  返回时间戳和计算好的编码拼接字符串，后面直接拼接到Webhook即可
    return f"&timestamp={timestamp}&sign={sign}"


def warning_bot(message):
    data = {
        "msgtype": "text",
        "text": {
            "content": message
        },
    }
    # 机器人链接地址，发post请求 向钉钉机器人传递指令
    webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=9fb7741f17e86008209eee7a3db93dd32caa9b26369fa6633a10c3d25de2cb8f'
    # 利用requests发送post请求
    req = requests.post(webhook_url + get_digest(), json=data)
    print(req.status_code)


def check_domain(domain):
    try:
        response = requests.get(domain, timeout=15)
        response_code = response.status_code
        response_time = response.elapsed.total_seconds()
        return response_code, response_time
    except (requests.exceptions.RequestException, requests.exceptions.HTTPError):
        return None, None


def monitor_domains():
    timeout_count = 0
    recovering = False

    while True:
        for domain in domains:
            while True:
                response_code, response_time = check_domain(domain)
                current_time = time.strftime("%Y-%m-%d %H:%M:%S")

                if response_code != 200:  # 请求非200
                    timeout_count += 1
                    logging.info(f"{domain} 第 {timeout_count} 次请求失败啦！！")
                    if timeout_count == 3:  # 连续超时3次，输出报错告警
                        # print(f"[{current_time}] 域名 {domain} 请求3次超时，告警！！！")
                        logging.warning(f"域名 {domain} 请求3次超时，告警！！！")
                        message = f"域名 {domain} 请求3次超时，告警！！！"
                        warning_bot(message)
                        recovering = False
                    if timeout_count == 360:    ## 失败360后跳过，避免一直卡在这里
                        timeout_count = 0
                        logging.info(f"域名 {domain}，响应码：{response_code}，响应时间：{response_time} 秒")
                        break
                else:  # 请求成功
                    if timeout_count >= 3 and not recovering:  # 恢复后输出恢复
                        logging.warning(f"域名 {domain} 恢复正常！")
                        message = f"域名 {domain} 恢复正常！"
                        warning_bot(message)
                        recovering = True
                    timeout_count = 0
                    logging.info(f"域名 {domain}，响应码：{response_code}，响应时间：{response_time} 秒")
                    break
                time.sleep(20)  ## 如果失败，等20秒后再继续
            time.sleep(2)   ## 如果成功，等待3秒后直接下一条


if __name__ == '__main__':
    monitor_domains()

# 示例日志记录
# logging.debug('This is a debug message')
# logging.info('This is an info message')
# logging.warning('This is a warning message')
# logging.error('This is an error message')
# 打开日志文件，以追加模式写入
#                     with open('monitor.log', 'a') as file:
#                         # 重定向print输出到日志文件
#                         sys.stdout = file
#                         # 执行print语句
#                         print(f"[{current_time}] 域名 {domain}，响应码：{response_code}，响应时间：{response_time} 秒")
#                     # 恢复print输出到控制台
#                     sys.stdout = sys.__stdout__