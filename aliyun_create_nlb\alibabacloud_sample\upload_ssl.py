#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/9/3 1:44
# @Name    : upload_ssl.py
# @email   : <EMAIL>
# <AUTHOR> 钢铁知识库
# -*- coding: utf-8 -*-
import os
import sys

from typing import List

from alibabacloud_cas20200407.client import Client as cas20200407Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_cas20200407 import models as cas_20200407_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient

from alibabacloud_bssopenapi20171214.client import Client as BssOpenApi20171214Client


class UploadSSL:
    def __init__(self):
        self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tEXo5UWmpPuhHNuhy55'  # ali1
        self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'
        self.DNS = '.cn-hongkong.nlb.aliyuncs.com'

    @staticmethod
    def create_client(
            access_key_id: str,
            access_key_secret: str,
    ):
        config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
        config.endpoint = f'cas.aliyuncs.com'
        return cas20200407Client(config)

    @staticmethod
    def create_client2(
            access_key_id: str,
            access_key_secret: str,
    ):
        config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
        config.endpoint = f'business.aliyuncs.com'
        return BssOpenApi20171214Client(config)

    def ssl_get_detail(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        get_user_certificate_detail_request = cas_20200407_models.GetUserCertificateDetailRequest(
            cert_id=10999865
        )
        runtime = util_models.RuntimeOptions()
        try:
            res = client.get_user_certificate_detail_with_options(get_user_certificate_detail_request, runtime)
            print(res)
        except Exception as error:
            # 如有需要，请打印 error
            UtilClient.assert_as_string(error)
    def query_accout_balance(self): ## 查询余额
        client = self.create_client2(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        runtime = util_models.RuntimeOptions()
        try:
            print(client.query_account_balance_with_options(runtime).body)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def ssl_0033(self):
            client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
            upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
                name='0033-737dd.tv_101xf.xyz...',
                cert='''
    -----BEGIN CERTIFICATE-----
    MIII2jCCB8KgAwIBAgISBH4V526Pb6Ph6xMpn5ca90sIMA0GCSqGSIb3DQEBCwUA
    MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
    EwJSMzAeFw0yMzEyMjEyMjA4MTdaFw0yNDAzMjAyMjA4MTZaMBMxETAPBgNVBAMT
    CDczN2RkLnR2MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqg95xWwD
    QlhVJmGWPUVWJplDV5hUQjiRZRY83AWYDaRkjn5VDhW9NtOmEHQvYzp3zmTmsZDe
    aDhiKBTCtjK4Xr6K0UQHRUNJhf/mvadNCm0kx4Xl9DXkvFymiDp4wKMbaEMmzpnP
    xaGOO62snh0i4SxvfT3fxSRIm8eWWeMdl0+uE7vgCdUE/PTxr4XOXbRiRxqUW30w
    rPhqKipjXcfmguixuxikmPTDrSKVFBZKoEfUT5f3OqTVrXK6mf1ScvGpDEuTjFZv
    nyKbrlISJjHMDJ0fadrS+OGq0+tUbkm03i8lBCfV2cHYRL7RUojUEnvHg+2lKCNx
    jPhLb7ERESHiwwIDAQABo4IGBzCCBgMwDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQW
    MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBTr
    lrLyzg+PykpQEvVkdnH2fZOZADAfBgNVHSMEGDAWgBQULrMXt1hWy65QCUDmH6+d
    ixTCxjBVBggrBgEFBQcBAQRJMEcwIQYIKwYBBQUHMAGGFWh0dHA6Ly9yMy5vLmxl
    bmNyLm9yZzAiBggrBgEFBQcwAoYWaHR0cDovL3IzLmkubGVuY3Iub3JnLzCCBA0G
    A1UdEQSCBAQwggQAggkxMDF4Zi54eXqCCTEwMnhmLnh5eoIJMTAzeGYueHl6ggkx
    MDR4Zi54eXqCCTEwNXhmLnh5eoIJMTA2eGYueHl6ggkxMDd4Zi54eXqCCTEwOHhm
    Lnh5eoIJMTA5eGYueHl6ggkxMTB4Zi54eXqCCTExNHhmLnh5eoIJMTE1eGYueHl6
    ggkxMTZ4Zi54eXqCCTExN3hmLnh5eoIJMTE4eGYueHl6ggkxMTl4Zi54eXqCCTEy
    MHhmLnh5eoIJMTIxeGYueHl6ggkxMjJ4Zi54eXqCCTEyM3hmLnh5eoIHMzFvcy5j
    Y4IHMzJvcy5jY4IHMzRvcy5jY4IHMzVvcy5jY4IHMzZvcy5jY4IHMzdvcy5jY4IH
    Mzhvcy5jY4IHMzlvcy5jY4IHNDBvcy5jY4IHNDFvcy5jY4IHNDJvcy5jY4IHNDNv
    cy5jY4IHNDRvcy5jY4IHNDVvcy5jY4IHNDZvcy5jY4IHNDdvcy5jY4IHNDhvcy5j
    Y4IHNDlvcy5jY4IHNTBvcy5jY4IHNTFvcy5jY4IINzM3YWEudHaCCDczN2JiLnR2
    ggg3MzdjYy50doIINzM3ZGQudHaCCDczN2VlLnR2ggg3MzdmZi50doIINzM3Z2cu
    dHaCCDczN2hoLnR2ggg3MzdpaS50doIINzM3amoudHaCCDczN2trLnR2ggg3Mzds
    bC50doIINzM3bW0udHaCCDczN25uLnR2ggg3Mzdvby50doIINzM3cHAudHaCCDcz
    N3FxLnR2ggg3Mzdyci50doIINzM3c3MudHaCCDczN3R0LnR2ggg3Mzd1dS50doII
    NzM3dnYudHaCCDczN3d3LnR2ggg3Mzd4eC50doIINzM3eXkudHaCCDczN3p6LnR2
    ggx3d3cuNzM3YWEudHaCDHd3dy43MzdiYi50doIMd3d3LjczN2NjLnR2ggx3d3cu
    NzM3ZGQudHaCDHd3dy43MzdlZS50doIMd3d3LjczN2ZmLnR2ggx3d3cuNzM3Z2cu
    dHaCDHd3dy43MzdoaC50doIMd3d3LjczN2lpLnR2ggx3d3cuNzM3amoudHaCDHd3
    dy43Mzdray50doIMd3d3LjczN2xsLnR2ggx3d3cuNzM3bW0udHaCDHd3dy43Mzdu
    bi50doIMd3d3LjczN29vLnR2ggx3d3cuNzM3cHAudHaCDHd3dy43MzdxcS50doIM
    d3d3LjczN3JyLnR2ggx3d3cuNzM3c3MudHaCDHd3dy43Mzd0dC50doIMd3d3Ljcz
    N3V1LnR2ggx3d3cuNzM3dnYudHaCDHd3dy43Mzd3dy50doIMd3d3LjczN3h4LnR2
    ggx3d3cuNzM3eXkudHaCDHd3dy43Mzd6ei50djATBgNVHSAEDDAKMAgGBmeBDAEC
    ATCCAQUGCisGAQQB1nkCBAIEgfYEgfMA8QB2AEiw42vapkc0D+VqAvqdMOscUgHL
    Vt0sgdm7v6s52IRzAAABjI6jBk4AAAQDAEcwRQIhAPVro2fPS+sbcn8VXBO1ysRb
    jB/7sJAGsLq4dxsQN81WAiB6YeKyCqHCZ2oSruSldcn19ICyxU9rQTWK6i/r6laZ
    0AB3ADtTd3U+LbmAToswWwb+QDtn2E/D9Me9AA0tcm/h+tQXAAABjI6jBlwAAAQD
    AEgwRgIhAL44LTg/n3e14CfrQw7VmWrUqf0gG7jxbxMaijFgb6gFAiEAxJsmyHq2
    y95rYxAG1Rsjwc1qcrf86TryNjcGKxHkEu8wDQYJKoZIhvcNAQELBQADggEBACnp
    tFavgdIVendBtQwhMp7Z1ULXxQ7Vu/Z5WEonQa3P6A7bPlhODVKJdZWPUBqKinbb
    xoia9tKY+QTv2n4+pC13F1pynIYJeVYW6//RohjKvgUXbDOPbw4Bt7kw5Y687OSO
    37BTYZBlnMUiEXf0dCaIYbtGhzbYs4GA0ZNRp4Z8nJ+OVp2tka0l9CWRCKQ1aGnB
    Cg+qsY5G7Tn0jWBL1MqyjaU/DirFBw0IpZic2v77i81CBmD+y2v+sCZvOv6CWyzW
    /71jDGXuMT4r+Hjnsrqys5G1iXiOeNAthlZIO9mecj+PKPmxd7lFL40ibD42vTNg
    K5UKId3LGOWOdcZ+AQI=
    -----END CERTIFICATE-----

    -----BEGIN CERTIFICATE-----
    MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
    TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
    cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
    WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
    RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
    AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
    R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
    sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
    NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
    Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
    /kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
    AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
    Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
    FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
    AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
    Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
    gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
    PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
    ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
    CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
    lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
    avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
    yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
    yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
    hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
    HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
    MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
    nLRbwHOoq7hHwg==
    -----END CERTIFICATE-----

    -----BEGIN CERTIFICATE-----
    MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
    MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
    DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
    TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
    cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
    AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
    ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
    wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
    LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
    4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
    bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
    sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
    Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
    FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
    SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
    PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
    TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
    SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
    c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
    +tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
    ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
    b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
    U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
    MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
    5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
    9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
    WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
    he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
    Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
    -----END CERTIFICATE-----
                ''',
                key='''
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                '''
            )
            runtime = util_models.RuntimeOptions()
            resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
            print(resp)
    def ssl_0036(self):
            client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
            upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
                name='0036',
                cert='''
    -----BEGIN CERTIFICATE-----
    MIIIRTCCBy2gAwIBAgISA1FEuE+yyWB3wKTk1hIjCBTaMA0GCSqGSIb3DQEBCwUA
    MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
    EwJSMzAeFw0yNDAxMDIwOTI5MDNaFw0yNDA0MDEwOTI5MDJaMBMxETAPBgNVBAMT
    CDczN2JiLnB3MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7bW3jcLp
    2v5go0VI5vWSEg0n6NN08Hjy4MQ1b9yUOBIxAm9OT+FOzN8Yi0IQdjSQC1QDiFbR
    9i/WYiRSFB/Yu6cL0iYrIzz54deEJBcdbplpUtUuJ/1ykt17daIUc4PIoLdm0mlt
    hwEXZYSKtagqpLKED98xsNs45oafZy+JJHa5Jg9ZDHn/UlSuD3Fsr57PJi30FbzA
    C9C4KnoUHQYohcxhItHxEb8l0rUfVt3J4GA1r7rtI4xzvbZBMiHcxAncdSahZwBG
    lmnk8I2TVqE+8LHkodPGAqZqC2N7WN41+Iy+wgXd6uJiZx/sebPdBlhCjuKoYSfs
    1K4/62Uub+pTewIDAQABo4IFcjCCBW4wDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQW
    MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBR3
    olyYwRCarU9nbHnkKLC/S5sVEzAfBgNVHSMEGDAWgBQULrMXt1hWy65QCUDmH6+d
    ixTCxjBVBggrBgEFBQcBAQRJMEcwIQYIKwYBBQUHMAGGFWh0dHA6Ly9yMy5vLmxl
    bmNyLm9yZzAiBggrBgEFBQcwAoYWaHR0cDovL3IzLmkubGVuY3Iub3JnLzCCA3kG
    A1UdEQSCA3AwggNsgggxMTZ4Zi5jY4IJMTI0eGYueHl6ggkxMjV4Zi54eXqCCTEy
    NnhmLnh5eoIJMTI3eGYueHl6ggkxMjh4Zi54eXqCCTEyOXhmLnh5eoIJMTMweGYu
    eHl6ggkxMzF4Zi54eXqCCTEzMnhmLnh5eoIJMTMzeGYueHl6ggkxMzR4Zi54eXqC
    CTEzNXhmLnh5eoIJMTM2eGYueHl6ggkxMzd4Zi54eXqCCTEzOHhmLnh5eoIJMTM5
    eGYueHl6ggkxNDB4Zi54eXqCCTE0MXhmLnh5eoIJMTQyeGYueHl6ggkxNDN4Zi54
    eXqCCTE0NHhmLnh5eoIJMTQ1eGYueHl6ggkxNDZ4Zi54eXqCCTE0N3hmLnh5eoIJ
    MTQ4eGYueHl6ggkxNDl4Zi54eXqCCTE1MHhmLnh5eoIJMTUxeGYueHl6ggkxNTJ4
    Zi54eXqCCTE1M3hmLnh5eoIHMzgybC5wd4IHNzM3YS5wd4IINzM3YWEucHeCBzcz
    N2IucHeCCDczN2JiLnB3ggc3MzdjLnB3ggg3MzdjYy5wd4IHNzM3ZC5wd4IINzM3
    ZGQucHeCBzczN2UucHeCCDczN2VlLnB3ggc3MzdmLnB3ggg3MzdmZi5wd4IHNzM3
    Zy5wd4IINzM3Z2cucHeCBzczN2gucHeCCDczN2hoLnB3ggc3MzdpLnB3ggg3Mzdp
    aS5wd4IHNzM3ai5wd4IINzM3amoucHeCBzczN2sucHeCCDczN2trLnB3ggc3Mzds
    LnB3ggg3MzdsbC5wd4IHNzM3bS5wd4IINzM3bW0ucHeCBzczN24ucHeCCDczN25u
    LnB3ggc3MzdvLnB3ggg3Mzdvby5wd4IHNzM3cC5wd4IINzM3cHAucHeCBzczN3Eu
    cHeCCDczN3FxLnB3ggc3MzdyLnB3ggg3Mzdyci5wd4IHNzM3cy5wd4IINzM3c3Mu
    cHeCBzczN3QucHeCCDczN3R0LnB3ggc3Mzd1LnB3ggg3Mzd1dS5wd4IHNzM3di5w
    d4IINzM3dnYucHeCBzczN3cucHeCCDczN3d3LnB3ggc3Mzd4LnB3ggg3Mzd4eC5w
    d4IHNzM3eS5wd4IINzM3eXkucHeCBzczN3oucHeCCDczN3p6LnB3ggh3LTczNy50
    doIJd3ctNzM3LnR2ggp3d3ctNzM3LnR2MBMGA1UdIAQMMAowCAYGZ4EMAQIBMIIB
    BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHUAO1N3dT4tuYBOizBbBv5AO2fYT8P0x70A
    DS1yb+H61BcAAAGMybg78wAABAMARjBEAiATvT6tJcpPJJw12jE4iOt1iODjLIQ7
    GTKj7mGG6CAb4AIgaETu3gJvXRlno+b2GXWgX45kGmTpdWX1s3AAwIx+aQUAdwB2
    /4g/Crb7lVHCYcz1h7o0tKTNuyncaEIKn+ZnTFo6dAAAAYzJuDw+AAAEAwBIMEYC
    IQCmjtDQ8Br/seN6ECTW+m9oH7M+45iv/J839hiVU2cQ+gIhANEVNT2DEANkTvyG
    PQuKE9LFnIORIpoTbL6/yDwZcQQ7MA0GCSqGSIb3DQEBCwUAA4IBAQAZs75SYGyz
    1jFkHLRqkAWnLoMj5DJx3sys+D19lX6bQWf2r3AfigB1R7y6EWOhEt/XxCz+KZzU
    hghjLA1katRamhiRFooEdERAdYRzueGqYUzdQ6BO60I+awDPvvNQNx1kZa+tKmqd
    nUHkbmIr83IcazS51CciJ27ozQwLiWCQbOI+LIFK9XH+eagHNHY61wCiar4Nu5rd
    ZSRm7vrvjZQE9xaG4ParD6o1d3oY+QG0p5rDmcu1g5uVenuQkR913G4nNYffxlTN
    RGqp6p71O9PtB48fn4cz1DmEMlz14nZpQDUcmFLj4mtF05ZPXhWLV9yi51rK+4Rj
    NxhmgjjhuUiP
    -----END CERTIFICATE-----

    -----BEGIN CERTIFICATE-----
    MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
    TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
    cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
    WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
    RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
    AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
    R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
    sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
    NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
    Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
    /kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
    AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
    Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
    FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
    AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
    Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
    gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
    PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
    ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
    CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
    lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
    avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
    yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
    yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
    hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
    HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
    MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
    nLRbwHOoq7hHwg==
    -----END CERTIFICATE-----

    -----BEGIN CERTIFICATE-----
    MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
    MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
    DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
    TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
    cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
    AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
    ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
    wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
    LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
    4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
    bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
    sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
    Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
    FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
    SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
    PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
    TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
    SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
    c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
    +tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
    ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
    b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
    U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
    MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
    5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
    9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
    WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
    he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
    Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
    -----END CERTIFICATE-----
                    ''',
                key='''
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                    '''
            )
            runtime = util_models.RuntimeOptions()
            resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
            print(resp)
    def ssl_0037(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0037',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0038(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0038',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0039(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0039',
            cert='''
-----BEGIN CERTIFICATE-----
MIIIcTCCB1mgAwIBAgISAzX0EdgnF9TdWewf4VV+9HWOMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAxMDExNjIyNTFaFw0yNDAzMzExNjIyNTBaMBMxETAPBgNVBAMT
CDExOG9zLmNjMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxi4z0hcl
GYulfTOmvHwoY0kQB57MdmyODBDCKT+3R53ZUf/Br9AmukKhY79NRnNlewqKtbp2
/Sn10C1PaVSiXBleqCiZPBBQzBeCHsvgIEmqkSaHD2zT8tfPfLzxyzW9la89G+p1
fgqint9dyvWBceHCCAnT2+FO2+GaMZpC40zpeX5gjzYsNqBbBhFpbGwRZRPUA0py
ONBLQ5O567zq6j+VUppwNLbsD6Lidk44dImEETCErmeesfRJBK7wi0hwvuYigQqC
+mmZRSPww4c2WYxN/Uxxq4CMoJwQv6JfwhQBdycNeURUl3VFyPsVYYLdxXBBzCWw
ofH2JnAAn+X6SwIDAQABo4IFnjCCBZowDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQW
MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBTM
pTDm/iZ0RkvmJZL6Vepu9/bmSzAfBgNVHSMEGDAWgBQULrMXt1hWy65QCUDmH6+d
ixTCxjBVBggrBgEFBQcBAQRJMEcwIQYIKwYBBQUHMAGGFWh0dHA6Ly9yMy5vLmxl
bmNyLm9yZzAiBggrBgEFBQcwAoYWaHR0cDovL3IzLmkubGVuY3Iub3JnLzCCA6UG
A1UdEQSCA5wwggOYgggxMDB4Zi5jY4IIMTE4b3MuY2OCCDExOW9zLmNjgggxMjBv
cy5jY4IIMTIxb3MuY2OCCDEyMm9zLmNjgggxMjNvcy5jY4IIMTI0b3MuY2OCCDEy
NW9zLmNjgggxMjdvcy5jY4IIMTI4b3MuY2OCCDEzMG9zLmNjgggxMzFvcy5jY4II
MTMyb3MuY2OCCDEzM29zLmNjgggxMzRvcy5jY4IIMTM1b3MuY2OCCDEzNm9zLmNj
gggxMzdvcy5jY4IIMTM4b3MuY2OCCDEzOW9zLmNjgggxNDBvcy5jY4IIMTQxb3Mu
Y2OCCDE0Mm9zLmNjgggxNDNvcy5jY4IIMTQ0b3MuY2OCCDE0NW9zLmNjgggxNDZv
cy5jY4IIMTQ3b3MuY2OCCDE0OG9zLmNjgggxNDlvcy5jY4IIMTUwb3MuY2OCCDE1
MW9zLmNjgggxNTJvcy5jY4IIMTUzb3MuY2OCCDE1NG9zLmNjgggxNTVvcy5jY4II
MTU2b3MuY2OCCDE1N29zLmNjgggxNThvcy5jY4IIMTU5b3MuY2OCCDE2MG9zLmNj
gggxNjFvcy5jY4IIMTYyb3MuY2OCCDE2M29zLmNjgggxNjRvcy5jY4IIMTY1b3Mu
Y2OCCDE2Nm9zLmNjgggxODF4Zi5jY4IIMjgyODgubmyCBzM4MmoucHeCCDU3NTUx
LnR2ggg1NzU3Ny5ubIIINTg1ODgubmyCCDU5NTk5Lm5sggc2OXhmLmNvggx3d3cu
MTAweGYuY2OCDHd3dy4xODF4Zi5jY4IMd3d3LjI4Mjg4Lm5sggx3d3cuNTc1Nzcu
bmyCDHd3dy41ODU4OC5ubIIMd3d3LjU5NTk5Lm5sggt3d3cuNjl4Zi5jb4INd3d3
LnhpYW5nZi5ubIIJeGlhbmdmLm5sgh14bi0tNHFycjk1YnU4NmF0ZGEueG4tLTU1
cXg1ZIIceG4tLTRxcnI5NWJ2dXR6d20ueG4tLTU1cXg1ZIIbeG4tLTRxcnQ3NWIy
YnoxZi54bi0tZmlxczhzghx4bi0tNHFydTc0YXF2ZHM5dy54bi0tZmlxczhzghx4
bi0tYjB0OTRmNTJmYm03Yi54bi0tNTVxeDVkght4bi0tY2pyc3k0MDNiZnZiLnhu
LS01NXF4NWSCHHhuLS1jdnM0MmN0M3IxMjRjLnhuLS01NXF4NWSCHHhuLS1kcHF6
NndqeGE2NzVwLnhuLS01NXF4NWQwEwYDVR0gBAwwCjAIBgZngQwBAgEwggEEBgor
BgEEAdZ5AgQCBIH1BIHyAPAAdwA7U3d1Pi25gE6LMFsG/kA7Z9hPw/THvQANLXJv
4frUFwAAAYzGDLo9AAAEAwBIMEYCIQCXZytZsN4WjIhFQCMGu6rb67gaqvjq7FnP
AQnj620KugIhAIWYAlkRZoaifBRKPIHKLOjyrIcipXEBBgtc/AP56VG4AHUAKdA6
G7Z0qnEc0wNbZVfBT4qni0/oOJRJ7KRT+US9JGgAAAGMxgy7CQAABAMARjBEAiAC
U1xZyLjyQgyT7Q/Ct8ke+OkWFuwsbgFM4pIppNhQXAIgNGtd/474LTtUcaz2U/fc
9/oV2H4JPusGrs8mmhGuIEgwDQYJKoZIhvcNAQELBQADggEBAD6OPZY5tsRX6Tmb
sd3fa5FuIHWP3jNSgA7/XT3zzxj+Mkp6asNZ7PGN6r2apC209QsUpYns5nByh+f+
Bek6UShC4pnGkJS9HXj2xmHEmkrN2750eFyVbsDsemJ8AJinNE2+p9F2CDXudQ8g
Uk/AVZOMLLNc8VHpNNjfgyoJI6WqAkN47rbsCIsdM8hHC1BrpuAyS4BKFM+CLKuY
LnT20AwAAZd0qXcbYCuc53yds5HbKOKSAFMZtYwuJYSYaVAFq3R3fnpwuoK3ZMUi
44kOgfTpkt+1HoNS3Itht6Kwc/EnWUSCMF46uMXnDI60Dg/pqrwnQtryHjEuI1GT
fO2ZCcw=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0040(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0040',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0041(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0041',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0042(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0042',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0043(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0043',
            cert='''
-----BEGIN CERTIFICATE-----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==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0044(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='44',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0046(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='46',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0047(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='47',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0049(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='49',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0050(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0050',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0051(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='51',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0052(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='52',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0053(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0053',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0054(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0054',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0055(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='55',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0056(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='56',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0057(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='57',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0058(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='58',
            cert='''
-----BEGIN CERTIFICATE-----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==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db001(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db001',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db002(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db002',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db003(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db003',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db004(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db004',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIFgTCCBGmgAwIBAgIQOXJEOvkit1HX02wQ3TE1lTANBgkqhkiG9w0BAQwFADB7
MQswCQYDVQQGEwJHQjEbMBkGA1UECAwSR3JlYXRlciBNYW5jaGVzdGVyMRAwDgYD
VQQHDAdTYWxmb3JkMRowGAYDVQQKDBFDb21vZG8gQ0EgTGltaXRlZDEhMB8GA1UE
AwwYQUFBIENlcnRpZmljYXRlIFNlcnZpY2VzMB4XDTE5MDMxMjAwMDAwMFoXDTI4
MTIzMTIzNTk1OVowgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpOZXcgSmVyc2V5
MRQwEgYDVQQHEwtKZXJzZXkgQ2l0eTEeMBwGA1UEChMVVGhlIFVTRVJUUlVTVCBO
ZXR3b3JrMS4wLAYDVQQDEyVVU0VSVHJ1c3QgUlNBIENlcnRpZmljYXRpb24gQXV0
aG9yaXR5MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAgBJlFzYOw9sI
s9CsVw127c0n00ytUINh4qogTQktZAnczomfzD2p7PbPwdzx07HWezcoEStH2jnG
vDoZtF+mvX2do2NCtnbyqTsrkfjib9DsFiCQCT7i6HTJGLSR1GJk23+jBvGIGGqQ
Ijy8/hPwhxR79uQfjtTkUcYRZ0YIUcuGFFQ/vDP+fmyc/xadGL1RjjWmp2bIcmfb
IWax1Jt4A8BQOujM8Ny8nkz+rwWWNR9XWrf/zvk9tyy29lTdyOcSOk2uTIq3XJq0
tyA9yn8iNK5+O2hmAUTnAU5GU5szYPeUvlM3kHND8zLDU+/bqv50TmnHa4xgk97E
xwzf4TKuzJM7UXiVZ4vuPVb+DNBpDxsP8yUmazNt925H+nND5X4OpWaxKXwyhGNV
icQNwZNUMBkTrNN9N6frXTpsNVzbQdcS2qlJC9/YgIoJk2KOtWbPJYjNhLixP6Q5
D9kCnusSTJV882sFqV4Wg8y4Z+LoE53MW4LTTLPtW//e5XOsIzstAL81VXQJSdhJ
WBp/kjbmUZIO8yZ9HE0XvMnsQybQv0FfQKlERPSZ51eHnlAfV1SoPv10Yy+xUGUJ
5lhCLkMaTLTwJUdZ+gQek9QmRkpQgbLevni3/GcV4clXhB4PY9bpYrrWX1Uu6lzG
KAgEJTm4Diup8kyXHAc/DVL17e8vgg8CAwEAAaOB8jCB7zAfBgNVHSMEGDAWgBSg
EQojPpbxB+zirynvgqV/0DCktDAdBgNVHQ4EFgQUU3m/WqorSs9UgOHYm8Cd8rID
ZsswDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQFMAMBAf8wEQYDVR0gBAowCDAG
BgRVHSAAMEMGA1UdHwQ8MDowOKA2oDSGMmh0dHA6Ly9jcmwuY29tb2RvY2EuY29t
L0FBQUNlcnRpZmljYXRlU2VydmljZXMuY3JsMDQGCCsGAQUFBwEBBCgwJjAkBggr
BgEFBQcwAYYYaHR0cDovL29jc3AuY29tb2RvY2EuY29tMA0GCSqGSIb3DQEBDAUA
A4IBAQAYh1HcdCE9nIrgJ7cz0C7M7PDmy14R3iJvm3WOnnL+5Nb+qh+cli3vA0p+
rvSNb3I8QzvAP+u431yqqcau8vzY7qN7Q/aGNnwU4M309z/+3ri0ivCRlv79Q2R+
/czSAaF9ffgZGclCKxO/WIu6pKJmBHaIkU4MiRTOok3JMrO66BQavHHxW/BBC5gA
CiIDEOUMsfnNkjcZ7Tvx5Dq2+UUTJnWvu6rvP3t3O9LEApE9GQDTF1w52z97GA1F
zZOFli9d31kWTz9RvdVFGD/tSo7oBmF0Ixa1DVBzJ0RHfxBdiSprhTEUxOipakyA
vGp4z7h/jnZymQyd/teRCBaho1+V
-----END CERTIFICATE-----
            ''',
            key='''
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0059(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='59',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0060(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='60',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0061(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='61',
            cert='''
-----BEGIN CERTIFICATE-----
MIII6jCCB9KgAwIBAgISA7+kRdPNe6HgTrHOnm6suBPXMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAxMDIxMDMyNTBaFw0yNDA0MDExMDMyNDlaMBMxETAPBgNVBAMT
CGpzemIxLmNjMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoCiK8vhm
5VNgBrtBVBcXzGJyNQH6R6uqtSGSKfOuj4I7wLKs6AijTYOJot8el1jzjY3Xjl80
+7J+D51bkSBhXCbUVpAsTN3eymVQ0q3vjwKVqMXoboJvcU5ZYAu3mVTXM3lokbRL
3z9toTojFZEVdllnujN4L+7baEtzr98ofnF1AMfb1RmFi0obdn8NDfRYRj5oUvxo
1x5d4e1aIZUeVDU9kG4N01+I1pxWQCDFOBE7gR6l3FAa//xgIEJ+sQ47NGGt3+HU
oTJZkdN0cJKkTQLThGa4PbQ0TIigd2QvM2qIm4j/uZrjy6Un0LHNLVbwgDOfBb2a
tERyxdNbzRAWSQIDAQABo4IGFzCCBhMwDgYDVR0PAQH/BAQDAgWgMB0GA1UdJQQW
MBQGCCsGAQUFBwMBBggrBgEFBQcDAjAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBTY
5mPUzSY4HP3EMxmGDbU37lVLUTAfBgNVHSMEGDAWgBQULrMXt1hWy65QCUDmH6+d
ixTCxjBVBggrBgEFBQcBAQRJMEcwIQYIKwYBBQUHMAGGFWh0dHA6Ly9yMy5vLmxl
bmNyLm9yZzAiBggrBgEFBQcwAoYWaHR0cDovL3IzLmkubGVuY3Iub3JnLzCCBB0G
A1UdEQSCBBQwggQQggczODNhLmNjgggzODNhYS5jY4IIMzgzYmIuY2OCCDM4M2Rk
LmNjggczODNlLmNjgggzODNlZS5jY4IIMzgzZmYuY2OCBzM4M2cuY2OCCDM4M2dn
LmNjgggzODNoaC5jY4IHMzgzaS5jY4IIMzgzaWkuY2OCBzM4M2ouY2OCCDM4M2pq
LmNjgggzODNray5jY4IHMzgzbC5jY4IIMzgzbGwuY2OCCDM4M21tLmNjgggzODNu
bi5jY4IHMzgzby5jY4IIMzgzb28uY2OCCDM4M3BwLmNjggczODNxLmNjgggzODNx
cS5jY4IHMzgzci5jY4IIMzgzcnIuY2OCCDM4M3NzLmNjggczODN0LmNjgggzODN0
dC5jY4IIMzgzdXUuY2OCBzM4M3YuY2OCCDM4M3Z2LmNjgggzODN3dy5jY4IIMzgz
eHguY2OCBzM4M3kuY2OCCDM4M3l5LmNjggczODN6LmNjgggzODN6ei5jY4IIanN6
YjEuY2OCCWpzemIxMC5jY4IJanN6YjExLmNjgglqc3piMTIuY2OCCWpzemIxMy5j
Y4IJanN6YjE0LmNjgglqc3piMTUuY2OCCWpzemIxNi5jY4IJanN6YjE3LmNjgglq
c3piMTguY2OCCWpzemIxOS5jY4IIanN6YjIuY2OCCWpzemIyMC5jY4IJanN6YjIx
LmNjgglqc3piMjIuY2OCCWpzemIyMy5jY4IJanN6YjI0LmNjgglqc3piMjUuY2OC
CWpzemIyNi5jY4IJanN6YjI3LmNjgglqc3piMjguY2OCCWpzemIyOS5jY4IIanN6
YjMuY2OCCWpzemIzMC5jY4IJanN6YjMxLmNjgglqc3piMzIuY2OCCWpzemIzMy5j
Y4IJanN6YjM0LmNjgglqc3piMzUuY2OCCWpzemIzNi5jY4IJanN6YjM3LmNjgglq
c3piMzguY2OCCWpzemIzOS5jY4IIanN6YjQuY2OCCWpzemI0MC5jY4IJanN6YjQx
LmNjgglqc3piNDIuY2OCCWpzemI0My5jY4IJanN6YjQ0LmNjgglqc3piNDUuY2OC
CWpzemI0Ni5jY4IJanN6YjQ3LmNjgglqc3piNDkuY2OCCGpzemI1LmNjgglqc3pi
NTAuY2OCCWpzemI1MS5jY4IJanN6YjUyLmNjgglqc3piNTMuY2OCCWpzemI1NC5j
Y4IJanN6YjU1LmNjgglqc3piNTYuY2OCCWpzemI1Ny5jY4IJanN6YjU4LmNjgglq
c3piNTkuY2OCCGpzemI2LmNjgglqc3piNjAuY2OCCWpzemI2MS5jY4IJanN6YjYy
LmNjgglqc3piNjMuY2OCCGpzemI3LmNjgghqc3piOC5jY4IIanN6YjkuY2MwEwYD
VR0gBAwwCjAIBgZngQwBAgEwggEFBgorBgEEAdZ5AgQCBIH2BIHzAPEAdwA7U3d1
Pi25gE6LMFsG/kA7Z9hPw/THvQANLXJv4frUFwAAAYzJ8p/uAAAEAwBIMEYCIQCQ
RlJ+DvtROT9CcShMooplQoMgFZWfn19iTSIzrsyHSwIhALxjKyJNCJq30Cim6bZ/
RT8d9MPNaOXqSJg2wkRUUsi3AHYAouK/1h7eLy8HoNZObTen3GVDsMa1LqLat4r4
mm31F9gAAAGMyfKh2wAABAMARzBFAiEA9/aoCTXI3Cp0EuRPwAglVcEq9d83Txe5
6i+SjxESlWECIFSrP5XlHPtdA+0PkA6pWbhUi6xRSI9VWoU0qdde/O9VMA0GCSqG
SIb3DQEBCwUAA4IBAQCjVCK0kmeuIm/Nc9TGQ6K3g8jnvEPHRChJPdw8h80vQJLA
LRKY3+LcX0ipJjPzq570cF508O5R40GBAIGwYarrXieCY/V4mSyfcWtWg70bx9pB
w49YuFjXU1Kg0aA6K3SZ1NHP26XcC8nu6rFoA9gst3l/nv9XCYbx/Xj/58H+QDtI
hRq2w06Xbg3eaUG3MQJHKHEKfIZF0c8EZFjBqc1qnTIXHn5WF3emglSP5Jj7iokt
HGyqgDBKEjuBOeXhI3/tUvg9qV1r+wBQ6WZoHUq8e9GT+e3C8t8AclxU53DhJa5L
8NzsStNFZYUga8A70e8BD5iz6+i1Mf479WDjr/sl
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0062(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='62',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0063(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='63',
            cert='''
-----BEGIN CERTIFICATE-----
MIIFIzCCBAugAwIBAgISBHztQDlFDlADGd4Igz21NwzbMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAxMDkxMTA1MTdaFw0yNDA0MDgxMTA1MTZaMBIxEDAOBgNVBAMT
B3hmMTEudHYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC7N2rVdel7
aT7cgn5THMXwfiI1q2V+T5pn8KfHrYaUlfHz94da4fZhLdPZb2fAHKfkSRkBLqCo
MOH4PDmT2tVdkz79byeNorHkGDD+XdlquksCt+XXgHZU13Ywh3BGIpp0CYz6fGv3
Bs5yqSRU4m7piHmIevOUHvtwJrV+GIb2j8JJCrTv43z04kCui1BnBM8exq2Uxz9M
6VSmaVeDWhV1symD9QGep+vnlSKgwmuE3Rul3mbEthE3aiGrw01LD+OXVmYqpk9g
G+5DX9Kc+56xo9yFTa+OEoZbqN/chW6fmODIGVPLy2hv6VQFVlbYnVeUNh48Oas/
QC64NZrxMpf1AgMBAAGjggJRMIICTTAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0lBBYw
FAYIKwYBBQUHAwEGCCsGAQUFBwMCMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFABg
yhuX6K1d+/80VzwCuc5p5wsRMB8GA1UdIwQYMBaAFBQusxe3WFbLrlAJQOYfr52L
FMLGMFUGCCsGAQUFBwEBBEkwRzAhBggrBgEFBQcwAYYVaHR0cDovL3IzLm8ubGVu
Y3Iub3JnMCIGCCsGAQUFBzAChhZodHRwOi8vcjMuaS5sZW5jci5vcmcvMFoGA1Ud
EQRTMFGCB3hmMDAudHaCB3hmMTEudHaCB3hmMjIudHaCB3hmMzMudHaCB3hmNDQu
dHaCB3hmNTUudHaCB3hmNjYudHaCB3hmNzcudHaCB3hmOTkudHYwEwYDVR0gBAww
CjAIBgZngQwBAgEwggEEBgorBgEEAdZ5AgQCBIH1BIHyAPAAdwBIsONr2qZHNA/l
agL6nTDrHFIBy1bdLIHZu7+rOdiEcwAAAYzuHNrbAAAEAwBIMEYCIQDrqwNaQggK
3ne/a8HuVEr/imCwXMx79uzyISZtkWJh3AIhANEVY9A7XP6t99vJgoGo2OG26DnF
wmU33Fdq/ajiCHrXAHUAdv+IPwq2+5VRwmHM9Ye6NLSkzbsp3GhCCp/mZ0xaOnQA
AAGM7hzbjgAABAMARjBEAiBGRGYTOViJhCYKjqv3WRoDknvzvZq7YEWUwpVGpwCF
YAIgYYTpzZz9OR7sANs8KzHhu5jZgq5ywke6SvTAoLpTmdUwDQYJKoZIhvcNAQEL
BQADggEBAC5gLoK4gVHU6stA72fzHWstQZvgdji0imzl+O6TqjwLln1DtHNVQkaC
jzBm037ugjBVOIgAgp8hYgxjWPYf75UaDLI/hvPqeg5biN+Eeh9wJ+MnQzbtQRig
47uroauuQgXGRBIgTzFXmF2kp90oaS/g7Hmx9/SmwYlLOEebqsSpZgkfvqyL8ibr
LT1YgAsF0tI2poRRE02dMbweMRwg/j0I+W3I1HTwmo6fdu6MYqgefSnPp9iSoWhw
VWjusEk66kuHbVwh0U6DM1nRYxbbaXiztNrGgxeKu5mVr/OH+v0yTgn6AkhkM3e4
3aPhglP6uvjbdlXg5W/PsW875WkRbwA=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0064(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='64',
            cert='''
-----BEGIN CERTIFICATE-----
MIIFCDCCA/CgAwIBAgISBISPZJ8SEQEXhqzE1n0hEnxNMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAxMDkxNTIwMzZaFw0yNDA0MDgxNTIwMzVaMBIxEDAOBgNVBAMT
B2pzNjYudHYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC79ap7hFBR
BjeyXabLn0gQSbWn7IBiv8e3EITn63OYb88Kxg8d+8bY+LvYcQKg2HKQRVuyx7zG
aWS6US419qCtTG1DTkE1Zsv4R1uZsjqIQCXiYdkP05KC8JpxawbOGHeUZ/VimQ5p
nC+x5mj85DEG8n+NrcRE7dYaP2vecEH/AXwRzVidzAZgC+JO20vIcJfIVK7NNJZY
FZfc+KPZJ2vEPk86KZLR7LgXOf24TA76tIzOha8ePTN4RkpgnPyhOHsz9o4nMKha
Xj1r1grqnSpSQnO0Ftrer4iTL1vuGzCIzZmAsoUTmZZtHAI3Dh9ZX5RvixGmRvDr
w4MTnBm5GC1pAgMBAAGjggI2MIICMjAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0lBBYw
FAYIKwYBBQUHAwEGCCsGAQUFBwMCMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFP+A
mA5iGytXrVHT/h5/IxGaJferMB8GA1UdIwQYMBaAFBQusxe3WFbLrlAJQOYfr52L
FMLGMFUGCCsGAQUFBwEBBEkwRzAhBggrBgEFBQcwAYYVaHR0cDovL3IzLm8ubGVu
Y3Iub3JnMCIGCCsGAQUFBzAChhZodHRwOi8vcjMuaS5sZW5jci5vcmcvMD8GA1Ud
EQQ4MDaCB2pzMDAudHaCB2pzMzMudHaCB2pzNDQudHaCB2pzNTUudHaCB2pzNjYu
dHaCB2pzNzcudHYwEwYDVR0gBAwwCjAIBgZngQwBAgEwggEEBgorBgEEAdZ5AgQC
BIH1BIHyAPAAdwBIsONr2qZHNA/lagL6nTDrHFIBy1bdLIHZu7+rOdiEcwAAAYzv
BpodAAAEAwBIMEYCIQDMI+J+IjNyZrWlCAw7Tep+DsDOHiXXcVr+EwXGpKioggIh
AP1MOj9Td3TDTpxFjJuRHT8eO5X/jwYk4pf7bcRdxYyXAHUAdv+IPwq2+5VRwmHM
9Ye6NLSkzbsp3GhCCp/mZ0xaOnQAAAGM7waaXwAABAMARjBEAiA1EreVyZEci69M
zes8MLM8gqD5EbPJjEId3/PU4g1tewIgWnFb3q6Bax7jPF8LWmimivCW7/s/fjWk
3PjgZoKZK7UwDQYJKoZIhvcNAQELBQADggEBAIzVDs2as2sn6K8fccYZDwVByUlr
7v3JW5KaZUsFxqPMzzfmBA+3M9E0ydEt5xe7vD5q1xiRnmXE483L5Utl1LUrUs3H
7EY4c9a9OB5w7vtfX1pklQ+EQoEOV4JxupytJyWt8JKv9ddV5cnh3TLdhaXfPJOJ
NbDk9kCPp1oKY4vCyKLj6u/Sxcr0oqSHv/11N911M/7VGk9O0kz8PpiMvMqjR4cw
9+7Fl/9Vdn+KSCtIV/uYNbwxbQd0eP0tXDwIhzqQ1N+yEdgUn80q+za9HlYjDku7
LQBOw7bBaPcEyLUaHYUwV6f/eS2MUP1oAwFYYAkY30UuGhBsZQiJVavj2hU=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db005(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db005',
            cert='''
-----BEGIN CERTIFICATE-----
MIIE6DCCA9CgAwIBAgISA2Q0oNojhVX2Lzl24zKA9PQ9MA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAxMTIwNDMzMzdaFw0yNDA0MTEwNDMzMzZaMBUxEzARBgNVBAMT
Cnd3dy4zODQudHYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCtrMdV
ZrkZEbxFxPlFDyBATfFRCjn81T86x4EdqpkaQMELVKZfOnXIyO8pc+U6GEE5VYoI
1iKwVnYWiUYDB7jylsMY+6Li8hiBSfyiQ+QD1fDhQqU9TdR1CUhfMroi5Y/0sBWR
jz+Zv/dEPtSiilKvWg+wIafbGbsowmBk81xt6/d5zCuN8MRcFi4WFCIu0JXqBVVP
sC9d53hvxnEEXAHZYGH0FE7U0/5fCh/RzgDqJJ/etqFR0u/FP6t/Zm63JZrX6vH9
kYvh2SB1Zwt5qkf+SuczT/MVkQ7k+WgNwhm5Slc0+JCVWzi47IkbPEbzXZih+5qp
nmXH+f6nE/MCbz5FAgMBAAGjggITMIICDzAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0l
BBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYE
FAafPtBT0N2DexbixCgxmHDT+i6hMB8GA1UdIwQYMBaAFBQusxe3WFbLrlAJQOYf
r52LFMLGMFUGCCsGAQUFBwEBBEkwRzAhBggrBgEFBQcwAYYVaHR0cDovL3IzLm8u
bGVuY3Iub3JnMCIGCCsGAQUFBzAChhZodHRwOi8vcjMuaS5sZW5jci5vcmcvMB0G
A1UdEQQWMBSCBjM4NC50doIKd3d3LjM4NC50djATBgNVHSAEDDAKMAgGBmeBDAEC
ATCCAQMGCisGAQQB1nkCBAIEgfQEgfEA7wB1ADtTd3U+LbmAToswWwb+QDtn2E/D
9Me9AA0tcm/h+tQXAAABjPwpWF4AAAQDAEYwRAIgHYNKqhtthTp8FLPyhOvueVJ0
AHksGJR84rFkgfqX5tECIDJmVyuvFBKNtyxUa854eKzJEFyEuW7huQGvMLxRRoVb
AHYA7s3QZNXbGs7FXLedtM0TojKHRny87N7DUUhZRnEftZsAAAGM/ClYWAAABAMA
RzBFAiBonYzHpK6j2yvvm+YaDVOnHPMZhx0RWnd7hm/dcOC0bwIhAJ7v/Gdk3Hw8
Jsozp4OTK7veUgJKBGEK6T2hntsrwu70MA0GCSqGSIb3DQEBCwUAA4IBAQC6ITad
mZqabpfkGk0+2d/PLw0SaJCp3nMm8i6hPL2uvYPRm5a+7O56xVSx5BFfqE4nLZ2B
tW2t5ckxnw6zYgGKEMoDJM2aiN0XweDEZdGEQkejqlKQTPA1N5N1u+tMmwspF+zo
HTOac24MFrcmNyFna/mx6/v31xekdi7bIFXAorV9X6WosTdKcpjsXgJWfuVs861j
QyrqsQ2/X+KwTqSbhnoBUeVSwxQnqlJ/Z5XJFArfz86mmvfekr8mww4Zw6ypUkxe
NwECe7OTuC0vrStZa3OwALlPVHBpUwYc3ChqSfNH4/Y2DAZ1KVbcwB3DiJM7v1ML
Bby23b1LSgHpCAk0
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db006(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db006',
            cert='''
-----BEGIN CERTIFICATE-----
MIIFFDCCA/ygAwIBAgISA18mvMA0i5laczz2Gd3KC+GWMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAxMTcxMzAxMDNaFw0yNDA0MTYxMzAxMDJaMBQxEjAQBgNVBAMT
CTUxemJ2Lnh5ejCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJxXjKQO
aBpWIseavGG310tDuqw9y8wRi0UyLxWk4hmo6+/qvPvi3hgKEQR69ah0tuxAb6uB
Ebw5AeFp9QQ8oSqyOXnTbr8i/SZnKWuzyflP+dGSPc4E6/QdnHrHIz/UmwpDSf9/
xQC8+gs44Ygkdh5sSxGzM+vCQy+qNP+qN44mAivbFjTvcVmd0+n4v9zI7kOCFhi5
5kOvpN2TZ95tjOMX3vZq54lva6b0s4i9xBFY1Id8CGqUgCX6iMLdNTCB7F8JTV6T
+Ye7lGwDN1xBWDLVcK+LdHoQbeg9x7gUk1vWf/DD8V12IYTxa1cyC6/eB53W+U8C
eEYYlsdtCVOSdysCAwEAAaOCAkAwggI8MA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUE
FjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQU
rsf2APXt0dEU8P1/sWGUhoB8TWwwHwYDVR0jBBgwFoAUFC6zF7dYVsuuUAlA5h+v
nYsUwsYwVQYIKwYBBQUHAQEESTBHMCEGCCsGAQUFBzABhhVodHRwOi8vcjMuby5s
ZW5jci5vcmcwIgYIKwYBBQUHMAKGFmh0dHA6Ly9yMy5pLmxlbmNyLm9yZy8wSwYD
VR0RBEQwQoIJNTF6YnUueHl6ggk1MXpidi54eXqCCTUxemJ3Lnh5eoIJNTF6Yngu
eHl6ggk1MXpieS54eXqCCTUxemJ6Lnh5ejATBgNVHSAEDDAKMAgGBmeBDAECATCC
AQIGCisGAQQB1nkCBAIEgfMEgfAA7gB1AEiw42vapkc0D+VqAvqdMOscUgHLVt0s
gdm7v6s52IRzAAABjRe5te4AAAQDAEYwRAIgQpKHudZX11kHYqW6eozQmE5tfrOy
ZKg0nWl603hfmVgCIC0XtvG4RLnlI6hupoPQW+8Y8F2Y9OCycBROEQKFJ4jKAHUA
O1N3dT4tuYBOizBbBv5AO2fYT8P0x70ADS1yb+H61BcAAAGNF7m19QAABAMARjBE
AiAgVyprnaVpKT1kooY/EhVkGrxmgc/RfieD+I2IV/1IUgIgPR0C3gQtuRHh6Q7Q
e1+G9qrIP9E1to7w1FpLjCilo9IwDQYJKoZIhvcNAQELBQADggEBABHusllB1xQb
sVn6+rwt/Vot0j4rr+3XrLM10MgDxo6xxvXZzGz1O+spJcwitJBxBUimTMGkIZSI
cECuWej06ore9GrbD1Zyx4vm4i3zHhPBHrhkvEUKOVqsDc0da4O/pHXUd3sZUZ5u
jabr/G66XSQfW8fHtBWejPdlTJ3EVL8ZipQ5vlDct6Kv2+lqHej2KpNYmJ7mc9D/
Ir1tz0XMNqHubNTFwNeHNwAb81YBTvOr82eLy/NK5Osfw75CTpv3cCgl+y8NdoCa
iVAmjofoHlCiUjHE3eML8TigqqjyBw9HAQeRJheLQZanNDtFfJwfuureyrRhy6RO
xbXwboURyjE=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db016(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db016',
            cert='''
-----BEGIN CERTIFICATE-----
MIIE5DCCA8ygAwIBAgISA2lSq8ijDeocasMKmF6gVsxHMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAzMDYwNDI4NTFaFw0yNDA2MDQwNDI4NTBaMBExDzANBgNVBAMT
Bjc1Ny50djCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMvgHYNUq5Nz
Nt4H1M2aeIFOxmHRQ0rKeE5mIHJkoL3N3DDAkgcNMGeeHRiIvHwoSVRw6o8VwprB
RRNHJnhWUifzNghZtTCgYln2DGj8TKQR20E82RlRMjzHIkRTgoww2U3j9ILxACg9
8pWBlSYJQYZfIN9oJWok4o5x+ilh2oD62iPFB3SUpF7TeqHfIJGnNNstWaILpp9M
qHRwA9bQIsURJghYaduydqV0WoRAJEqFHs9CuXFjp6pHUb660MRcE7h7G9pgxVZS
vuv4CGzZYcbcXBrtcAjNrHkezBdyFBPKTxUBs1PTH4d5a0uELHoFJB2Rnf3Dy8K4
PwS0IJAUbukCAwEAAaOCAhMwggIPMA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUEFjAU
BggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUHa3x
vaQBy4L3P1h5glcgFAPX7qcwHwYDVR0jBBgwFoAUFC6zF7dYVsuuUAlA5h+vnYsU
wsYwVQYIKwYBBQUHAQEESTBHMCEGCCsGAQUFBzABhhVodHRwOi8vcjMuby5sZW5j
ci5vcmcwIgYIKwYBBQUHMAKGFmh0dHA6Ly9yMy5pLmxlbmNyLm9yZy8wHQYDVR0R
BBYwFIIGNzU3LnR2ggp3d3cuNzU3LnR2MBMGA1UdIAQMMAowCAYGZ4EMAQIBMIIB
AwYKKwYBBAHWeQIEAgSB9ASB8QDvAHYA7s3QZNXbGs7FXLedtM0TojKHRny87N7D
UUhZRnEftZsAAAGOEjxlhwAABAMARzBFAiAOhaob+Ga+Rjh0MqLxjyi1huSmikn0
q3K1QNl1VlweSwIhAL9GsJSa6KtS+6fw2uOmPJ7L8YJ4ZGN9fbhD+bbqH4mkAHUA
ouK/1h7eLy8HoNZObTen3GVDsMa1LqLat4r4mm31F9gAAAGOEjxllAAABAMARjBE
AiBFKlch3CQFOUQ/CJl5bWAsCdoQITjT0/OemFpJKCe94QIgbnNUzYeTc2o81UnK
UfqoYH1Ixcgr455/YZbaJt928yEwDQYJKoZIhvcNAQELBQADggEBADl4J5o6XhPF
hLwBmN5N1yxt3mPs6T33GdncneLT1aQu2FDd4dSmj7ZAAfQQDyS4hl5qyhsVK0iP
0nDDcs2kJI1K7RTzvBUaribCFQyDtH37gOQdbaH/KQGjOayYoT35ZzaEEeTJ6xrv
U30zzVvSTXNMv1PbBunqotCfmK9IFZMsk3r8qqp4ypTqfDsaYpar0ueJo44IG7u7
KfQZhhB95gLFyAaQMF+MkoZtlauOcTQYai7STtwCOze6fiJkJS5LMv+XLAq5fV6b
JashhRAPPPqFkjFo5yiPAqMF8VRFiJxygCTqgtqFIqI23MSGvuB5+eAVvX8boldx
ao1hIDKNROQ=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0065(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='65',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db007(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db007',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0066(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0066',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFYDCCBEigAwIBAgIQQAF3ITfU6UK47naqPGQKtzANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTIxMDEyMDE5MTQwM1oXDTI0MDkzMDE4MTQwM1ow
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwggIiMA0GCSqGSIb3DQEB
AQUAA4ICDwAwggIKAoICAQCt6CRz9BQ385ueK1coHIe+3LffOJCMbjzmV6B493XC
ov71am72AE8o295ohmxEk7axY/0UEmu/H9LqMZshftEzPLpI9d1537O4/xLxIZpL
wYqGcWlKZmZsj348cL+tKSIG8+TA5oCu4kuPt5l+lAOf00eXfJlII1PoOK5PCm+D
LtFJV4yAdLbaL9A4jXsDcCEbdfIwPPqPrt3aY6vrFk/CjhFLfs8L6P+1dy70sntK
4EwSJQxwjQMpoOFTJOwT2e4ZvxCzSow/iaNhUd6shweU9GNx7C7ib1uYgeGJXDR5
bHbvO5BieebbpJovJsXQEOEO3tkQjhb7t/eo98flAgeYjzYIlefiN5YNNnWe+w5y
sR2bvAP5SQXYgd0FtCrWQemsAXaVCg/Y39W9Eh81LygXbNKYwagJZHduRze6zqxZ
Xmidf3LWicUGQSk+WT7dJvUkyRGnWqNMQB9GoZm1pzpRboY7nn1ypxIFeFntPlF4
FQsDj43QLwWyPntKHEtzBRL8xurgUBN8Q5N0s8p0544fAQjQMNRbcTa0B7rBMDBc
SLeCO5imfWCKoqMpgsy6vYMEG6KDA0Gh1gXxG8K28Kh8hjtGqEgqiNx2mna/H2ql
PRmP6zjzZN7IKw0KKP/32+IVQtQi0Cdd4Xn+GOdwiK1O5tmLOsbdJ1Fu/7xk9TND
TwIDAQABo4IBRjCCAUIwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYw
SwYIKwYBBQUHAQEEPzA9MDsGCCsGAQUFBzAChi9odHRwOi8vYXBwcy5pZGVudHJ1
c3QuY29tL3Jvb3RzL2RzdHJvb3RjYXgzLnA3YzAfBgNVHSMEGDAWgBTEp7Gkeyxx
+tvhS5B1/8QVYIWJEDBUBgNVHSAETTBLMAgGBmeBDAECATA/BgsrBgEEAYLfEwEB
ATAwMC4GCCsGAQUFBwIBFiJodHRwOi8vY3BzLnJvb3QteDEubGV0c2VuY3J5cHQu
b3JnMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwuaWRlbnRydXN0LmNvbS9E
U1RST09UQ0FYM0NSTC5jcmwwHQYDVR0OBBYEFHm0WeZ7tuXkAXOACIjIGlj26Ztu
MA0GCSqGSIb3DQEBCwUAA4IBAQAKcwBslm7/DlLQrt2M51oGrS+o44+/yQoDFVDC
5WxCu2+b9LRPwkSICHXM6webFGJueN7sJ7o5XPWioW5WlHAQU7G75K/QosMrAdSW
9MUgNTP52GE24HGNtLi1qoJFlcDyqSMo59ahy2cI2qBDLKobkx/J3vWraV0T9VuG
WCLKTVXkcGdtwlfFRjlBz4pYg1htmf5X6DYO8A4jqv2Il9DjXA6USbW1FzXSLr9O
he8Y4IWS6wY7bCkjCWDcRQJMEhg76fsO3txE+FiYruq9RUWhiF1myv4Q6W+CyBFC
Dfvp7OOGAN6dEOM4+qR9sdjoSYKEBpsr6GtPAQw4dy753ec5
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_0067(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='0067',
            cert='''
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db019(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db019',
            cert='''
-----BEGIN CERTIFICATE-----
MIIE+zCCA+OgAwIBAgISAxYMuzThCf5S0VDOA/NUUqVJMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAzMDcwMzEyNTJaFw0yNDA2MDUwMzEyNTFaMBUxEzARBgNVBAMT
Cnd3dy40NTEudHYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDd75p9
CnCZ6RV7wK6JyNeje8XMST35AARijCTG04A+Kt+vhP8HwElP6fu7R12a1takLZc/
dMsS6zwe2WIw/9newdPygoVBeWU5F7fIcfDSXRSxpczkbSmZoQQYdcjaEwtsk+4c
N4b4IUUJSK/Z6IA4i0eEJYnUJP8Oj4XXPOiAJTXgeXsXJQXzIQ49zEJm+iR887RQ
US6vspKJckPiqo8BG5p+Z8Z2GT2qXNFlbTNuWLno4g02mTPw+NWxpCkjYrNGqY/f
sCRn9RWb5liQ8e1zLK2/NX5+IMrlazR4o2pxgED7YsvR6J9jYVaX+0OayAlmBomY
KnVIH4Ml2INdmb5tAgMBAAGjggImMIICIjAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0l
BBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYE
FB+veF9fo7C5XF+BykTQB9ZRNjH7MB8GA1UdIwQYMBaAFBQusxe3WFbLrlAJQOYf
r52LFMLGMFUGCCsGAQUFBwEBBEkwRzAhBggrBgEFBQcwAYYVaHR0cDovL3IzLm8u
bGVuY3Iub3JnMCIGCCsGAQUFBzAChhZodHRwOi8vcjMuaS5sZW5jci5vcmcvMDEG
A1UdEQQqMCiCBjQzNy50doIGNDUxLnR2ggp3d3cuNDM3LnR2ggp3d3cuNDUxLnR2
MBMGA1UdIAQMMAowCAYGZ4EMAQIBMIIBAgYKKwYBBAHWeQIEAgSB8wSB8ADuAHUA
SLDja9qmRzQP5WoC+p0w6xxSActW3SyB2bu/qznYhHMAAAGOFx0ueAAABAMARjBE
AiBwIrfKBA2gW3Ms7pASG81NW+SHM8p+lYOr57GsVodTbAIgGhU47oHqR1ak2qaG
RZlyCbIekC0+YHn+fbudGKkYRAMAdQA7U3d1Pi25gE6LMFsG/kA7Z9hPw/THvQAN
LXJv4frUFwAAAY4XHS5xAAAEAwBGMEQCIGPnMhQ3RRXLmco8cZHSlVSCszwkDoEw
81WnO8/uvWfWAiBf3fLwSyDmShDyvUfgM6IWVgfuQKlOY5HU+HWUcBb7fDANBgkq
hkiG9w0BAQsFAAOCAQEArxGb0tj6Q+cY0+P/YiK6cPptqWbkJHUSLD1RYVzUacLH
xlL73iUmheWxcOrmqNEombjAeuBGfctt13Z+OVMP5p38RtaoeHmbpRXfiv1z8nI8
sMs5KXxwULLpUwt4lmaLbAO6FL2U2jSJfSw23nfkcwfFIyLLFdOp6kd4Hl04XYc0
2r/YTP1KmFaRm8vSsXW8N6PBmDOOdXUZu7am3YTMWigLEQqAitDwU29xW+YKU+2J
C9pvD+4XFbRsGMOSOWDvskR4XNSHRmrHjggavpLsIKQzk9C58+ZSjWRzuUNDjVZ3
mJBFNdXWsHik+Gg1uGfDCpiEyIyTk525Nak9wIhVog==
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db020(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db020',
            cert='''
-----BEGIN CERTIFICATE-----
MIIE6zCCA9OgAwIBAgISAyUUMeLMgdI831fhwhxiQyRMMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAzMDcwODAxMDdaFw0yNDA2MDUwODAxMDZaMBUxEzARBgNVBAMT
Cnd3dy44MzcudHYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDGOnoc
QL56JT4YCTaUTGnRZZO1Ank/WpRoCH3Jkt6QEv81H+8l4DaYgVcZ0Tg2xcrT6Jlq
gsflBBF+HCIDZ7SgK3XSTMIWnR66eKI+WK0Nms3tbYYTomLlPJ74Sf8Is56SIAC3
xo4N+gcHpniTjid5QzQXHulP5bbN1Bhp7A7DUHV3cE2C0O8xo2/PbI0R4El28fzj
LGggPejoh4j7YqKcgEBWmO3oXkO/cVgvkoyuAspFhXZ7SfyhoF4MSdRNQVp9iJWE
SlmAvR5QfxeZDpwN105vPVUtyU4Dqdy8AKa55eq1M14TmnjjyHTPgBICKdE7lkUo
8oyMty0EVwz7eS9BAgMBAAGjggIWMIICEjAOBgNVHQ8BAf8EBAMCBaAwHQYDVR0l
BBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYE
FK3Mdxvx62mweDPtD+S/IFWcm1UOMB8GA1UdIwQYMBaAFBQusxe3WFbLrlAJQOYf
r52LFMLGMFUGCCsGAQUFBwEBBEkwRzAhBggrBgEFBQcwAYYVaHR0cDovL3IzLm8u
bGVuY3Iub3JnMCIGCCsGAQUFBzAChhZodHRwOi8vcjMuaS5sZW5jci5vcmcvMB0G
A1UdEQQWMBSCBjgzNy50doIKd3d3LjgzNy50djATBgNVHSAEDDAKMAgGBmeBDAEC
ATCCAQYGCisGAQQB1nkCBAIEgfcEgfQA8gB3AEiw42vapkc0D+VqAvqdMOscUgHL
Vt0sgdm7v6s52IRzAAABjhglF7kAAAQDAEgwRgIhAMJnT4Ti4Jiv9WfkgGb9rsDu
gzhQ2e4jhn0VYon19BjWAiEAqC6od/Co7In1E/jVyNqWfLLNG2UlExhmkQIN00zQ
fgoAdwDuzdBk1dsazsVct520zROiModGfLzs3sNRSFlGcR+1mwAAAY4YJRfOAAAE
AwBIMEYCIQDmOlK81LmmdHimA/+8n8maetLK5QxV96xwQV33Rygq1wIhALDmaUI5
waWG9jRg4RxfGTacdxRIPnIUGXcJrlyZe1yeMA0GCSqGSIb3DQEBCwUAA4IBAQCV
8yWJU4W7v8S9ej8l2TBO5so96ccPRqa7NIrfOe1eD8h3z16CpRjojvHU7Rbh0dYK
M2MVTkABb29TvStDSQc4UcvGtI9urwZczoCHfInKvbUUdhmZDpQnGrUznViexLcl
lOiWAIi4IeQu37vrRahhhbcclAxqk19OeTmQt2iPymwaOrrRt7rFqNWdK8uubXEW
lkVeRW/ifRz1A4dJOOuhDha7RZ0qAvw//BcA2iL8X74PufUoLXaV/Mtgnl1pMnv+
EBHT+VG4tuYZtxLiylOO4pOhghQ/ao9tADchWxHBA6svxzL3coN8LUR27mOYeUak
9697OsInMwEKufQdq96N
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db021(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db021',
            cert='''
-----BEGIN CERTIFICATE-----
MIIE3jCCA8agAwIBAgISBIRRN1KMoIeFJN/kIGCA9my9MA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAzMDcwOTA3MjlaFw0yNDA2MDUwOTA3MjhaMBQxEjAQBgNVBAMT
CTM4MzM4My50djCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALR+MJpp
BQCFPHe/XntURVbPfxk0jeZ1aWC4ovEeY6pjBUB2vUaCr9BARONBB60Uy13CCQu4
IuisihEUFjyPurg484kZB75Hwv4FPlxdtuwwfgZShPR3A7tR0ketgeB9NyD1cxf4
FaPlROWIf3HV6d3ziUJDO27r7ppT97qjoH9KI9JnlCm7HzhjKGyYtngdHRhXN6es
UmrEATv2fVvRYS5+ZZalXOyAqk7EOe5clq5N3X7V6u25gTam+MfDAoZqf2jQWHrV
KeqaOd3A8pRlQi4pmufiJJ1Ga7pm4aSkXQbM1LRgwFPP1wejSGD8v+s3YwuON3N/
inAUv429FOUk1zECAwEAAaOCAgowggIGMA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUE
FjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQU
pjZpPYEFWF5RUq1z1eqAWnwpgF0wHwYDVR0jBBgwFoAUFC6zF7dYVsuuUAlA5h+v
nYsUwsYwVQYIKwYBBQUHAQEESTBHMCEGCCsGAQUFBzABhhVodHRwOi8vcjMuby5s
ZW5jci5vcmcwIgYIKwYBBQUHMAKGFmh0dHA6Ly9yMy5pLmxlbmNyLm9yZy8wFAYD
VR0RBA0wC4IJMzgzMzgzLnR2MBMGA1UdIAQMMAowCAYGZ4EMAQIBMIIBAwYKKwYB
BAHWeQIEAgSB9ASB8QDvAHUAdv+IPwq2+5VRwmHM9Ye6NLSkzbsp3GhCCp/mZ0xa
OnQAAAGOGGHZkwAABAMARjBEAiANrVt7sY353AzQOinsZvW7Q5A4McrL1AMZtY5f
1TQnMgIgEKQHJqNIKBS9ShJQ1PjWvItaHngHFns0+g7uv9IprKgAdgCi4r/WHt4v
Lweg1k5tN6fcZUOwxrUuotq3iviabfUX2AAAAY4YYdllAAAEAwBHMEUCIHQ53RKV
AallW5F4mX9ZNi5nrjuOsBbrKXoRzFxO9v5CAiEAhZ2RQq7jG+LItjKnRSEgxawF
ZH6f+68Lq6rZJTLIZ7kwDQYJKoZIhvcNAQELBQADggEBAK+m4tIa/QWu0CqpD1/U
fQrOcwmsEKS/D/wmqUZq47oFsXDgynmKF1Ppvnobv2ybhf5733mlkPrVF5HiYTwm
UPBT8Ll9uueFo8SOQQt7QVqhbemot4lPbvJWRSLAXzx+v0aSt2xhDo0tMVNl0pB8
pIknF13t84rzhg7KqRjtOALBd257hvbp9iBJk7SWSXz25C7usXRxpamxR95+9sZY
kZ9NWD2AZ7xCfaAZUcqlVeOuwaa8EyqLTJD/gU7LZ/jWw62J2cRzwV+iR4MiZzyE
zP7mE5DEM7VGGfYKxb4NOH4bA1XxI13F5yh9sIgV0jbhBqT82/7P8MbsKs6ey2s/
s9E=
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)
    def ssl_db023(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='db023',
            cert='''
-----BEGIN CERTIFICATE-----
MIIFJzCCBA+gAwIBAgISBJgpifeg7dJXzUsFlIjuGtvBMA0GCSqGSIb3DQEBCwUA
MDIxCzAJBgNVBAYTAlVTMRYwFAYDVQQKEw1MZXQncyBFbmNyeXB0MQswCQYDVQQD
EwJSMzAeFw0yNDAzMTIxMTA3MjlaFw0yNDA2MTAxMTA3MjhaMBQxEjAQBgNVBAMT
CTE0OTE0OS50djCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKt4dqzw
NUat8uwkHBsLvIbXRu/GdvXsppLFKdbBs8iHtFcL8gvmvdQ5HMI+xQAucZS8xt/T
xxAN8K5kRVwZqIYtxMEVZmBi3GZgMTDGjfXOASxiiFdHT49eUMhXE/LoO42Zpkfq
zZGrOrDh5vqEkNaGPQ8G7AWLJmVMxuZIWp/yWBYotF/BsSvHdmlW8swe5JvZ8HHo
QKjjS6bgXjxgx1IaGdgM7Ggo3q+FVHr6RT5yGt2644HAM53tY8yLI+Qu5UJogo/D
sofk3sLntSpfB2/5cl9Cs7VJHKb266QLe+C1jdlNatBedofaIuk/mwIjPImChvqW
NuMhDyKZXneTV5cCAwEAAaOCAlMwggJPMA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUE
FjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQU
frmdyelL3si4f2F33grcbV2k8AwwHwYDVR0jBBgwFoAUFC6zF7dYVsuuUAlA5h+v
nYsUwsYwVQYIKwYBBQUHAQEESTBHMCEGCCsGAQUFBzABhhVodHRwOi8vcjMuby5s
ZW5jci5vcmcwIgYIKwYBBQUHMAKGFmh0dHA6Ly9yMy5pLmxlbmNyLm9yZy8wWwYD
VR0RBFQwUoIJMTQ5MTQ5LnR2ggkzODIzODIudHaCCzU3NTU1NzU1LnR2gg13d3cu
MTQ5MTQ5LnR2gg13d3cuMzgyMzgyLnR2gg93d3cuNTc1NTU3NTUudHYwEwYDVR0g
BAwwCjAIBgZngQwBAgEwggEFBgorBgEEAdZ5AgQCBIH2BIHzAPEAdgA7U3d1Pi25
gE6LMFsG/kA7Z9hPw/THvQANLXJv4frUFwAAAY4yj4UZAAAEAwBHMEUCIQDZ/aiP
W3r3EH8OmATEYLWDOsAMKSVveESSVHZiH/EcrgIgLRRtDVSW+f3vKnmlX72kgrVk
IOGRb2NAtykicU2KBswAdwB2/4g/Crb7lVHCYcz1h7o0tKTNuyncaEIKn+ZnTFo6
dAAAAY4yj4VZAAAEAwBIMEYCIQCyv/eCdkdV9siyqTOcODjZ5PtzsqZAC484n7a5
s8W9ZQIhAKSstkMnL5oftC9hbiWRL4AZ7xrUl2oEI17Pg9wvGl2kMA0GCSqGSIb3
DQEBCwUAA4IBAQCFC2hM896mOQIZA/I2BTvi9q/JYsGc3tRdjpRiBHaBAJ3hM1HS
s3NC7bi7e+aEiRi/Be8aSQxhDMMfawa5vlyRqYiFQGBGs+94m4Ve57V6uBfTQt9N
DZnxef7QviIjsobc/ipF1NB0XGyGJr9hfxmIoS8rvj6LNZSs3smpzvuZdMbEYAat
cHCZrOY7RDHRh3RC10dfkSVS74p8surEQ+nLYRuWDoM0h3kjjpC2eKyLo249V87c
2CJi9A+rHj0VCmD0y+cWvCshviY/O2lFoOkiWTtEeG5nfs6ruQ06sCsjmYJWGkQM
eQd5O7bsLmrZdvbdG1dg1Y+P6osu8HST+rMv
-----END CERTIFICATE-----

-----BEGIN CERTIFICATE-----
MIIFFjCCAv6gAwIBAgIRAJErCErPDBinU/bWLiWnX1owDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjAwOTA0MDAwMDAw
WhcNMjUwOTE1MTYwMDAwWjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg
RW5jcnlwdDELMAkGA1UEAxMCUjMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
AoIBAQC7AhUozPaglNMPEuyNVZLD+ILxmaZ6QoinXSaqtSu5xUyxr45r+XXIo9cP
R5QUVTVXjJ6oojkZ9YI8QqlObvU7wy7bjcCwXPNZOOftz2nwWgsbvsCUJCWH+jdx
sxPnHKzhm+/b5DtFUkWWqcFTzjTIUu61ru2P3mBw4qVUq7ZtDpelQDRrK9O8Zutm
NHz6a4uPVymZ+DAXXbpyb/uBxa3Shlg9F8fnCbvxK/eG3MHacV3URuPMrSXBiLxg
Z3Vms/EY96Jc5lP/Ooi2R6X/ExjqmAl3P51T+c8B5fWmcBcUr2Ok/5mzk53cU6cG
/kiFHaFpriV1uxPMUgP17VGhi9sVAgMBAAGjggEIMIIBBDAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMBMBIGA1UdEwEB/wQIMAYB
Af8CAQAwHQYDVR0OBBYEFBQusxe3WFbLrlAJQOYfr52LFMLGMB8GA1UdIwQYMBaA
FHm0WeZ7tuXkAXOACIjIGlj26ZtuMDIGCCsGAQUFBwEBBCYwJDAiBggrBgEFBQcw
AoYWaHR0cDovL3gxLmkubGVuY3Iub3JnLzAnBgNVHR8EIDAeMBygGqAYhhZodHRw
Oi8veDEuYy5sZW5jci5vcmcvMCIGA1UdIAQbMBkwCAYGZ4EMAQIBMA0GCysGAQQB
gt8TAQEBMA0GCSqGSIb3DQEBCwUAA4ICAQCFyk5HPqP3hUSFvNVneLKYY611TR6W
PTNlclQtgaDqw+34IL9fzLdwALduO/ZelN7kIJ+m74uyA+eitRY8kc607TkC53wl
ikfmZW4/RvTZ8M6UK+5UzhK8jCdLuMGYL6KvzXGRSgi3yLgjewQtCPkIVz6D2QQz
CkcheAmCJ8MqyJu5zlzyZMjAvnnAT45tRAxekrsu94sQ4egdRCnbWSDtY7kh+BIm
lJNXoB1lBMEKIq4QDUOXoRgffuDghje1WrG9ML+Hbisq/yFOGwXD9RiX8F6sw6W4
avAuvDszue5L3sz85K+EC4Y/wFVDNvZo4TYXao6Z0f+lQKc0t8DQYzk1OXVu8rp2
yJMC6alLbBfODALZvYH7n7do1AZls4I9d1P4jnkDrQoxB3UqQ9hVl3LEKQ73xF1O
yK5GhDDX8oVfGKF5u+decIsH4YaTw7mP3GFxJSqv3+0lUFJoi5Lc5da149p90Ids
hCExroL1+7mryIkXPeFM5TgO9r0rvZaBFOvV2z0gp35Z0+L4WPlbuEjN/lxPFin+
HlUjr8gRsI3qfJOQFy/9rKIJR0Y/8Omwt/8oTWgy1mdeHmmjk7j1nYsvC9JSQ6Zv
MldlTTKB3zhThV1+XWYp6rjd5JW1zbVWEkLNxE7GJThEUG3szgBVGP7pSWTUTsqX
nLRbwHOoq7hHwg==
-----END CERTIFICATE-----
            ''',
            key='''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)

    def ssl_00(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        upload_user_certificate_request = cas_20200407_models.UploadUserCertificateRequest(
            name='',
            cert='''

            ''',
            key='''

            '''
        )
        runtime = util_models.RuntimeOptions()
        resp = client.upload_user_certificate_with_options(upload_user_certificate_request, runtime).body.cert_id
        print(resp)

    def main(self):
        # self.ssl_get_detail()
        self.query_accout_balance()
        # self.ssl_0039()
        # self.ssl_0042()
        # self.ssl_0061()

        # self.ssl_db002()  ## 3.21到期
        # self.ssl_db016()  ## 3.21到期
        # self.ssl_0033()  ## 3.20到期
        # self.ssl_0036()
        # self.ssl_0050()   ## 4.1 expired
        # self.ssl_0053()   ## 5.23 expired
        # self.ssl_db005()    ## 4.11 expired
        # self.ssl_db007()
        # self.ssl_0065()
        # self.ssl_0066()
        # self.ssl_0067()
        # self.ssl_db019()
        # self.ssl_db020()
        # self.ssl_db021()
        # self.ssl_0054()   ## 5.27 expired
        # self.ssl_db023()   ## 6.10 expired



if __name__ == '__main__':
    try:
        UploadSSL().main()
    except:
        print("读取代码出错")
