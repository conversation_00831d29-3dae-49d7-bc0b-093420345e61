import requests

host = 'gw.cloudbypass.com'
port = 1288
username = '94191639-res_BR-Bahia-Cachoeira_s3padk46wr4'
password = 'ikfvrjfs'
url = 'https://2024.ip138.com/'

# 定义代理
proxies = {
    'http': f'http://{username}:{password}@{host}:{port}',
    'https': f'http://{username}:{password}@{host}:{port}'
}

# 请求的post数据
data = {
    'key1': 'value1',
    'key2': 'value2'
}


if __name__ == '__main__':
    resp = requests.post(url, data=data, proxies=proxies)
    # 打印响应内容
    print(resp.text)