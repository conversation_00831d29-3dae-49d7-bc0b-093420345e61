#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/1/26 14:54
# @Name    : test2.py
# @email   : <EMAIL>
# <AUTHOR> 钢铁知识库
import requests


headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Origin": "https://114.55.244.168:32803",
    "Pragma": "no-cache",
    "Referer": "https://114.55.244.168:32803/site/php",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36 Edg/132.0.0.0",
    "sec-ch-ua": "\"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Microsoft Edge\";v=\"132\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "x-http-token": "BestuXSBmz6istTjBbU2gfs5FBLS8ILM1OxdMk9izk5CdCC0"
}
cookies = {
    "06ee6ef03302253bd7f37da6663ef6a5_ssl": "951c8084-dd6e-4f4d-8f13-86d36bdcc208.faz_7ImOvcETcjJabSZNZzeb48k",
    "https_Path": "%2Fwww%2Fwwwroot%2Fjoke76.com"
}
url = "https://114.55.244.168:32803/site"
params = {
    "action": "SetSSL"
}
data = {
    "type": "0",
    "siteName": "huanxi220.com",
******************************************************************************************************************************************************************************************************************************************************
    "csr": "-----BEGIN CERTIFICATE-----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\nXMtEPhHzo6aWBHtrY0mWjvqu2K7yxsxecchdfO0AaLGU\n-----END CERTIFICATE-----\n\n-----BEGIN CERTIFICATE-----\nMIIEVzCCAj+gAwIBAgIRAIOPbGPOsTmMYgZigxXJ/d4wDQYJKoZIhvcNAQELBQAw\nTzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh\ncmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMjQwMzEzMDAwMDAw\nWhcNMjcwMzEyMjM1OTU5WjAyMQswCQYDVQQGEwJVUzEWMBQGA1UEChMNTGV0J3Mg\nRW5jcnlwdDELMAkGA1UEAxMCRTUwdjAQBgcqhkjOPQIBBgUrgQQAIgNiAAQNCzqK\na2GOtu/cX1jnxkJFVKtj9mZhSAouWXW0gQI3ULc/FnncmOyhKJdyIBwsz9V8UiBO\nVHhbhBRrwJCuhezAUUE8Wod/Bk3U/mDR+mwt4X2VEIiiCFQPmRpM5uoKrNijgfgw\ngfUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMCBggrBgEFBQcD\nATASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQWBBSfK1/PPCFPnQS37SssxMZw\ni9LXDTAfBgNVHSMEGDAWgBR5tFnme7bl5AFzgAiIyBpY9umbbjAyBggrBgEFBQcB\nAQQmMCQwIgYIKwYBBQUHMAKGFmh0dHA6Ly94MS5pLmxlbmNyLm9yZy8wEwYDVR0g\nBAwwCjAIBgZngQwBAgEwJwYDVR0fBCAwHjAcoBqgGIYWaHR0cDovL3gxLmMubGVu\nY3Iub3JnLzANBgkqhkiG9w0BAQsFAAOCAgEAH3KdNEVCQdqk0LKyuNImTKdRJY1C\n2uw2SJajuhqkyGPY8C+zzsufZ+mgnhnq1A2KVQOSykOEnUbx1cy637rBAihx97r+\nbcwbZM6sTDIaEriR/PLk6LKs9Be0uoVxgOKDcpG9svD33J+G9Lcfv1K9luDmSTgG\n6XNFIN5vfI5gs/lMPyojEMdIzK9blcl2/1vKxO8WGCcjvsQ1nJ/Pwt8LQZBfOFyV\nXP8ubAp/au3dc4EKWG9MO5zcx1qT9+NXRGdVWxGvmBFRAajciMfXME1ZuGmk3/GO\nkoAM7ZkjZmleyokP1LGzmfJcUd9s7eeu1/9/eg5XlXd/55GtYjAM+C4DG5i7eaNq\ncm2F+yxYIPt6cbbtYVNJCGfHWqHEQ4FYStUyFnv8sjyqU8ypgZaNJ9aVcWSICLOI\nE1/Qv/7oKsnZCWJ926wU6RqG1OYPGOi1zuABhLw61cuPVDT28nQS/e6z95cJXq0e\nK1BcaJ6fJZsmbjRgD5p3mvEf5vdQM7MCEvU0tHbsx2I5mHHJoABHb8KVBgWp/lcX\nGWiWaeOyB7RP+OfDtvi2OsapxXiV7vNVs7fMlrRjY1joKaqmmycnBvAq14AEbtyL\nsVfOS66B8apkeFX2NY4XPEYV4ZSCe8VHPrdrERk2wILG3T/EGmSIkCYVUMSnjmJd\nVQD9F6Na/+zmXCc=\n-----END CERTIFICATE-----\n"
}
response = requests.post(url, headers=headers, cookies=cookies, params=params, data=data, verify=False)

print(response.text)
print(response)