import json
import re
import sys

site_name = sys.argv[1]

# 函数：从 nginx 配置文件中提取 server_name
def extract_server_names(file_path):
    server_names = []
    with open(file_path, 'r') as file:
        for line in file:
            if 'server_name' in line:
                # 提取 server_name 后面的内容并分割处理
                names = re.findall(r'server_name (.*?);', line)
                if names:
                    # 将域名添加到列表，处理多个域名和空格
                    for name in names[0].split():
                        if name != '1.01717.com':
                            server_names.append(name)
    return server_names

# 函数：更新 JSON 数据
def update_json_config(json_data, new_server_names):
    # 更新 listen_tag，每个域名对应端口80
    json_data['server']['listen_tag'] = [{name: "80"} for name in new_server_names]
    # 更新 server_name
    json_data['server']['server_name'] = new_server_names

# 将宝塔中nginx的server_name更新到waf中
def update_server_names(server_names, waf_nginx_config):
    # 读取新的 Nginx 配置文件内容
    with open(waf_nginx_config, 'r') as file:
        config_content = file.read()

    # 使用正则表达式匹配 server_name 行
    server_name_pattern = re.compile(rf'(server_name\s+{site_name}_com\s+)([^;]+)(;)')
    match = server_name_pattern.search(config_content)
    
    # 提取现有的 server_name 部分（保留的部分）
    existing_prefix = match.group(1)
    existing_suffix = match.group(3)
    
    # 构建新的 server_name 行
    new_server_name_line = existing_prefix + ' '.join(server_names) + existing_suffix
    # 替换旧的 server_name 行
    updated_config_content = server_name_pattern.sub(new_server_name_line, config_content)

    # 写入更新后的配置文件
    with open(waf_nginx_config, 'w') as file:
        file.write(updated_config_content)

# 主流程
if __name__ == "__main__":
    nginx_config_path = f'/tmp/{site_name}.com/{site_name}.com.conf'  # 宝塔nginx 配置文件
    json_config_path = f'/www/cloud_waf/vhost/site_json/{site_name}_com.json'  # waf JSON 配置文件
    waf_nginx_config = f'/www/cloud_waf/nginx/conf.d/vhost/{site_name}_com.conf' # waf nginx的配置文件

    # 从 nginx 配置文件中提取 server_name 值
    server_names = extract_server_names(nginx_config_path)
    # 加载 JSON 数据
    with open(json_config_path, 'r') as f:
        data = json.load(f)
   
    # 更新 JSON 数据
    update_json_config(data, server_names)
   
    # 保存更新后的 JSON 数据
    with open(json_config_path, 'w') as f:
        json.dump(data, f, indent=4)

    # 将宝塔中nginx的server_name更新到waf中 
    update_server_names(server_names, waf_nginx_config)
