<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>运维管理系统</title>
    <style>
        .container {
            display: flex;
            height: 100vh;
            padding: 20px;
            box-sizing: border-box;
            max-width: 100%;  /* 限制最大宽度 */
        }
        
        .left-panel {
            flex: 2;
            padding: 20px;
            border-right: 1px solid #ccc;
            min-width: 200px;  /* 设置最小宽度 */
        }
        
        .right-panel {
            flex: 8;
            padding: 20px;
            overflow: hidden;  /* 防止内容溢出 */
        }
        
        #output {
            width: 100%;
            height: 500px;
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 10px;
            overflow-y: auto;
            white-space: pre-wrap;         /* 保留空格和换行，自动换行 */
            word-wrap: break-word;         /* 允许长单词断行 */
            word-break: break-all;         /* 允许在任意字符间断行 */
            overflow-x: hidden;            /* 隐藏水平滚动条 */
            box-sizing: border-box;        /* 包含内边距在宽度内 */
            max-width: 100%;              /* 限制最大宽度 */
        }
        
        .form-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;  /* 允许按钮换行 */
        }
        
        input[type="text"] {
            width: 120px;          /* 设置输入框宽度 */
            padding: 8px;
            margin: 5px 0;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px 0;
            background: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            white-space: nowrap;
            min-width: 90px;  /* 设置最小宽度使按钮大小一致 */
        }
        
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="left-panel">
            <h2>参数配置</h2>
            <label for="site_id">站点ID：</label>
            <div class="form-group">
                <input type="text" id="site_id" name="site_id" required>
                <button onclick="executeCommand('check')">域名检查</button>
                <button onclick="executeCommand('cert')">申请证书</button>
                <button onclick="executeCommand('rsync_cert')">同步证书</button>
            </div>
        </div>
        <div class="right-panel">
            <h2>输出结果</h2>
            <div id="output"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        let reconnectTimer = null;
        let isConnecting = false;  // 添加连接状态标志
        
        function connectWebSocket() {
            // 避免重复连接
            if (isConnecting) return;
            if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) return;
            
            isConnecting = true;
            
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}/zhengshu/ws/`;
            
            console.log('正在创建新的WebSocket连接...');
            ws = new WebSocket(wsUrl, ['websocket']);
            
            // 修改心跳间隔为25分钟
            let heartbeat = setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    try {
                        ws.send(JSON.stringify({ type: 'heartbeat' }));
                        console.log('发送心跳...');
                    } catch (e) {
                        console.error('发送心跳失败:', e);
                        clearInterval(heartbeat);
                        reconnectWebSocket();
                    }
                }
            }, 25 * 60 * 1000); // 25分钟发送一次心跳
            
            // 添加ping检测
            let pingInterval = setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    try {
                        ws.send(JSON.stringify({ type: 'ping' }));  // 使用JSON格式发送ping
                    } catch (e) {
                        console.error('ping失败:', e);
                        clearInterval(pingInterval);
                        reconnectWebSocket();
                    }
                }
            }, 30000); // 每30秒发送一次ping
            
            ws.onopen = function() {
                console.log('WebSocket连接已建立');
                reconnectAttempts = 0;
                isConnecting = false;  // 重置连接状态
                
                // 如果有待发送的命令，可以在这里发送
                const pendingCommand = sessionStorage.getItem('pendingCommand');
                if (pendingCommand) {
                    ws.send(pendingCommand);
                    sessionStorage.removeItem('pendingCommand');
                }
            };
            
            ws.onmessage = function(event) {
                // 先检查是否是ping/pong消息
                if (event.data === 'pong' || event.data === 'ping') {
                    return; // 忽略ping/pong消息
                }
                
                try {
                    const data = JSON.parse(event.data);
                    console.log('收到消息:', data);
                    
                    if (data.type === 'heartbeat') {
                        return;
                    }
                    
                    const outputDiv = document.getElementById('output');
                    if (data.output) {
                        outputDiv.innerHTML += data.output + '\n';
                        outputDiv.scrollTop = outputDiv.scrollHeight;
                    }
                } catch (e) {
                    console.error('处理消息失败:', e);
                    // 不要在输出框中显示解析错误
                }
            };
            
            ws.onclose = function() {
                console.log('WebSocket连接已关闭');
                clearInterval(heartbeat);
                clearInterval(pingInterval);
                isConnecting = false;  // 重置连接状态
                
                if (reconnectAttempts < maxReconnectAttempts) {
                    console.log(`尝试重连... (${reconnectAttempts + 1}/${maxReconnectAttempts})`);
                    reconnectAttempts++;
                    clearTimeout(reconnectTimer);  // 清除之前的重连计时器
                    reconnectTimer = setTimeout(connectWebSocket, 1000);
                } else {
                    console.log('达到最大重连次数，请刷新页面重试');
                    alert('连接已断开，请刷新页面重试');
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                isConnecting = false;  // 重置连接状态
            };
        }

        function executeCommand(type) {
            const siteId = document.getElementById('site_id').value;
            if (!siteId) {
                alert('请输入站点ID');
                return;
            }
            
            // 清空输出区域
            const outputDiv = document.getElementById('output');
            outputDiv.innerHTML = '';
            outputDiv.innerHTML += "开始执行命令，请勿关闭页面...\n";
            
            const commandData = JSON.stringify({
                type: 'execute',
                site_id: siteId,
                command_type: type
            });
            
            // 优化连接逻辑，避免创建多余的连接
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(commandData);
            } else if (ws && ws.readyState === WebSocket.CONNECTING) {
                // 如果正在连接中，将命令暂存到sessionStorage
                console.log('连接中，命令将在连接建立后发送');
                sessionStorage.setItem('pendingCommand', commandData);
            } else {
                // 如果没有连接或连接已关闭，则创建新连接
                console.log('WebSocket连接不可用，正在重新连接...');
                sessionStorage.setItem('pendingCommand', commandData);
                connectWebSocket();
            }
        }
        
        // 页面加载完成后自动连接WebSocket
        window.onload = function() {
            connectWebSocket();
            
            // 添加页面可见性变化监听
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'visible') {
                    // 页面变为可见时，检查连接状态
                    if (!ws || (ws.readyState !== WebSocket.CONNECTING && ws.readyState !== WebSocket.OPEN)) {
                        console.log('页面可见，重新连接WebSocket');
                        connectWebSocket();
                    }
                }
            });
        };
        
        // 页面关闭前提示用户
        window.onbeforeunload = function() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                return "命令正在执行中，关闭页面将中断操作。确定要离开吗？";
            }
        };
    </script>
</body>
</html> 