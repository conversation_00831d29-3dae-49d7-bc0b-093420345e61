# 证书自动化申请系统配置文件

# Redis配置
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = None  # 如果没有密码则为None

# 队列配置
REDIS_DOMAIN_HASH = 'auto_cert_domains'  # 存储站点ID和域名的Hash
REDIS_CERT_QUEUE = 'auto_cert_queue'    # 存储待处理站点ID的List
REDIS_CURRENT_SITE_KEY = 'current_site_id'  # 存储当前使用的站点ID

# 域名配置
MAX_DOMAINS_PER_SITE = 100  # 每个站点最大域名数量
NGINX_CONF_PATH = '/www/cloud_waf/nginx/conf.d/vhost/'  # Nginx配置路径

# 脚本配置
DOMAINS_CHECK_SCRIPT = '/path/to/domains_check.sh'  # 域名检测脚本路径
ISSUE_CERT_SCRIPT = '/path/to/issue_cert.sh'        # 证书申请脚本路径

# API配置
DOMAIN_API_URL = 'http://example.com/api/domains'  # 域名获取API地址 