#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import requests
import json
import time
import hashlib
import os,sys

"""
20250328 使用宝塔api管理站点
命令行参数: python3.8 auto_bt_api.py 'bt.add_site({"webname": "auto1"})'
           # websites_data = bt.web_add_domain("auto1","444.com,555.com,666.com")
           # websites_data = bt.web_del_domain("auto1","trec.6nwkx.top")
           # websites_data = bt.web_del_domain("autoa1", ["trec.67x7p.top", "trec.kv8so.top"])
参考文档：
https://www.bt.cn/bbs/thread-23895-1-1.html
https://www.bt.cn/data/api-doc.pdf
bt-python-sdk: https://github.com/adamzhang1987/bt-python-sdk/blob/main/pybt/sites.py
"""

# 抑制不安全请求的警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class BtApi:
    # 最大重试次数
    MAX_RETRIES = 3
    
    def __init__(self, bt_panel=None, bt_key=None):
        """
        初始化
        :param bt_panel: 宝塔接口地址
        :param bt_key: 宝塔Api密钥
        """
        self.BT_KEY = "OcvMRo8h1lSGNzZ5h1kiZNlsQ7N87xtJ" if bt_key is None else bt_key  ##小舅1
        # self.BT_KEY = "QhVlphI8iLFntk852kIs0XuUgcCwdyJJ" if bt_key is None else bt_key
        self.BT_PANEL = "https://114.55.244.168:32803" if bt_panel is None else bt_panel  ##小舅1
        # self.BT_PANEL = "https://47.115.32.90:32803" if bt_panel is None else bt_panel
    
    @staticmethod
    def read_file(file_path: str) -> str:
        """读取文件内容并返回字符串"""
        with open(file_path, "r") as f:
            return f.read()

    def get_system_total(self):
        """
        获取系统基础统计
        :return: 统计数据
        """
        url = f"{self.BT_PANEL}/system?action=GetSystemTotal"
        p_data = self.get_key_data()
        result = self.http_post_cookie(url, p_data)
        return result
    
    def websites(self, search='', page='1', limit='15', type='-1', order='id desc', tojs=''):
        """
        获取网站列表
        :param search: 搜索内容
        :param page: 当前分页
        :param limit: 取出的数据行数
        :param type: 分类标识 -1: 分部分类 0: 默认分类
        :param order: 排序规则 使用 id 降序：id desc 使用名称升序：name desc
        :param tojs: 分页 JS 回调,若不传则构造 URI 分页连接
        :return: 网站列表数据
        """
        url = f"{self.BT_PANEL}/data?action=getData&table=sites"
        p_data = self.get_key_data()
        p_data['p'] = page
        p_data['limit'] = limit
        p_data['type'] = type
        p_data['order'] = order
        p_data['tojs'] = tojs
        p_data['search'] = search + '.com'
        result = self.http_post_cookie(url, p_data)
        return result
    
    def get_site_id(self, site):
        """
        获取指定站点ID 若站点不存在则返回-1
        Args: site (str): 网站名
        """
        data = self.websites()["data"]
        for i in data:
            if i["name"] == site + '.com':
                return i["id"]
        return -1
    
    def add_site(self, info_arr={}):
        """
        新增网站
        :param info_arr: 包含网站信息的字典
            webname: 网站域名 json格式
            path: 网站路径
            type_id: 网站分类ID
            type: 网站类型
            version: PHP版本
            port: 网站端口
            ps: 网站备注
            ftp: 网站是否开通FTP
            ftp_username: FTP用户名
            ftp_password: FTP密码
            sql: 网站是否开通数据库
            codeing: 数据库编码类型 utf8|utf8mb4|gbk|big5
            datauser: 数据库账号
            datapassword: 数据库密码
        :return: API响应结果
        """
        url = f"{self.BT_PANEL}/site?action=AddSite"
        # 准备POST数据
        p_data = self.get_key_data()  # 取签名
        p_data['webname'] = json.dumps({"domain": info_arr.get('webname') + '.com', "domainlist": [], "count": 0})
        p_data['path'] = info_arr.get('path', "/www/wwwroot/joke2.com")
        p_data['type_id'] = info_arr.get('type_id', '0')
        p_data['type'] = info_arr.get('type', "PHP")
        p_data['version'] = info_arr.get('version', "80")
        p_data['port'] = info_arr.get('port', "80")
        p_data['ps'] = info_arr.get('webname') + ".com"
        p_data['ftp'] = False
        # p_data['ftp_username'] = info_arr.get('ftp_username')
        # p_data['ftp_password'] = info_arr.get('ftp_password')
        p_data['sql'] = False
        # p_data['codeing'] = info_arr.get('codeing')
        # p_data['datauser'] = info_arr.get('datauser')
        # p_data['datapassword'] = info_arr.get('datapassword')
        # 请求面板接口
        result = self.http_post_cookie(url, p_data)
        return result

    def web_add_domain(self, webname, domain):
        """
        添加域名
        :param id: 网站ID
        :param webname: 网站名称
        :param domain: 要添加的域名:端口 80端口不必构造端口,多个域名用换行符隔开
        :return: API响应结果
        """
        url = f"{self.BT_PANEL}/site?action=AddDomain"
        p_data = self.get_key_data()
        p_data['id'] = self.get_site_id(webname)
        p_data['webname'] = webname + '.com'
        p_data['domain'] = domain
        result = self.http_post_cookie(url, p_data)
        return result
    
    def web_del_domain(self, webname, domain):
        """
        删除网站域名
        Args:
            id: 网站ID
            webname: 网站名
            domain: 网站域名，可以是单个域名字符串或域名列表["a.com", "b.com", "c.com"]
            port: 网站域名端口
        Returns:
            dict/list: 单个域名时返回API响应字典，多个域名时返回包含所有响应的列表
        """
        url = f"{self.BT_PANEL}/site?action=DelDomain"
        p_data = self.get_key_data()
        p_data['id'] = self.get_site_id(webname)
        p_data['webname'] = webname + '.com'
        p_data['port'] = '80'
        # 处理单个域名的情况（向后兼容）
        if isinstance(domain, str):
            p_data['domain'] = domain
            result = self.http_post_cookie(url, p_data)
            return result
        # 处理多个域名的情况
        results = []
        for single_domain in domain:
            p_data['domain'] = single_domain
            result = self.http_post_cookie(url, p_data)
            results.append({"domain": single_domain, "result": result})
        return results

    def web_domain_list(self, webname, list=True):
        """
        获取网站域名列表
        :param id: 网站ID
        :param list: 固定传True
        :return: 返回的JSON数据
        """
        url = f"{self.BT_PANEL}/data?action=getData&table=domain"
        p_data = self.get_key_data()
        p_data['search'] = self.get_site_id(webname)
        p_data['list'] = list
        result = self.http_post_cookie(url, p_data)
        return result

    def set_ssl(self, webname, key, csr):
        """
        设置SSL域名证书
        Args:
            type: 类型
            site: 网站名
            key: 证书key
            csr: 证书PEM
        Returns:
            dict: API response data
        """
        url = f"{self.BT_PANEL}/site?action=SetSSL"
        p_data = self.get_key_data()
        p_data['type'] = '0'
        p_data['siteName'] = webname + '.com'
        p_data['key'] = key
        p_data['csr'] = csr
        result = self.http_post_cookie(url, p_data)
        return result

    def get_key_data(self):
        """
        构造带有签名的关联数组
        :return: 包含请求令牌和时间的字典
        """
        now_time = int(time.time())
        p_data = {
            'request_token': hashlib.md5(f"{now_time}{hashlib.md5(self.BT_KEY.encode()).hexdigest()}".encode()).hexdigest(),
            'request_time': now_time
        }
        return p_data
    
    def http_post_cookie(self, url, data, timeout=60):
        """
        发起POST请求，支持重试机制
        :param url: 目标网址，带http://
        :param data: 欲提交的数据
        :param timeout: 超时时间，默认60秒
        :return: 请求结果
        """
        cookie_file = f"./{hashlib.md5(self.BT_PANEL.encode()).hexdigest()}.cookie"
        session = requests.Session()
        
        # 重试计数器
        retry_count = 0
        last_exception = None
        
        # 实现重试逻辑
        while retry_count < self.MAX_RETRIES:
            try:
                # 尝试发送请求
                response = session.post(
                    url,
                    data=data,
                    timeout=timeout,
                    verify=False  # 不验证SSL证书
                )
                
                # 请求成功，保存cookies到文件
                with open(cookie_file, 'w') as f:
                    json.dump(requests.utils.dict_from_cookiejar(session.cookies), f)
                
                # 尝试解析并返回JSON
                try:
                    return response.json()
                except:
                    return response.text
                    
            except (requests.exceptions.ConnectionError, 
                    requests.exceptions.Timeout, 
                    requests.exceptions.RequestException) as e:
                # 捕获请求异常
                last_exception = e
                retry_count += 1
                
                # 如果已达到最大重试次数，则跳出循环
                if retry_count >= self.MAX_RETRIES:
                    break
                
                # 指数退避策略：等待时间随重试次数增加
                wait_time = 2 ** retry_count  # 1次:2秒, 2次:4秒, 3次:8秒
                print(f"请求失败: {str(e)}，{wait_time}秒后进行第{retry_count+1}次重试...")
                time.sleep(wait_time)
        
        # 所有重试都失败，记录错误并返回错误信息
        error_msg = f"请求失败，已重试{self.MAX_RETRIES}次: {str(last_exception)}"
        print(error_msg)
        return {"status": False, "msg": error_msg}

if __name__ == "__main__":
    # 测试时手动设置 sys.argv（无需命令行输入）
    # sys.argv = [
    #     "auto_bt_api.py",  # 脚本名（占位，无实际作用）
    #     'bt.web_del_domain("autoa3", ["999.com", "998.com"])'  # 要执行的表达式
    # ]
    

    if len(sys.argv) < 2:
        print("Usage: python script.py 'bt.method_name(args)'")
        sys.exit(1)
    bt = BtApi()
    # 获取命令行参数:python3.8 bt_api.py 'bt.add_site({"webname": "autob1"})'
    call_expr = sys.argv[1]
    try:
        websites_data = eval(call_expr, {"bt": bt, "read_file": bt.read_file})
        print(json.dumps(websites_data, ensure_ascii=False))
    except Exception as e:
        print(f"Error executing '{call_expr}': {e}")

    
    # 获取网站列表并输出JSON
    # websites_data = bt.get_system_total()
    # websites_data = bt.add_site({"webname": "auto1"})
    # websites_data = bt.websites(search='auto1')
    # websites_data = bt.get_site_id("auto1")
    # websites_data = bt.web_add_domain("auto1","444.com,555.com,666.com")
    # websites_data = bt.web_del_domain("autoa1","trec.6nwkx.top")
    # websites_data = bt.web_del_domain("autoa1", ["trec.67x7p.top", "trec.kv8so.top"])
    # websites_data = bt.web_domain_list("auto1")
    
    ### 命令行参考
    # python3.8 auto_bt_api.py 'bt.add_site({"webname": "autob2"})'
    # python3.8 auto_bt_api.py 'bt.web_add_domain("autob2","x43010.jieruitech.info")'
    # python3.8 auto_bt_api.py 'bt.web_del_domain("autob2",["x43010.jieruitech.info"])'
    # python3.8 auto_bt_api.py 'bt.web_domain_list("autob1")'