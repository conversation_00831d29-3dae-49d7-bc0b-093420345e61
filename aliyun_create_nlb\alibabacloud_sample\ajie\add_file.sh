#!/bin/bash
random_num=$RANDOM$RANDOM
num_cut=`echo $random_num |cut -c 1-9`
ng_root=/usr/share/nginx/html
ng_html=$1/$num_cut
api_file=/usr/share/nginx/html/api_data.php

if [ "${1}" != "" ] && [ -f ${1}.html ];then
    mkdir -p ${ng_root}/${ng_html}
    mv ${1}.html ${ng_root}/${ng_html}/
    html_file=`cd ${ng_root} && ls ${ng_html}/${1}.html -d`
    echo ${html_file}
    ## 拿到关键字行号
    api_num=`grep -n ');' $api_file | cut -d ':' -f1`
    ###        'xy9xs' => array('file' => '/xy9xs/5158252/xy9xs.html', 'user' => 1),
    sed -i "${api_num}s:^:        '${1}' => array('file' => '/${ng_html}/${1}.html', 'user' => 1),\n:" ${api_file}
else
    echo "error:请确保目录中有需要添加的html文件，并且$1参数不为空"
    echo "举例:sh add_file.sh 222"
    exit 3
fi
