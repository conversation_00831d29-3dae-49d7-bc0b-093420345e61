#!/bin/bash

# 定义域名列表文件
DOMAINS_FILE="$1"
# 检查执行携带参数是否正常
if [ $# -lt 1 ]; then
    echo "Usage: $0 <arg1>"
    exit 1
fi

# 定义 acme.sh 命令的基本部分
BASE_COMMAND="sh /root/.acme.sh/acme.sh --issue --server letsencrypt -w /usr/share/nginx/html --cert-home /root/.acme.sh/${DOMAINS_FILE} --force"

# 读取域名列表文件
DOMAINS=$(cat "$DOMAINS_FILE")

# 初始化命令字符串
COMMAND="$BASE_COMMAND"

# 为每个域名添加 -d 参数
for domain in $DOMAINS; do
    COMMAND="$COMMAND -d $domain"
done

# 执行命令
echo "$COMMAND"
$COMMAND
