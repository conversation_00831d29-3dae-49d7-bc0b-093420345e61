#!/bin/bash
set -euo pipefail
NGINX_CONF="${1}"

echo "===== 调试开始 ====="
[ ! -f "$NGINX_CONF" ] && echo "文件不存在" && exit 1

# 提取server_name中的IP
ips=$(perl -0777 -ne 'print "$1" while /server_name\s+((?:.|\n)+?)\s*;/gs' "$NGINX_CONF" | grep -oP '\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b' | sort -u)
echo "提取的IP列表:"
echo "$ips"

# 检查是否已存在通用监听行（无IP的listen 443）
if grep -q '^\s*listen\s\+443\s\+ssl\s\+http2;' "$NGINX_CONF"; then
  echo "检测到通用监听行: listen 443 ssl http2;"
else
  echo "未检测到通用监听行，将添加"
fi

# 为每个IP添加监听行
added=0
for ip in $ips; do
  if ! grep -q "listen $ip:443 ssl http2;" "$NGINX_CONF"; then
    echo "添加: listen $ip:443 ssl http2;"
    sed -i "/^\s*server_name\s\+/i \\        listen $ip:443 ssl http2;" "$NGINX_CONF"
    added=1
  else
    echo "已存在: listen $ip:443"
  fi
done

[ $added -eq 0 ] && echo "无需添加新监听"
echo "===== 操作完成 ====="
