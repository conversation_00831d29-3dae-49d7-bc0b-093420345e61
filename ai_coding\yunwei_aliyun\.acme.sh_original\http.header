HTTP/1.1 200 OK
Date: Sun, 09 Mar 2025 13:48:45 GMT
Content-Type: application/json; charset=utf-8
Cache-Control: public, max-age=60, s-maxage=60
Vary: Accept,Accept-Encoding, Accept, X-Requested-With
ETag: W/"1d37c6a37be6d50043567b38f2f85dfbcb9f4a9c4c507d4a7d10fc9313bfef78"
Last-Modified: Sun, 09 Mar 2025 12:30:48 GMT
X-Poll-Interval: 300
X-GitHub-Media-Type: github.v3; format=json
x-github-api-version-selected: 2022-11-28
Access-Control-Expose-Headers: ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OAuth-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset
Access-Control-Allow-Origin: *
Strict-Transport-Security: max-age=31536000; includeSubdomains; preload
X-Frame-Options: deny
X-Content-Type-Options: nosniff
X-XSS-Protection: 0
Referrer-Policy: origin-when-cross-origin, strict-origin-when-cross-origin
Content-Security-Policy: default-src 'none'
Server: github.com
Accept-Ranges: bytes
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1741531724
X-RateLimit-Resource: core
X-RateLimit-Used: 1
Content-Length: 357
X-GitHub-Request-Id: D95A:27873D:3FD58:5B2E9:67CD9C3C

