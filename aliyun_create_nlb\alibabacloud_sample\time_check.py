#!/usr/bin/env python
# -*- coding: utf-8 -*-
import base64
import hashlib
import hmac
import urllib
import requests
import time
import logging

# 配置日志，打印告警信息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# 钉钉告警拼接
def get_digest():
    # 取毫秒级别时间戳，round(x, n) 取x小数点后n位的结果，默认取整
    timestamp = str(round(time.time() * 1000))
    secret = 'SECcadb06773f5af50923dd2f63f5f61aef2aa3cf98da5f2fd03cf834297869ec6c'
    secret_enc = secret.encode('utf-8')  # utf-8编码
    string_to_sign = '{}\n{}'.format(timestamp, secret)  # 字符串格式化拼接
    string_to_sign_enc = string_to_sign.encode('utf-8')  # utf-8编码
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()  # HmacSHA256算法计算签名
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))  # Base64编码后进行urlEncode
    #  返回时间戳和计算好的编码拼接字符串，后面直接拼接到Webhook即可
    return f"&timestamp={timestamp}&sign={sign}"


# 钉钉告警
def warning_bot(message):
    data = {
        "msgtype": "text",
        "text": {
            "content": message
        },
    }
    # 机器人链接地址，发post请求 向钉钉机器人传递指令
    webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token' \
                  '=9fb7741f17e86008209eee7a3db93dd32caa9b26369fa6633a10c3d25de2cb8f '
    # 利用requests发送post请求
    req = requests.post(webhook_url + get_digest(), json=data)
    print(req.status_code)


def get_time_from_url(url):
    try:
        # 发送 HTTP GET 请求
        response = requests.get(url, timeout=10)

        # 检查响应状态
        if response.status_code == 200:
            # 假设返回的是一个纯文本时间戳，直接解析
            try:
                timestamp = int(response.text.strip())  # 转换为整型
                return timestamp
            except ValueError:
                logging.error("返回的内容无法转换为有效的时间戳")
                return None
        else:
            logging.error(f"HTTP 请求失败，状态码: {response.status_code}")
            return None

    except requests.RequestException as e:
        logging.error(f"请求发生异常: {e}")
        return None


def check_time_difference(urls, max_diff=240):
    # 获取当前时间戳
    current_timestamp = int(time.time())

    for url in urls:
        # 从指定 URL 获取时间戳
        timestamp_from_url = get_time_from_url(url)

        if timestamp_from_url is None:
            logging.warning(f"未能从 URL 获取有效的时间戳，无法进行对比！(URL: {url})")
            continue  # 如果某个文件无法获取时间戳，继续监控下一个

        # 计算时间差
        time_diff = current_timestamp - timestamp_from_url

        # 如果时间差大于给定的最大差值，发送告警
        if time_diff > max_diff:
            logging.warning(
                f"警告！时间戳差异超过 {max_diff} 秒！当前时间戳: {current_timestamp}, 返回的时间戳: {timestamp_from_url}, 差值: {time_diff}秒")
            # message = f"警告！时间戳差异超过 {max_diff} 秒!!"
            # warning_bot(message)
        else:
            logging.info(
                f"时间戳差异正常，当前时间戳: {current_timestamp}, 返回的时间戳: {timestamp_from_url}, 差值: {time_diff}秒")


# 使用示例 URL 列表，监控多个文件
urls = [
    "http://39.106.57.216:8888/time_pagetask.txt",
    "http://39.106.57.216:8888/time_pictask.txt"
]

while True:
    check_time_difference(urls)
    time.sleep(30)