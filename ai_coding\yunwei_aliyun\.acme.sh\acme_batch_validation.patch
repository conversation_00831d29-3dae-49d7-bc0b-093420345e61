--- acme.sh	2025-04-22 01:21:54
+++ acme.sh.new	2025-04-22 01:21:54
@@ -4919,6 +4919,9 @@
     fi
 
   fi
+  
+  # Initialize variables for batch processing
+  _all_wellknown_paths=""
 
   if [ "$dns_entries" ]; then
     if [ -z "$Le_DNSSleep" ]; then
@@ -4952,6 +4955,7 @@
 
   _ncIndex=1
   ventries=$(echo "$vlist" | tr "$dvsep" ' ')
+  _pending_validations=""
   for ventry in $ventries; do
     d=$(echo "$ventry" | cut -d "$sep" -f 1)
     keyauthorization=$(echo "$ventry" | cut -d "$sep" -f 2)
@@ -5025,7 +5029,6 @@
         _debug wellknown_path "$wellknown_path"
 
         _debug "Writing keyauthorization: $keyauthorization to $wellknown_path/$token"
-
         mkdir -p "$wellknown_path"
         
         if ! printf "%s" "$keyauthorization" > "$wellknown_path/$token"; then
@@ -5038,38 +5041,69 @@
         if ! chmod a+r "$wellknown_path/$token"; then
           _debug "chmod failed, will just continue."
         fi
-        # 同步验证文件到其他服务器
-        if ! _sync_to_remote_servers "$wellknown_path/$token" "$wellknown_path/"; then
-          _err "Failed to sync verification file to remote servers"
-          _clearupwebbroot "$_currentRoot" "$removelevel" "$token"
-          _clearup
-          _on_issue_err "$_post_hook" "$vlist"
-          return 668
-        fi
-
+        
+        # Store the wellknown path for batch syncing later
+        if ! _contains "$_all_wellknown_paths" "$wellknown_path"; then
+          _all_wellknown_paths="$_all_wellknown_paths $wellknown_path"
+          _debug "Added to wellknown paths: $wellknown_path"
+        fi
       fi
     elif [ "$vtype" = "$VTYPE_ALPN" ]; then
       acmevalidationv1="$(printf "%s" "$keyauthorization" | _digest "sha256" "hex")"
       _debug acmevalidationv1 "$acmevalidationv1"
       if ! _starttlsserver "$d" "" "$Le_TLSPort" "$keyauthorization" "$_ncaddr" "$acmevalidationv1"; then
         _err "Error starting TLS server."
         _clearupwebbroot "$_currentRoot" "$removelevel" "$token"
         _clearup
         _on_issue_err "$_post_hook" "$vlist"
         return 1
       fi
     fi
 
-    if ! __trigger_validation "$uri" "$keyauthorization" "$vtype"; then
-      _err "$d: Cannot get challenge: $response"
-      _clearupwebbroot "$_currentRoot" "$removelevel" "$token"
-      _clearup
-      _on_issue_err "$_post_hook" "$vlist"
-      return 1
+    # Store validation information for batch processing
+    if [ "$vtype" = "$VTYPE_HTTP" ]; then
+      _pending_validations="$_pending_validations$d$sep$uri$sep$keyauthorization$sep$vtype$sep$_currentRoot$sep$removelevel$sep$token$dvsep"
     fi
+  done
+  
+  # Sync all wellknown paths to remote servers at once
+  if [ -n "$_all_wellknown_paths" ]; then
+    _info "Syncing all verification files to remote servers"
+    for _path in $_all_wellknown_paths; do
+      _info "Syncing directory: $_path"
+      if ! _sync_to_remote_servers "$_path" "$_path"; then
+        _err "Failed to sync verification files from $_path to remote servers"
+        _clearup
+        _on_issue_err "$_post_hook" "$vlist"
+        return 668
+      fi
+    done
+  fi
+  
+  # Trigger all validations at once
+  _info "Triggering all validations"
+  _pending_entries=$(echo "$_pending_validations" | tr "$dvsep" ' ')
+  for _pentry in $_pending_entries; do
+    d=$(echo "$_pentry" | cut -d "$sep" -f 1)
+    uri=$(echo "$_pentry" | cut -d "$sep" -f 2)
+    keyauthorization=$(echo "$_pentry" | cut -d "$sep" -f 3)
+    vtype=$(echo "$_pentry" | cut -d "$sep" -f 4)
+    _currentRoot=$(echo "$_pentry" | cut -d "$sep" -f 5)
+    removelevel=$(echo "$_pentry" | cut -d "$sep" -f 6)
+    token=$(echo "$_pentry" | cut -d "$sep" -f 7)
+    
+    _info "Triggering validation for: $d"
+    if ! __trigger_validation "$uri" "$keyauthorization" "$vtype"; then
+      _err "$d: Cannot get challenge: $response"
+      _clearupwebbroot "$_currentRoot" "$removelevel" "$token"
+      _clearup
+      _on_issue_err "$_post_hook" "$vlist"
+      return 1
+    fi
 
     if [ "$code" ] && [ "$code" != '202' ]; then
       if [ "$code" = '200' ]; then
         _debug "Trigger validation code: $code"
       else
         _err "$d: Challenge error: $response"
         _clearupwebbroot "$_currentRoot" "$removelevel" "$token"
@@ -5079,6 +5113,12 @@
       fi
     fi
+  done
+  
+  # Check validation status for all domains
+  _pending_entries=$(echo "$_pending_validations" | tr "$dvsep" ' ')
+  for _pentry in $_pending_entries; do
+    d=$(echo "$_pentry" | cut -d "$sep" -f 1)
+    uri=$(echo "$_pentry" | cut -d "$sep" -f 2)
+    keyauthorization=$(echo "$_pentry" | cut -d "$sep" -f 3)
+    vtype=$(echo "$_pentry" | cut -d "$sep" -f 4)
+    _currentRoot=$(echo "$_pentry" | cut -d "$sep" -f 5)
+    removelevel=$(echo "$_pentry" | cut -d "$sep" -f 6)
+    token=$(echo "$_pentry" | cut -d "$sep" -f 7)
+    _authz_url=$(echo "$vlist" | tr "$dvsep" '\n' | grep "^$d$sep" | cut -d "$sep" -f 6)
+    _debug "_authz_url" "$_authz_url"
 
     waittimes=0
     if [ -z "$MAX_RETRY_TIMES" ]; then
@@ -5171,7 +5211,7 @@
         fi
       fi
     done
-
+    
   done
 
   _clearup
@@ -5431,6 +5471,14 @@
 # 添加同步函数
 _sync_to_remote_servers() {
   _src="$1"
+  _dest="$1"
+  
+  # If source is a directory, sync the entire directory
+  if [ -d "$_src" ]; then
+    _debug "Source is a directory, syncing entire directory"
+    _src="${_src}/"
+  fi
+  
   _dest="$2"
   
   if [ -z "$SYNC_SERVERS" ]; then
