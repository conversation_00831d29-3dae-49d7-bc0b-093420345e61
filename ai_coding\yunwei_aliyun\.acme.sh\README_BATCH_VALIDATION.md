# 批量域名验证优化

## 问题描述

当前 acme.sh 脚本在验证多个域名时，会对每个域名进行以下操作：
1. 创建验证文件
2. 同步验证文件到远程服务器
3. 触发验证
4. 等待验证完成

这种方式在域名数量较多时效率较低，因为每个域名都需要单独同步文件到远程服务器。

## 优化方案

此补丁修改了验证流程，实现了以下优化：
1. 先为所有域名创建验证文件
2. 一次性同步整个 `.well-known/acme-challenge` 目录到远程服务器
3. 批量触发所有域名的验证
4. 检查所有域名的验证状态

## 应用补丁

使用以下命令应用补丁：

```bash
cd /path/to/acme.sh/directory
cp acme.sh acme.sh.bak  # 备份原始文件
patch -p0 < acme_batch_validation.patch
```

## 补丁说明

1. 添加了批量处理验证的逻辑
2. 修改了 `_sync_to_remote_servers` 函数以支持同步整个目录
3. 保持了原有的验证逻辑，只是改变了执行顺序

## 注意事项

1. 此补丁主要针对 HTTP 验证方式进行了优化
2. 在应用补丁前请务必备份原始文件
3. 如果遇到问题，可以随时恢复备份文件
