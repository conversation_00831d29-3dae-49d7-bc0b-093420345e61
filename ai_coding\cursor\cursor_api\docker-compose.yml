version: '3.8'
services:
  cursor-api:
    image: devinglaw/cursor2api:latest
    container_name: cursor2api
    ports:
      - "3998:3000"   # 映射宿主机的3998端口到容器的3000端口
    env_file:
      - .env   # 引用 .env 文件
    environment:
      # - x-cursor-checksum=${x-cursor-checksum}  # 从 .env 文件加载 x-cursor-checksum
      - NODE_ENV=production       # 例如，如果是 Node.js 应用，设置生产环境变量
      - LOG_LEVEL=debug           # 增加日志级别设置
      - DATABASE_URL=  # 例如，数据库的环境变量
    volumes:
      - cursor-api-data:/data    # 将数据持久化存储
    restart: unless-stopped      # 容器异常退出时自动重启，除非被明确停止
    logging:
      driver: "json-file"         # 日志驱动
      options:
        max-size: "10m"           # 日志文件最大尺寸
        max-file: "3"             # 最大保留日志文件数
    networks:
      - cursor-net                # 将容器加入到指定的网络
volumes:
  cursor-api-data:

networks:
  cursor-net: