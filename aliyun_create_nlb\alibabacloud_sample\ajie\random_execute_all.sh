#!/bin/bash

## nohup sh /root/ajie/random_execute_all.sh >> /root/ajie/random_all.log 2>&1 &
while true; do
  # 生成一个55-65之间的随机数
  interval=$(( (RANDOM % 11) + 55 ))
  # 将时间间隔转换为秒数
  sleep_duration=$(( interval * 60 ))

  # 执行命令
  /usr/bin/sh /root/ajie/main7.sh >> /root/ajie/main_all.log
  echo "finish main7.sh" `date +"%Y%m%d %H:%M:%S"`
  /usr/bin/sh /root/ajie/main6.sh >> /root/ajie/main_all.log
  echo "finish main6.sh" `date +"%Y%m%d %H:%M:%S"`
  /usr/bin/sh /root/ajie/main5.sh >> /root/ajie/main_all.log
  echo "finish main5.sh" `date +"%Y%m%d %H:%M:%S"`
  /usr/bin/sh /root/ajie/main4.sh >> /root/ajie/main_all.log
  echo "finish main4.sh" `date +"%Y%m%d %H:%M:%S"`
  /usr/bin/sh /root/ajie/main3.sh >> /root/ajie/main_all.log
  echo "finish main3.sh" `date +"%Y%m%d %H:%M:%S"`
  /usr/bin/sh /root/ajie/main1.sh >> /root/ajie/main_all.log
  echo "finish main1.sh" `date +"%Y%m%d %H:%M:%S"`

  # 等待指定的时间间隔
  sleep "$sleep_duration"
done
