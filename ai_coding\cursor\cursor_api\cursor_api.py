import asyncio
import random
import string
from DrissionPage import Chromium
import requests
from mailtmapi import MailTM
import time
import re
import os
from aiohttp import ClientSession
def generate_temp_password():
    """生成一个临时密码"""
    return "".join(random.choices(string.ascii_letters + string.digits, k=12))
async def fetch_email(mailtm, temp_mail, BASE_URL):
    """获取邮件并返回验证码"""
    try:
        # 获取最新邮件，最多取一封
        messages = await mailtm.get_messages(temp_mail.token.token, 1)
        if not messages.hydra_member:
            print("No email received, retrying...")
            return None
        message = messages.hydra_member[0]
        # 下载邮件内容
        response = requests.get(BASE_URL + message.downloadUrl,
                                headers={'Authorization': f'Bearer {temp_mail.token.token}'})
        message_text = response.text.strip().replace('\n', '').replace('\r', '').replace('=', '')
        # 提取验证码
        match = re.search(r'Your verification code is (\d+)', message_text)
        if match:
            return match.group(1).strip()
        else:
            print("Verification code not found in email.")
            return None
    except Exception as e:
        print(f"Error fetching email: {e}")
        return None
async def main():
    BASE_URL = "https://api.mail.tm"  # 定义BASE_URL
    mailtm = MailTM()
    temp_mail = await mailtm.get_account()  # 获取临时邮箱
    password = generate_temp_password()  # 生成临时密码
    # 打开注册页面
    tab = Chromium().latest_tab
    tab.get("https://authenticator.cursor.sh/sign-up")
    # 输入邮箱
    print("Input email")
    email_input = tab.ele("@id=radix-:R2bapnltbnla:")
    email_input.input(temp_mail.address)
    time.sleep(1)
    # 点击继续按钮
    print("Click continue")
    tab.ele('@text()=Continue').click()
    time.sleep(3)
    # 输入密码
    print("Waiting for password input element...")
    try:
        # 通过timeout参数等待元素加载
        password_input = tab.ele("@id=radix-:r1:", timeout=10)
        print("Input password")
        password_input.input(password)
        time.sleep(1)
        # 点击继续按钮
        print("Click continue")
        tab.ele('@text()=Continue').click()
    except Exception as e:
        print(f"Error locating password input element: {e}")
        return
    print("Waiting for email...")

    # 获取验证码，最多尝试10次
    verify_code = None
    for _ in range(10):
        verify_code = await fetch_email(mailtm, temp_mail, BASE_URL)
        if verify_code:
            break
        time.sleep(5)
    if not verify_code:
        print("Failed to get verification code, exiting.")
        return
    print(f"Verification code: {verify_code}")
    # 直接模拟键盘输入验证码
    for i in verify_code:
        tab.actions.key_down(str(i))
        time.sleep(0.1)
        tab.actions.key_up(str(i))
    time.sleep(5)
    # 获取cookie中的WorkosCursorSessionToken
    cookies = tab.cookies().as_dict()
    print("Register Success!")
    print(f"Account: {temp_mail.address}")
    print(f"Password: {password}")
    print(f"Cookies: {cookies}")
    # 确保output文件夹存在
    os.makedirs('output', exist_ok=True)
    # 保存账号信息到output目录下
    with open('output/cursor_accounts.txt', 'a', encoding='utf-8') as f:
        f.write(f"{temp_mail.address}:{password}\n")
        f.write(f"Cookies: {cookies}\n")
        f.write("\n")
    # 提取并保存 WorkosCursorSessionToken
    extract_and_save_token(cookies)
def extract_and_save_token(cookies):
    """从 cookies 提取 WorkosCursorSessionToken 并保存到文件"""
    # 1. 每次程序运行时先清空 cursor_accounts.txt 文件
    with open('cursor_accounts.txt', 'w', encoding='utf-8') as f:
        pass  # 清空文件内容
    # 2. 读取 cursor_accounts.txt 内容并去重
    try:
        with open('cursor_accounts.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        # 去重：通过 set() 去重后转回 list 保证唯一性
        unique_lines = list(set(lines))
        # 清空 cursor_accounts.txt 并写入去重后的内容
        with open('cursor_accounts.txt', 'w', encoding='utf-8') as f:
            f.writelines(unique_lines)
    except FileNotFoundError:
        print("cursor_accounts.txt 文件未找到")
    # 3. 提取 WorkosCursorSessionToken
    if 'WorkosCursorSessionToken' in cookies:
        token = cookies['WorkosCursorSessionToken']
        # 4. 读取现有的 tokens
        existing_tokens = []
        try:
            with open('output/token.txt', 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    existing_tokens = content.split(',')
        except FileNotFoundError:
            pass
        # 5. 添加新 token 并去重
        if token not in existing_tokens:
            existing_tokens.append(token)
        # 6. 去重：去掉重复的 token（包括现有的和新添加的 token）
        unique_tokens = list(set(existing_tokens))
        # 7. 保存所有去重后的 tokens 到 token.txt, 用逗号连接
        with open('output/token.txt', 'w', encoding='utf-8') as f:
            f.write(','.join(unique_tokens))
        # 8. 保存到 .env 文件
        with open('output/.env', 'w', encoding='utf-8') as f:
            f.write(f"AUTH_TOKENS={','.join(unique_tokens)}")
        print(f"Token {token} successfully added and saved.")
    else:
        print("No WorkosCursorSessionToken found in cookies.")

if __name__ == "__main__":
    repeat_times = 5  # 注册次数
    for i in range(repeat_times):
        try:
            asyncio.run(main())
        except Exception as e:
            print(f"Error during registration: {e}")