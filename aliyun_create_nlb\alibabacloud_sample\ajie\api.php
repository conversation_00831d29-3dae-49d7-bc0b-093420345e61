<?php
//自动更新跳转网址接口
error_reporting(0);
print_r($_GET);exit;
$domain_file = 'ddomain.txt';//域名保存文件，将文件放在根目录，可以修改为任意文件名，但是文件与这里都需要修改，否则可能出错！
$api_password = '123';//接口密码，接口自动更新跳转域名使用
//下方为跳转列表，不在列表中的将报404错误
$list = array(
	'db667' => '/db667/789231/db667.html',//前面键名为本地访问的文件名，键名全部小写进行适配，后面键值为跳转地址不带域名的，以/开头
	'db668' => '/db667/789231/db667.html',//前面键名为本地访问的文件名，键名全部小写进行适配，后面键值为跳转地址不带域名的，以/开头
);
$act = strtolower(trim($_GET['act']));
$api_update = trim($_GET['api_update']);
$api_domain = trim($_GET['api_domain']);
if(!empty($api_update) && !empty($api_domain) && $api_update === $api_password){
	file_put_contents($domain_file, $api_domain);
	echo "ok";exit;
}else if(!empty($act) && !empty($list[$act])){
	$domain = file_get_contents($domain_file);
	if(!empty($domain)){
		//域名正常，直接跳转
		header("Location: ".$domain.$list[$act]);
		exit();
	}else{
		//如果域名文件内容为空，则直接返回404
		header('HTTP/1.1 404 Not Found');
		exit();
	}
}else{
	header('HTTP/1.1 404 Not Found');
	exit();
}