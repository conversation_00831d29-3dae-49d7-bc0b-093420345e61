#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import time
import json
import logging
import random
from typing import Dict, List, Tuple, Set

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='domain_monitor.log',
    filemode='a'
)
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

class DomainMonitor:
    def __init__(self, domain_list: List[str], check_interval: int = 5, max_failures: int = 3):
        """
        初始化域名监控类
        
        Args:
            domain_list: 待监控的域名列表
            check_interval: 检查间隔时间(秒)
            max_failures: 最大失败次数，超过此次数将切换CNAME
        """
        self.domain_list = domain_list
        self.check_interval = check_interval
        self.max_failures = max_failures
        self.failure_counts = {domain: 0 for domain in domain_list}
        self.modified_domains = set()  # 记录已经修改过CNAME的域名
        
        # DNSPod API配置 - 腾讯云API
        self.secret_id = 'AKIDe6MQHuJVUG0Ys0znPo8UsBIvSmRxGNHx'
        self.secret_key = 'XqwoSt66tSoAMNZjneIl38zc2FE0hOwH'
        self.host = 'dnspod.tencentcloudapi.com'
        self.api_version = '2021-03-23'
        
        # CNAME配置 - 备用解析值
        self.backup_cname = 'm3253.jieruitech.info'  # 监控别名失败后要CNAME的值
    
    def check_domain_status(self, domain: str) -> int:
        """
        检查域名HTTP状态
        
        Args:
            domain: 要检查的域名
            
        Returns:
            HTTP状态码
        """
        try:
            # 添加超时，避免某个域名长时间无响应
            response = requests.get(f"http://{domain}/123.txt", timeout=10)
            return response.status_code
        except requests.exceptions.RequestException as e:
            logging.error(f"请求域名 {domain} 失败: {e}")
            return 0  # 请求失败返回0
    
    def modify_cname_record(self, domain: str) -> bool:
        """
        调用DNSPod API修改域名的CNAME记录
        
        Args:
            domain: 需要修改CNAME的域名
            
        Returns:
            是否修改成功
        """
        if domain in self.modified_domains:
            logging.warning(f"域名 {domain} 已经修改过CNAME，跳过")
            return False
        
        # 提取主域名和子域名
        domain_parts = domain.split('.')
        if len(domain_parts) < 2:
            logging.error(f"域名格式错误: {domain}")
            return False
        
        # 假设最后两部分是主域名（如example.com），前面的是子域名
        main_domain = '.'.join(domain_parts[-2:])
        sub_domain = '.'.join(domain_parts[:-2]) if len(domain_parts) > 2 else '@'
        
        # 首先需要获取域名的记录列表，找到对应的记录ID
        record_id = self.get_record_id(main_domain, sub_domain)
        if not record_id:
            logging.error(f"无法找到域名 {domain} 的记录ID")
            return False
        
        # 准备修改CNAME的API请求数据 - 使用腾讯云API
        params = {
            "Action": "ModifyRecord",
            "Version": self.api_version,
            "Domain": main_domain,
            "RecordId": record_id,
            "SubDomain": sub_domain,
            "RecordType": "CNAME",
            "RecordLine": "默认",
            "Value": self.backup_cname,
            "TTL": 600
        }
        
        # 生成签名并发送请求
        try:
            url = self._generate_signed_url(params)
            response = requests.get(url)
            result = response.json()
            
            if result.get('Response') and not result.get('Response').get('Error'):
                logging.info(f"成功修改域名 {domain} 的CNAME记录为 {self.backup_cname}")
                self.modified_domains.add(domain)  # 标记为已修改
                self.failure_counts[domain] = 0  # 重置失败计数器
                return True
            else:
                logging.error(f"修改域名 {domain} 的CNAME记录失败: {result}")
                return False
                
        except Exception as e:
            logging.error(f"调用DNSPod API修改域名 {domain} 的CNAME记录时发生错误: {e}")
            return False
    
    def _generate_signed_url(self, params: Dict) -> str:
        """
        生成带签名的URL - 按照腾讯云API签名算法要求实现
        
        Args:
            params: 请求参数
            
        Returns:
            带签名的完整URL
        """
        # 添加公共参数
        timestamp = int(time.time())
        params["Timestamp"] = timestamp
        params["Nonce"] = random.randint(1000000, 9999999)  # 与PHP mt_rand(1000000, 9999999)一致
        params["SecretId"] = self.secret_id
        params["SignatureMethod"] = "HmacSHA256"
        
        # 1. 按字典序排序参数
        sorted_params = dict(sorted(params.items()))
        
        # 2. 构造规范请求字符串
        import urllib.parse
        request_str = "GET" + self.host + "/?"
        
        # 3. 构造待签名字符串
        param_list = []
        for k, v in sorted_params.items():
            param_list.append(f"{k}={v}")
        param_str = "&".join(param_list)
        string_to_sign = "GET" + self.host + "/" + "?" + param_str
        
        # 4. 计算签名
        import base64
        import hmac
        import hashlib
        signature = base64.b64encode(
            hmac.new(
                self.secret_key.encode('utf-8'),
                string_to_sign.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        # 5. 构造最终URL
        # 使用urlencode处理所有参数
        encoded_params = urllib.parse.urlencode(sorted_params)
        url = f"https://{self.host}/?{encoded_params}&Signature={urllib.parse.quote(signature)}"
        
        return url
    
    def get_record_id(self, main_domain: str, sub_domain: str) -> str:
        """
        获取指定域名记录的ID
        
        Args:
            main_domain: 主域名
            sub_domain: 子域名或@
            
        Returns:
            记录ID，如果找不到则返回空字符串
        """
        # 准备API请求参数
        params = {
            "Action": "DescribeRecordList",
            "Version": self.api_version,
            "Domain": main_domain,
            "Subdomain": sub_domain,
            "RecordType": "CNAME"
        }
        
        try:
            # 生成签名URL并发送请求
            url = self._generate_signed_url(params)
            response = requests.get(url)
            result = response.json()
            
            if result.get('Response') and not result.get('Response').get('Error'):
                # 遍历记录列表，找到匹配的记录
                records = result.get('Response', {}).get('RecordList', [])
                for record in records:
                    if record.get('Name') == sub_domain and record.get('Type') == 'CNAME':
                        return str(record.get('RecordId', ''))
            
            logging.warning(f"未找到域名 {sub_domain}.{main_domain} 的CNAME记录")
            return ''
                
        except Exception as e:
            logging.error(f"获取域名 {sub_domain}.{main_domain} 的记录列表时发生错误: {e}")
            return ''
    
    def run(self):
        """
        运行监控循环
        """
        logging.info("开始域名监控...")
        
        while True:
            for domain in self.domain_list:
                status_code = self.check_domain_status(domain)
                
                if status_code == 200:
                    logging.info(f"域名 {domain} 状态正常 (200)")
                    self.failure_counts[domain] = 0  # 重置失败计数
                else:
                    logging.warning(f"域名 {domain} 状态异常: {status_code}")
                    self.failure_counts[domain] += 1
                    
                    # 检查是否达到最大失败次数
                    if self.failure_counts[domain] >= self.max_failures:
                        logging.error(f"域名 {domain} 连续失败 {self.max_failures} 次，准备修改CNAME记录")
                        if domain not in self.modified_domains:
                            self.modify_cname_record(domain)
                
                # 间隔指定时间再检查下一个域名
                time.sleep(self.check_interval)


if __name__ == "__main__":
    # 配置需要监控的域名列表
    domains_to_monitor = [
        "zhandouji.buyusdt.me",
        "longxia.buyusdt.me",
        # "toy.buyusdt.me",
    ]
    
    # 创建监控实例并运行
    monitor = DomainMonitor(
        domain_list=domains_to_monitor,
        check_interval=60,      # 每5秒检查一次
        max_failures=4         # 连续3次失败后修改CNAME
    )
    
    try:
        monitor.run()
    except KeyboardInterrupt:
        logging.info("监控程序已停止")
