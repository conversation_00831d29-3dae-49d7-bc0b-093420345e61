#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能需求说明:
-------------
本脚本用于自动监控指定IP列表的HTTP可访问状态，基于itdog.cn的HTTP检测服务。
当检测到超过设定阈值的节点访问失败时，将自动触发告警。

主要功能:
1. 自动检测多个IP的HTTP访问状态
2. 使用Selenium模拟浏览器操作，绕过网站防爬机制
3. 配置灵活的检测间隔（默认每60分钟一次）
4. 支持自定义告警阈值（默认超过10节点失败触发告警）
5. 详细的日志记录和异常处理
6. 调试模式下生成截图以辅助问题排查

使用方法:
1. 修改用户配置区域中的IP列表和其他参数
2. 安装依赖: pip install selenium webdriver-manager
3. 运行脚本: python itdog_check.py

依赖项:
- Python 3.8+
- Selenium
- webdriver-manager
- Chrome浏览器

开发背景:
本脚本为解决需要定期监控多个IP的HTTP状态问题而开发，
当服务器、CDN节点或其他网络服务出现异常时，能够及时发现并告警。

"""

import time
import json
import logging
from datetime import datetime
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("itdog_check.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ITDog HTTP检测服务的URL
BASE_URL = "https://www.itdog.cn/http/"

# ====================== 用户配置区域 ======================
# 定义 IP 列表（需替换为实际 IP）
ips = [
    "***************",  # 示例IP，请替换为实际IP
    "*******",          # Google DNS
    "*******"           # Cloudflare DNS
]

# 检测点，可根据需要调整，默认使用线路1,2,3
lines = "1,2,3"

# 告警阈值：当失败节点总数超过此值时触发告警
alert_threshold = 10

# 检测间隔，单位：秒（默认60分钟）
check_interval = 60 * 60

# 设置为True可以在测试模式下缩短检测间隔为1分钟，便于调试
DEBUG_MODE = True
# ====================== 配置区域结束 ======================


def setup_webdriver():
    """设置WebDriver，创建一个无头Chrome浏览器实例"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式，不显示浏览器窗口
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # 添加用户代理
    chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
    
    # 使用webdriver_manager自动安装和管理Chrome驱动
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    return driver


def check_ip_with_selenium(driver, ip):
    """使用Selenium检测单个IP的HTTP状态"""
    logger.info(f"开始检测IP: {ip}")
    
    try:
        # 访问ITDog检测页面
        driver.get(BASE_URL)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 20)
        
        try:
            # 检查是否需要接受Cookie或处理其他弹窗
            cookie_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '接受') or contains(text(), 'Accept')]")
            if cookie_buttons:
                cookie_buttons[0].click()
                logger.info("已接受Cookie提示")
                time.sleep(1)
        except Exception as e:
            logger.warning(f"处理Cookie提示时发生异常: {str(e)}")
        
        # 获取页面源码以便调试
        if DEBUG_MODE:
            logger.debug(f"页面标题: {driver.title}")
            logger.debug(f"页面URL: {driver.current_url}")
        
        # 填写IP地址
        try:
            # 尝试通过ID查找输入框
            host_input = wait.until(EC.presence_of_element_located((By.ID, "host")))
        except:
            try:
                # 如果ID不存在，尝试其他常见选择器
                host_input = wait.until(EC.presence_of_element_located((By.NAME, "host")))
            except:
                # 尝试更一般的CSS选择器
                host_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']")))
        
        # 清除并填入IP
        host_input.clear()
        host_input.send_keys(ip)
        
        # 选择检测点（如果有相关UI元素）
        # 因为我们不知道确切的UI元素，暂时注释这部分代码
        
        # 查找提交按钮
        try:
            # 尝试多种可能的按钮选择器
            submit_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']")))
        except:
            try:
                submit_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '检测') or contains(text(), '测试') or contains(text(), 'Test')]")))
            except:
                # 尝试更一般的按钮选择器
                all_buttons = driver.find_elements(By.TAG_NAME, "button")
                if all_buttons:
                    submit_button = all_buttons[0]  # 假设第一个按钮是提交按钮
                else:
                    logger.error("无法找到提交按钮")
                    return 0
        
        # 点击提交按钮
        submit_button.click()
        logger.info("已提交检测请求")
        
        # 等待结果加载（等待较长时间，因为HTTP检测可能需要一段时间）
        time.sleep(10)  # 等待10秒钟让测试完成
        
        # 尝试查找结果元素
        # 由于我们不知道确切的结果元素，使用一些常见的模式
        failed_count = 0
        
        # 截图以便调试
        if DEBUG_MODE:
            screenshot_path = f"itdog_debug_{ip.replace('.', '_')}.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"已保存调试截图到 {screenshot_path}")
        
        # 尝试查找包含"失败"、"超时"等状态的元素
        status_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '失败') or contains(text(), '超时') or contains(text(), 'Failed') or contains(text(), 'Timeout')]")
        failed_count = len(status_elements)
        
        # 如果上面的方法找不到，尝试更一般的方法
        if failed_count == 0:
            # 记录页面源码以便分析
            if DEBUG_MODE:
                with open(f"itdog_page_source_{ip.replace('.', '_')}.html", "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
                logger.info(f"已保存页面源码以便分析")
            
            # 假设结果以表格形式展示，查找表格行
            rows = driver.find_elements(By.CSS_SELECTOR, "table tr")
            for row in rows:
                if "失败" in row.text.lower() or "超时" in row.text.lower() or "failed" in row.text.lower() or "timeout" in row.text.lower():
                    failed_count += 1
        
        logger.info(f"IP: {ip}, 检测点失败数: {failed_count}")
        return failed_count
        
    except Exception as e:
        logger.error(f"检测IP: {ip}时发生异常: {str(e)}")
        logger.debug(traceback.format_exc())
        
        # 出错时保存截图和页面源码以便调试
        try:
            screenshot_path = f"itdog_error_{ip.replace('.', '_')}.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"已保存错误截图到 {screenshot_path}")
            
            with open(f"itdog_error_page_{ip.replace('.', '_')}.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
        except Exception as screenshot_error:
            logger.error(f"保存错误截图和页面源码时发生异常: {str(screenshot_error)}")
        
        return 0  # 发生异常不计入失败数


def check_ips():
    """检测所有IP并统计失败数量"""
    logger.info(f"开始检测 {len(ips)} 个IP...")
    
    total_failed = 0
    results = []
    
    # 初始化WebDriver
    try:
        driver = setup_webdriver()
        
        # 检测每个IP
        for ip in ips:
            failed_count = check_ip_with_selenium(driver, ip)
            total_failed += failed_count
            results.append({"ip": ip, "failed": failed_count})
        
        # 关闭WebDriver
        driver.quit()
        
    except Exception as e:
        logger.error(f"WebDriver初始化或执行过程中发生异常: {str(e)}")
        logger.debug(traceback.format_exc())
    
    # 记录检测结果
    logger.info(f"检测完成，总失败节点数: {total_failed}")
    
    # 检查是否需要告警
    if total_failed > alert_threshold:
        trigger_alert(total_failed, results)
    
    return total_failed


def trigger_alert(failed_count, results):
    """触发告警"""
    alert_message = f"告警: 检测到 {failed_count} 个节点失败，超过阈值 {alert_threshold}"
    logger.warning(alert_message)
    
    # 这里只是简单输出告警信息，实际使用时可以替换为实际的告警方法
    print(f"\033[31m{alert_message}\033[0m")
    
    # 输出失败节点详情
    print("\033[33m失败节点详情:\033[0m")
    for result in results:
        if result["failed"] > 0:
            print(f"IP: {result['ip']}, 失败点数: {result['failed']}")


def main():
    """主函数，循环检测所有IP"""
    logger.info("ITDog IP检测服务启动...")
    
    while True:
        try:
            start_time = time.time()
            check_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logger.info(f"开始第 {check_time} 轮检测")
            
            # 执行检测
            check_ips()
            
            # 计算下次检测时间
            elapsed = time.time() - start_time
            sleep_time = max(1, check_interval - elapsed)  # 确保至少休眠1秒
            
            logger.info(f"本轮检测耗时 {elapsed:.2f} 秒，将在 {sleep_time:.2f} 秒后进行下一轮检测")
            
            # 调试模式下缩短睡眠时间，方便测试
            if DEBUG_MODE:
                logger.info("调试模式：睡眠时间缩短为60秒")
                sleep_time = min(sleep_time, 60)
                
            time.sleep(sleep_time)
            
        except KeyboardInterrupt:
            logger.info("检测服务被手动终止")
            break
        except Exception as e:
            logger.error(f"运行时发生异常: {str(e)}")
            logger.debug(traceback.format_exc())
            logger.info("将在60秒后重试...")
            time.sleep(60)  # 出错后等待60秒再重试


if __name__ == "__main__":
    main()
