#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
import argparse
import time
from domain_processor import DomainProcessor
from cert_generator import CertGenerator
import multiprocessing

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("auto_cert_system.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AutoCertSystem")

def run_domain_processor():
    """运行域名处理模块"""
    processor = DomainProcessor()
    while True:
        try:
            processor.process()
            # 每隔一段时间检查一次新域名
            time.sleep(3600)  # 每小时检查一次
        except Exception as e:
            logger.error(f"域名处理模块运行异常: {str(e)}")
            time.sleep(300)  # 发生错误时等待5分钟再重试

def run_cert_generator():
    """运行证书生成模块"""
    generator = CertGenerator()
    generator.monitor_queue()

def main():
    parser = argparse.ArgumentParser(description='证书自动化申请系统')
    parser.add_argument('--mode', choices=['all', 'domain', 'cert'], 
                      default='all', help='运行模式: all=全部, domain=仅域名处理, cert=仅证书生成')
    
    args = parser.parse_args()
    
    if args.mode == 'all':
        logger.info("系统启动，运行所有模块")
        # 使用多进程运行两个模块
        domain_proc = multiprocessing.Process(target=run_domain_processor)
        cert_proc = multiprocessing.Process(target=run_cert_generator)
        
        domain_proc.start()
        cert_proc.start()
        
        domain_proc.join()
        cert_proc.join()
        
    elif args.mode == 'domain':
        logger.info("系统启动，仅运行域名处理模块")
        run_domain_processor()
        
    elif args.mode == 'cert':
        logger.info("系统启动，仅运行证书生成模块")
        run_cert_generator()

if __name__ == "__main__":
    main() 