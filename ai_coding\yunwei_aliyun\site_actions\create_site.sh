#!/bin/bash
SITE=$1
CONFD="/www/cloud_waf/nginx/conf.d"
VHOST="/www/cloud_waf/vhost"

# 构建文件路径
file_path="${CONFD}/vhost/${SITE}_com.conf"
# 检查执行携带参数是否正常
if [ $# -lt 1 ]; then
    echo "Usage: $0 <arg1>"
    exit 1
fi
# 检查文件是否存在
if [ -f "$file_path" ]; then
    echo "站点已存在!! 不再进行创建。"
    exit 1
fi

# 处理conf.d目录下相关的配置文件 #/www/cloud_waf/nginx/conf.d/
# /www/cloud_waf/nginx/conf.d/other/siteid.json
sed -i "s;}$;,\"${SITE}_com\":\"${SITE}_com\"};" ${CONFD}/other/siteid.json
# /www/cloud_waf/nginx/conf.d/vhost/muban666_com.conf
\cp ${CONFD}/vhost/muban666_com.conf ${CONFD}/vhost/${SITE}_com.conf
sed -i "s/muban666/${SITE}/g" ${CONFD}/vhost/${SITE}_com.conf
# /www/cloud_waf/nginx/conf.d/waf/config/domains.json
sed -i "s/]$/,{\"domains\":[\"${SITE}.com\"],\"name\":\"${SITE}_com\"}]/" ${CONFD}/waf/config/domains.json
# /www/cloud_waf/nginx/conf.d/waf/config/site.json
sed -i "s;}$;,\"${SITE}_com\":{\"admin_protect\":[],\"cc\":{\"cc_type_status\":2,\"cycle\":120,\"endtime\":86400,\"is_cc_url\":true,\"limit\":100,\"open\":true,\"ps\":\"基础CC防护\",\"status\":200},\"cdn\":false,\"cdn_baidu\":false,\"cdn_header\":[\"x-forwarded-for\",\"x-real-ip\",\"x-forwarded\",\"forwarded-for\",\"forwarded\",\"true-client-ip\",\"client-ip\",\"ali-cdn-real-ip\",\"cdn-src-ip\",\"cdn-real-ip\",\"cf-connecting-ip\",\"x-cluster-client-ip\",\"wl-proxy-client-ip\",\"proxy-client-ip\",\"true-client-ip\"],\"cookie\":{\"mode\":3,\"ps\":\"cookie头攻击防御\"},\"disable_ext\":[\"sql\",\"bak\",\"swp\"],\"disable_php_path\":[\"^\/cache\/\",\"^\/config\/\",\"^\/runtime\/\",\"^\/application\/\",\"^\/temp\/\",\"^\/logs\/\",\"^\/log\/\"],\"disable_upload_ext\":[\"php\",\"jsp\"],\"download\":{\"mode\":3,\"ps\":\"恶意下载防御\"},\"file_import\":{\"mode\":3,\"ps\":\"文件包含防御\"},\"file_upload\":{\"mode\":3,\"ps\":\"文件上传木马拦截\",\"status\":444},\"from_data\":{\"mode\":3,\"ps\":\"畸形文件上传协议检测\"},\"idc\":{\"mode\":0,\"ps\":\"禁止IDC访问\"},\"mode\":2,\"number_attacks\":{\"ps\":\"攻击次数拦截\",\"retry\":150,\"retry_cycle\":180,\"retry_time\":86400},\"php_eval\":{\"mode\":3,\"ps\":\"PHP代码执行检测\"},\"rce\":{\"mode\":3,\"ps\":\"命令执行拦截\"},\"readonly\":{\"open\":false,\"ps\":\"只读模式,请勿在非攻防演练时开启,开启后将会影响用户登录、支付、搜索、注册、评论等功能\"},\"scan\":{\"mode\":3,\"ps\":\"扫描器防御\"},\"smart_cc\":{\"expire\":120,\"ip_drop_time\":3600,\"max_avg_proxy_time\":200,\"max_err_count\":10,\"max_qps\":10,\"open\":false,\"ps\":\"智能CC防护\",\"status\":444},\"sql\":{\"mode\":3,\"ps\":\"SQL注入拦截\"},\"ssrf\":{\"mode\":3,\"ps\":\"ssrf代码执行检测\"},\"user_agent\":{\"mode\":3,\"ps\":\"恶意爬虫防御\"},\"xss\":{\"mode\":3,\"ps\":\"XSS注入拦截\"}}};" ${CONFD}/waf/config/site.json
# /www/cloud_waf/nginx/conf.d/user/muban666_com.conf
\cp ${CONFD}/user/muban666_com.conf ${CONFD}/user/${SITE}_com.conf
# /www/cloud_waf/nginx/conf.d/cert/muban666_com/
\cp -aRp ${CONFD}/cert/muban666_com ${CONFD}/cert/${SITE}_com

#### 处理vhost目录下相关的配置文件
# /www/cloud_waf/vhost/site_json/muban666_com.json
\cp ${VHOST}/site_json/muban666_com.json ${VHOST}/site_json/${SITE}_com.json
sed -i "s/muban666/${SITE}/g" ${VHOST}/site_json/${SITE}_com.json
# /www/cloud_waf/vhost/site_json/domains.json
sed -i "s/]$/,{\"domains\":[\"${SITE}.com\"],\"name\":\"${SITE}_com\"}]/" ${VHOST}/site_json/domains.json
# /www/cloud_waf/vhost/site_json/site.json
sed -i "s;}$;,\"${SITE}_com\":{\"admin_protect\":[],\"cc\":{\"cc_type_status\":2,\"cycle\":120,\"endtime\":86400,\"is_cc_url\":true,\"limit\":100,\"open\":true,\"ps\":\"基础CC防护\",\"status\":200},\"cdn\":false,\"cdn_baidu\":false,\"cdn_header\":[\"x-forwarded-for\",\"x-real-ip\",\"x-forwarded\",\"forwarded-for\",\"forwarded\",\"true-client-ip\",\"client-ip\",\"ali-cdn-real-ip\",\"cdn-src-ip\",\"cdn-real-ip\",\"cf-connecting-ip\",\"x-cluster-client-ip\",\"wl-proxy-client-ip\",\"proxy-client-ip\",\"true-client-ip\"],\"cookie\":{\"mode\":3,\"ps\":\"cookie头攻击防御\"},\"disable_ext\":[\"sql\",\"bak\",\"swp\"],\"disable_php_path\":[\"^\/cache\/\",\"^\/config\/\",\"^\/runtime\/\",\"^\/application\/\",\"^\/temp\/\",\"^\/logs\/\",\"^\/log\/\"],\"disable_upload_ext\":[\"php\",\"jsp\"],\"download\":{\"mode\":3,\"ps\":\"恶意下载防御\"},\"file_import\":{\"mode\":3,\"ps\":\"文件包含防御\"},\"file_upload\":{\"mode\":3,\"ps\":\"文件上传木马拦截\",\"status\":444},\"from_data\":{\"mode\":3,\"ps\":\"畸形文件上传协议检测\"},\"idc\":{\"mode\":0,\"ps\":\"禁止IDC访问\"},\"mode\":2,\"number_attacks\":{\"ps\":\"攻击次数拦截\",\"retry\":150,\"retry_cycle\":180,\"retry_time\":86400},\"php_eval\":{\"mode\":3,\"ps\":\"PHP代码执行检测\"},\"rce\":{\"mode\":3,\"ps\":\"命令执行拦截\"},\"readonly\":{\"open\":false,\"ps\":\"只读模式,请勿在非攻防演练时开启,开启后将会影响用户登录、支付、搜索、注册、评论等功能\"},\"scan\":{\"mode\":3,\"ps\":\"扫描器防御\"},\"smart_cc\":{\"expire\":120,\"ip_drop_time\":3600,\"max_avg_proxy_time\":200,\"max_err_count\":10,\"max_qps\":10,\"open\":false,\"ps\":\"智能CC防护\",\"status\":444},\"sql\":{\"mode\":3,\"ps\":\"SQL注入拦截\"},\"ssrf\":{\"mode\":3,\"ps\":\"ssrf代码执行检测\"},\"user_agent\":{\"mode\":3,\"ps\":\"恶意爬虫防御\"},\"xss\":{\"mode\":3,\"ps\":\"XSS注入拦截\"}}};" ${VHOST}/site_json/site.json
# /www/cloud_waf/vhost/slice_log_json/muban666_com.json
\cp ${VHOST}/slice_log_json/muban666_com.json ${VHOST}/slice_log_json/${SITE}_com.json
sed -i "s/muban666/${SITE}/g" ${VHOST}/slice_log_json/${SITE}_com.json
#### 执行重载btw 7
#btw 7
