#!/bin/bash
#
# ZeroSSL Certificate Generator for CentOS 7
# 用于通过ZeroSSL REST API生成多IP证书的脚本
# 使用HTTP验证方式
#

# 定义ZeroSSL API密钥
API_KEY="b8ae6c8c36b067ac52f9a388ff648ff0"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -f, --ip-file <file>     包含IP列表的文件路径(默认为./ip_list.txt)"
    echo "  -c, --common-name <CN>   证书公用名(默认为第一个IP)"
    echo "  -o, --output-dir <dir>   证书输出目录(默认为./certs)"
    echo "  -w, --webroot <dir>      Web服务器根目录，用于验证(默认为/usr/share/nginx/html)"
    echo "  -d, --days <days>        证书有效期(默认为90天)"
    echo "  -v, --verbose            显示详细输出"
    echo "  -h, --help               显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 -f ip_list.txt -o /etc/ssl/certs"
    echo
}

# 检查命令是否存在
check_command() {
    command -v "$1" >/dev/null 2>&1 || { echo >&2 "需要 $1 但未找到。请安装后再试。"; exit 1; }
}

# 解析参数
parse_args() {
    # 默认值
    IP_FILE="./ip_list.txt"
    OUTPUT_DIR="./certs"
    WEB_ROOT="/usr/share/nginx/html"
    DAYS=90
    VERBOSE=0

    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--ip-file)
                IP_FILE="$2"
                shift 2
                ;;
            -c|--common-name)
                COMMON_NAME="$2"
                shift 2
                ;;
            -o|--output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -w|--webroot)
                WEB_ROOT="$2"
                shift 2
                ;;
            -d|--days)
                DAYS="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=1
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 检查API密钥是否已设置
    if [ "$API_KEY" = "your-zerossl-api-key-here" ]; then
        echo "错误: 请在脚本中设置有效的ZeroSSL API密钥"
        exit 1
    fi

    # 检查IP文件是否存在
    if [ ! -f "$IP_FILE" ]; then
        echo "错误: IP列表文件 '$IP_FILE' 不存在"
        exit 1
    fi

    # 读取IP列表
    IPS=$(cat "$IP_FILE")
    # 转换IP列表为数组
    IFS=',' read -r -a IP_ARRAY <<< "$IPS"
    
    # 输出IP数量
    echo "读取到 ${#IP_ARRAY[@]} 个IP地址"

    # 如果未指定COMMON_NAME，使用第一个IP
    if [ -z "$COMMON_NAME" ]; then
        COMMON_NAME="${IP_ARRAY[0]}"
    fi

    # 确保输出目录存在
    mkdir -p "$OUTPUT_DIR"
}

# 生成CSR和私钥
generate_csr() {
    echo "正在生成CSR和私钥..."
    
    # 创建配置文件
    CONFIG_FILE="$OUTPUT_DIR/openssl.cnf"
    cat > "$CONFIG_FILE" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = req_ext

[dn]
C = CN
ST = State
L = City
O = Organization
OU = Organizational Unit
CN = $COMMON_NAME

[req_ext]
subjectAltName = @alt_names

[alt_names]
EOF

    # 添加IP地址到配置文件
    for i in "${!IP_ARRAY[@]}"; do
        echo "IP.$((i+1)) = ${IP_ARRAY[$i]}" >> "$CONFIG_FILE"
    done

    # 生成CSR和私钥
    openssl req -new -newkey rsa:2048 -nodes \
        -out "$OUTPUT_DIR/certificate.csr" \
        -keyout "$OUTPUT_DIR/private.key" \
        -config "$CONFIG_FILE" >/dev/null 2>&1

    if [ $? -ne 0 ]; then
        echo "生成CSR和私钥失败"
        exit 1
    fi

    echo "CSR和私钥已生成"
}

# 创建证书请求
create_certificate() {
    echo "正在请求创建证书..."
    
    # 读取CSR内容
    CSR=$(cat "$OUTPUT_DIR/certificate.csr")
    
    # 组合所有IP为逗号分隔的列表
    DOMAINS="$IPS"
    
    # 创建请求
    RESPONSE=$(curl -s -X POST "https://api.zerossl.com/certificates?access_key=$API_KEY" \
        --data-urlencode "certificate_csr=$CSR" \
        -d "certificate_domains=$DOMAINS" \
        -d "certificate_validity_days=$DAYS")
    
    # 保存完整响应
    echo "$RESPONSE" > "$OUTPUT_DIR/certificate_response.json"
    
    # 如果启用详细输出，显示API响应内容
    if [ $VERBOSE -eq 1 ]; then
        echo "API响应内容："
        cat "$OUTPUT_DIR/certificate_response.json"
        echo
    fi
    
    # 检查响应并提取ID
    ID=$(echo "$RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    
    if [ -z "$ID" ]; then
        echo "创建证书请求失败: $RESPONSE"
        exit 1
    fi
    
    echo "证书ID: $ID"
    
    # 保存ID以便后续使用
    echo "$ID" > "$OUTPUT_DIR/certificate_id.txt"
}

# 处理HTTP验证
setup_http_validation() {
    echo "正在设置HTTP验证..."
    RESPONSE=$(cat "$OUTPUT_DIR/certificate_response.json")

    # 检查响应中是否包含 "other_methods"
    if ! echo "$RESPONSE" | grep -q '"other_methods"'; then
        echo "错误: API响应中没有找到 'other_methods' 部分，无法提取验证信息。"
        echo "完整响应保存在: $OUTPUT_DIR/certificate_response.json"
        exit 1
    fi

    # 提取通用的验证文件名
    # 我们从第一个IP的条目中获取文件名，因为它是共享的
    FIRST_IP_FOR_EXTRACTION="${IP_ARRAY[0]}"
    COMMON_FILENAME=$(echo "$RESPONSE" | grep -o "\"file_validation_url_http\":\"http:\/\/$FIRST_IP_FOR_EXTRACTION\/\.well-known\/pki-validation\/[A-Z0-9]\{32\}\.txt\"" | head -n 1 | sed -n 's|.*/\([A-Z0-9]\{32\}\.txt\)\"$|\1|p')

    if [ -z "$COMMON_FILENAME" ]; then
        echo "错误：无法从API响应中提取通用验证文件名。"
        echo "请检查 $OUTPUT_DIR/certificate_response.json 中第一个IP ('$FIRST_IP_FOR_EXTRACTION') 的 'file_validation_url_http' 条目。"
        exit 1
    fi
    echo "提取到的通用验证文件名: $COMMON_FILENAME"

    # 提取通用的验证内容 (对于所有IP应该是一样的)
    # grep -o 会找到所有匹配项，我们取第一个，因为它们是相同的
    COMMON_CONTENT_LINES_JSON=$(echo "$RESPONSE" | grep -o '\"file_validation_content\":\[\"[^\"]*\",\"[^\"]*\",\"[^\"]*\"\]' | head -n 1)
    if [ -z "$COMMON_CONTENT_LINES_JSON" ]; then
        echo "错误：无法从API响应中提取通用验证内容。"
        echo "请检查 $OUTPUT_DIR/certificate_response.json 中的 'file_validation_content' 数组。"
        exit 1
    fi

    COMMON_CONTENT_LINE1=$(echo "$COMMON_CONTENT_LINES_JSON" | cut -d'"' -f4)
    COMMON_CONTENT_LINE2=$(echo "$COMMON_CONTENT_LINES_JSON" | cut -d'"' -f6)
    COMMON_CONTENT_LINE3=$(echo "$COMMON_CONTENT_LINES_JSON" | cut -d'"' -f8)

    if [ -z "$COMMON_CONTENT_LINE1" ] || [ -z "$COMMON_CONTENT_LINE2" ] || [ -z "$COMMON_CONTENT_LINE3" ]; then
        echo "错误：未能完整提取通用验证内容的三行。"
        exit 1
    fi
    echo "提取到的通用验证内容:"
    echo "  L1: $COMMON_CONTENT_LINE1"
    echo "  L2: $COMMON_CONTENT_LINE2"
    echo "  L3: $COMMON_CONTENT_LINE3"

    VALIDATION_DIR="$WEB_ROOT/.well-known/pki-validation"
    mkdir -p "$VALIDATION_DIR"
    VALIDATION_FILE_FULL_PATH="$VALIDATION_DIR/$COMMON_FILENAME"

    echo -e "$COMMON_CONTENT_LINE1\n$COMMON_CONTENT_LINE2\n$COMMON_CONTENT_LINE3" > "$VALIDATION_FILE_FULL_PATH"

    if [ $? -ne 0 ]; then
        echo "错误: 创建验证文件 '$VALIDATION_FILE_FULL_PATH' 失败。"
        exit 1
    fi
    
    echo "已创建通用验证文件: $VALIDATION_FILE_FULL_PATH"
    echo "此文件将用于验证列表中的所有IP地址。"

    # 清理并记录验证文件信息
    rm -f "$OUTPUT_DIR/validation_files.log" 
    echo "通用验证文件路径: $VALIDATION_FILE_FULL_PATH" > "$OUTPUT_DIR/validation_files.log"
    echo "通用验证文件内容:" >> "$OUTPUT_DIR/validation_files.log"
    echo "$COMMON_CONTENT_LINE1" >> "$OUTPUT_DIR/validation_files.log"
    echo "$COMMON_CONTENT_LINE2" >> "$OUTPUT_DIR/validation_files.log"
    echo "$COMMON_CONTENT_LINE3" >> "$OUTPUT_DIR/validation_files.log"
    echo "---" >> "$OUTPUT_DIR/validation_files.log"
    echo "此文件用于验证以下所有IP:" >> "$OUTPUT_DIR/validation_files.log"
    for IP_ADDR in "${IP_ARRAY[@]}"; do
        echo "$IP_ADDR" >> "$OUTPUT_DIR/validation_files.log"
    done

    echo "验证文件设置完成。详情请查看: $OUTPUT_DIR/validation_files.log"
}

# 验证域名
verify_domains() {
    echo "正在发起域名验证..."
    
    # 读取证书ID
    ID=$(cat "$OUTPUT_DIR/certificate_id.txt")
    
    # 发送验证请求
    echo "发送验证请求: https://api.zerossl.com/certificates/$ID/challenges?access_key=$API_KEY"
    
    VERIFICATION_RESPONSE=$(curl -s -X POST "https://api.zerossl.com/certificates/$ID/challenges?access_key=$API_KEY" \
        -d "validation_method=HTTP_CSR_HASH")
    
    echo "$VERIFICATION_RESPONSE" > "$OUTPUT_DIR/verification_response.json"
    
    # 如果启用详细输出，显示API响应内容
    if [ $VERBOSE -eq 1 ]; then
        echo "验证API响应内容："
        cat "$OUTPUT_DIR/verification_response.json"
        echo
    fi
    
    # 检查是否成功
    SUCCESS=$(echo "$VERIFICATION_RESPONSE" | grep -o '"success":true')
    
    if [ -z "$SUCCESS" ]; then
        echo "发起验证失败: "
        cat "$OUTPUT_DIR/verification_response.json"
        exit 1
    fi
    
    echo "验证请求已发送，等待验证完成..."
}

# 等待证书签发
wait_for_issuance() {
    echo "等待证书签发..."
    
    # 读取证书ID
    ID=$(cat "$OUTPUT_DIR/certificate_id.txt")
    
    MAX_ATTEMPTS=30
    ATTEMPT=0
    
    while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
        ATTEMPT=$((ATTEMPT+1))
        
        # 获取证书状态
        STATUS_RESPONSE=$(curl -s "https://api.zerossl.com/certificates/$ID?access_key=$API_KEY")
        
        echo "$STATUS_RESPONSE" > "$OUTPUT_DIR/status_response_$ATTEMPT.json"
        
        # 提取状态
        STATUS=$(echo "$STATUS_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        
        if [ "$STATUS" = "issued" ]; then
            echo "证书已签发！"
            return 0
        elif [ "$STATUS" = "cancelled" ] || [ "$STATUS" = "expired" ]; then
            echo "证书状态为 $STATUS，签发失败"
            exit 1
        fi
        
        echo "证书状态: $STATUS，继续等待... (尝试 $ATTEMPT/$MAX_ATTEMPTS)"
        sleep 10
    done
    
    echo "等待超时，请手动检查证书状态"
    exit 1
}

# 下载证书
download_certificate() {
    echo "下载证书..."
    
    # 读取证书ID
    ID=$(cat "$OUTPUT_DIR/certificate_id.txt")
    
    # 下载证书
    CERTIFICATE_RESPONSE=$(curl -s "https://api.zerossl.com/certificates/$ID/download/return?access_key=$API_KEY")
    
    echo "$CERTIFICATE_RESPONSE" > "$OUTPUT_DIR/download_response.json"
    
    # 提取证书内容
    CERTIFICATE=$(echo "$CERTIFICATE_RESPONSE" | grep -o '"certificate\.crt":"[^"]*"' | cut -d'"' -f4)
    CA_BUNDLE=$(echo "$CERTIFICATE_RESPONSE" | grep -o '"ca_bundle\.crt":"[^"]*"' | cut -d'"' -f4)
    
    if [ -z "$CERTIFICATE" ]; then
        echo "下载证书失败: "
        cat "$OUTPUT_DIR/download_response.json"
        exit 1
    fi
    
    # 保存证书文件
    echo "$CERTIFICATE" > "$OUTPUT_DIR/certificate.crt"
    echo "$CA_BUNDLE" > "$OUTPUT_DIR/ca_bundle.crt"
    
    # 创建完整链证书
    cat "$OUTPUT_DIR/certificate.crt" "$OUTPUT_DIR/ca_bundle.crt" > "$OUTPUT_DIR/fullchain.crt"
    
    echo "证书已下载到 $OUTPUT_DIR/"
    echo "  - 证书: $OUTPUT_DIR/certificate.crt"
    echo "  - CA包: $OUTPUT_DIR/ca_bundle.crt"
    echo "  - 完整链: $OUTPUT_DIR/fullchain.crt"
    echo "  - 私钥: $OUTPUT_DIR/private.key"
}

# 清理验证文件
cleanup() {
    echo "清理验证文件..."
    VALIDATION_FILE_TO_DELETE=""

    if [ -f "$OUTPUT_DIR/validation_files.log" ]; then
        VALIDATION_FILE_TO_DELETE=$(grep "通用验证文件路径:" "$OUTPUT_DIR/validation_files.log" | cut -d' ' -f3)
        if [ -z "$VALIDATION_FILE_TO_DELETE" ]; then
             echo "警告: 在日志文件中未找到有效的验证文件路径。"
             VALIDATION_FILE_TO_DELETE="" # 重置以触发后备
        fi
    fi

    # 如果日志中没有或无效，尝试从响应中重新派生
    if [ -z "$VALIDATION_FILE_TO_DELETE" ] && [ -f "$OUTPUT_DIR/certificate_response.json" ]; then
        echo "尝试从API响应中后备提取验证文件名..."
        RESPONSE=$(cat "$OUTPUT_DIR/certificate_response.json")
        FIRST_IP_FOR_EXTRACTION="${IP_ARRAY[0]}"
        # 使用与 setup_http_validation 中相同的提取逻辑
        COMMON_FILENAME=$(echo "$RESPONSE" | grep -o "\"file_validation_url_http\":\"http:\/\/$FIRST_IP_FOR_EXTRACTION\/\.well-known\/pki-validation\/[A-Z0-9]\{32\}\.txt\"" | head -n 1 | sed -n 's|.*/\([A-Z0-9]\{32\}\.txt\)\"$|\1|p')
        
        if [ -n "$COMMON_FILENAME" ]; then
            VALIDATION_FILE_TO_DELETE="$WEB_ROOT/.well-known/pki-validation/$COMMON_FILENAME"
            echo "通过后备方法确定的验证文件: $VALIDATION_FILE_TO_DELETE"
        else
            echo "警告: 后备提取验证文件名也失败。"
        fi
    fi

    if [ -n "$VALIDATION_FILE_TO_DELETE" ]; then
        if [ -f "$VALIDATION_FILE_TO_DELETE" ]; then
            rm "$VALIDATION_FILE_TO_DELETE"
            echo "已删除验证文件: $VALIDATION_FILE_TO_DELETE"
        else
            echo "警告: 目标验证文件 '$VALIDATION_FILE_TO_DELETE' 未找到，可能已被删除或路径不正确。"
        fi
    else
        echo "未找到要清理的验证文件信息。"
    fi
}

main() {
    # 检查必要的命令
    check_command "curl"
    check_command "openssl"
    
    # 解析命令行参数
    parse_args "$@"
    
    # 执行证书生成步骤
    generate_csr
    create_certificate
    setup_http_validation
    verify_domains
    wait_for_issuance
    download_certificate
    cleanup
    
    echo "完成！证书已成功生成并保存到 $OUTPUT_DIR/"
}

main "$@"
