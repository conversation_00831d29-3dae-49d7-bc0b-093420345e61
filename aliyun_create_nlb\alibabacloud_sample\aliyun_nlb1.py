# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import copy
import json
import os
import random
import sys
import time

from typing import List

from alibabacloud_nlb20220430.client import Client as Nlb20220430Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_nlb20220430 import models as nlb_20220430_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient

## 创建阿里nlb实例
## /usr/bin/python /root/ajie/yajian91_create_nlb/alibabacloud_sample/aliyun_nlb1.py create
class AliyunNlb:
    def __init__(self):
        self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t9wShTub7S6kGcHQm3B'  # 账号db-ali-3 aliyun4187412305 dqiowdqw34543
        self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tLDFWsX5j7L4tEJe2VT'  # 账号db-ali-2  dabing00@$
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号db-ali-2
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tDXu23YyMMiuGG4zKQx'  # 账号db-1  dabing00@$
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号db-1
        # 用户登录名称 <EMAIL>
        # 登录密码 dabing00@$
        # AccessKey ID LTAI5tBPxvVMaW8RivBUiMa4
        # AccessKey Secret ******************************
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tKWjtneMPHf1D2pEZ8F'  # 账号26-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号26-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tFbYxPhSXLSL4hACZg8'  # 账号25-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号25-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tCAZ34KFnjVQdXvRhHv'  # 账号24-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号24-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tQp5fyX5mcqDayw7jAU'  # 账号23-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号23-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tFwvs5ADi1KE1CyEg7Y'  # 账号22-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号22-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tSEcCoJb4Lpam3GHKSg'  # 账号21-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号21-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tDLdYKV6kdZuUjFAj5Y'  # 账号19-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号19-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tSUFNZE9A5adAbfww7Q'  # 账号18-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号18-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tDj57KcTHzkvmrN8SYy'  # 账号17-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号17-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t5akidQ16QD1UoLVFaG'  # 账号16-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号16-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tR3UPaSYUVwgN7qe5K5'  # 账号15-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号15-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tE4nwnWeVBZCafqmDik'  # 账号14-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号14-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tAkD3F7zjeJDd3rbLK9'  # 账号13
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号13
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tADEEp4T3mWPGPikqrY'  # 账号12-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号12-子账号
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tPWN75E9bh6viSbSYGi'  # 账号10
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号10
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tGG7kuykNGdBVfKNuGR'  # 账号11
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = 'XO4vXFyPlAst9CDQauqFFv10BRa9aq'  # 账号11
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t6t5M9LSvbhK2TiaWg4'   # 账号9
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号9
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tMzR9BZi9rqfcbvRc2u'   # 账号8
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号8
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t6jtLtDo6bXKGQ8oP1H'   # 账号7
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号7
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tKeDHG4tQuUwpwqYFf6'   # 账号5
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号5
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t8H92ANTArLFsxbJuzk'  # 账号3
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'  # 账号3
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t8oKcEWDdM2G4gf1Tyh'   # 账号4
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号4
        self.DNS = '.cn-hongkong.nlb.aliyuncs.com'

    @staticmethod
    def create_client(
            access_key_id: str,
            access_key_secret: str,
    ) -> Nlb20220430Client:
        config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
        # 访问的域名
        config.endpoint = f'nlb.cn-hongkong.aliyuncs.com'
        return Nlb20220430Client(config)

    def get_nlb_list(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        tag_0 = nlb_20220430_models.ListLoadBalancersRequestTag(
            key='user1',
            value='user1'
        )
        list_load_balancers_request = nlb_20220430_models.ListLoadBalancersRequest(
            tag=[
                tag_0
            ],
            load_balancer_type='network'
        )

        runtime = util_models.RuntimeOptions()
        try:
            resp = client.list_load_balancers_with_options(list_load_balancers_request, runtime).body.load_balancers
            nlb_active = []
            nlb_info = {}
            for li in resp:
                nlb_info['LoadBalancerId'] = li.load_balancer_id
                nlb_info['domain'] = li.dnsname
                nlb_info['nlb_status'] = li.load_balancer_status
                nlb_active.append(copy.deepcopy(nlb_info))
            return nlb_active
        except Exception as error:
            ConsoleClient.log(UtilClient.to_jsonstring(error))

    def get_nlb_domain(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        list_listeners_request = nlb_20220430_models.ListListenersRequest(
            # load_balancer_ids=[
            #     'nlb-x8lbrtcco1zcgb894c'
            # ]
        )
        runtime = util_models.RuntimeOptions()
        try:
            nlb_active = self.get_nlb_list()
            resp = client.list_listeners_with_options(list_listeners_request, runtime).body.listeners
            for li in resp:
                for id in nlb_active:
                    if id.get('LoadBalancerId') == li.load_balancer_id:
                        id['domain'] = "http://" + id.get('domain') + ":" + str(li.listener_port)
            return nlb_active
        except Exception as error:
            UtilClient.assert_as_string(error)

    # 创建nlb实例
    def create_nlb(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        load_balancer_billing_config = nlb_20220430_models.CreateLoadBalancerRequestLoadBalancerBillingConfig(
            # String, 可选, 网络型负载均衡实例的计费类型。  仅取值**PostPay**：表示按量计费。,
            pay_type='PostPay'
        )
        zone_mappings_0 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            # String, 必填, 可用区对应的交换机，每个可用区只能使用一台交换机和一个子网。至少需要添加2个可用区，最多支持添加10个可用区。,
            # need modify
            v_switch_id='vsw-j6ccuos757jx40x4cc2ea',    # 账号26
            # v_switch_id='vsw-j6cqysnr9b7lkpoqq0mbq',    # 账号11-1
            # String, 必填, 网络型负载均衡实例的可用区ID。至少需要添加2个可用区，最多支持添加10个可用区。  您可以通过调用[DescribeZones](~~443890~~)接口获取可用区ID。,
            zone_id='cn-hongkong-c'
        )
        zone_mappings_1 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            v_switch_id='vsw-j6cv7gfbig0hu3rbqemvv',    # 账号26
            # v_switch_id='vsw-j6cd4pp67w1f8i18zg7zc',    # 账号11-1
            zone_id='cn-hongkong-d'
        )
        tag_0 = nlb_20220430_models.CreateLoadBalancerRequestTag(
            key='user1',
            value='user1'
        )
        create_load_balancer_request = nlb_20220430_models.CreateLoadBalancerRequest(
            load_balancer_type='network',
            load_balancer_name='user1',
            address_type='Internet',
            address_ip_version='ipv4',
            vpc_id='vpc-j6cd5rz7r298hf3f6ql9y', # 账号26
            # vpc_id='vpc-j6ckn26l5dfim577n2yg7', # 账号11-1
            # Array, 可选,
            zone_mappings=[
                zone_mappings_0,
                zone_mappings_1
            ],
            tag=[
                tag_0
            ],
            # Object, 可选,
            load_balancer_billing_config=load_balancer_billing_config,
            region_id='cn-hongkong'
        )
        runtime = util_models.RuntimeOptions()
        nlb_id = client.create_load_balancer_with_options(create_load_balancer_request, runtime).body.loadbalancer_id
        # 循环等待nlb, 直到状态为Active
        while True:
            lis = self.get_nlb_list()
            for li in lis:
                if li.get('LoadBalancerId') == nlb_id and li.get('nlb_status') == 'Active':
                    break
            else:
                time.sleep(9)
                continue
            break
        return str(nlb_id)

    def create_listen(self, nlb_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        create_listener_request = nlb_20220430_models.CreateListenerRequest(
            listener_protocol='TCP',
            listener_port=80,
            # listener_port=random.randint(5000, 65500),
            load_balancer_id=str(nlb_id),
            server_group_id='sgp-ikywh04eajw0vebfi8', # http26
            # server_group_id='sgp-gb8w1ndzkwtvrddt85', # http11-1
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.create_listener_with_options(create_listener_request, runtime).body.listener_id
            return str(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def create_listen_https(self, nlb_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        create_listener_request = nlb_20220430_models.CreateListenerRequest(
            region_id='cn-hongkong',
            listener_protocol='TCPSSL',
            listener_port=443,
            # load_balancer_id='nlb-2cf3jud0qxofc9s7zl',
            load_balancer_id=str(nlb_id),
            server_group_id='sgp-131cl8kxboklrjy1xd', # https26
            # server_group_id='sgp-fknwz74oo8oe8352d2', # https11-1
            # server_group_id='sgp-hvg7g38yzdhlqmf7ul', # https9-1
            security_policy_id='tls_cipher_policy_1_0',
            certificate_ids=[
                '12575236-cn-hangzhou'  # 账号26
                # '11139778-cn-hangzhou'  # 账号10-1
                # '11094326-cn-hangzhou'  # 账号11-1
            ]
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.create_listener_with_options(create_listener_request, runtime).body.listener_id
            # print("listener_id: " + resp)
            return str(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def get_https_listen_status(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        ## 查询443端口监听status
        runtime = util_models.RuntimeOptions()
        list_listeners_request = nlb_20220430_models.ListListenersRequest(
            region_id='cn-hongkong',
            listener_ids=[listen_https_id])
        lis = client.list_listeners_with_options(list_listeners_request, runtime).body.listeners
        return lis

    def create_additional_ca(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        runtime = util_models.RuntimeOptions()
        while True:
            https_listen = self.get_https_listen_status(listen_https_id)
            for li in https_listen[:1]:
                if li.listener_status == 'Running':
                    break
            else:
                time.sleep(3)
                continue
            break
        associate_additional_certificates_with_listener_request = nlb_20220430_models.AssociateAdditionalCertificatesWithListenerRequest(
            region_id='cn-hongkong',
            additional_certificate_ids=[
                '12575237-cn-hangzhou',  # 账号26
                '12575238-cn-hangzhou',  # 账号26
                '12575239-cn-hangzhou',  # 账号26
                '12632032-cn-hangzhou',  # 账号26
            ],
            listener_id=listen_https_id,
        )
        try:
            resp = client.associate_additional_certificates_with_listener_with_options(associate_additional_certificates_with_listener_request, runtime).body
            # print(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def create_additional_ca2(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        runtime = util_models.RuntimeOptions()
        while True:
            https_listen = self.get_https_listen_status(listen_https_id)
            for li in https_listen[:1]:
                if li.listener_status == 'Running':
                    break
            else:
                time.sleep(3)
                continue
            break
        associate_additional_certificates_with_listener_request = nlb_20220430_models.AssociateAdditionalCertificatesWithListenerRequest(
            region_id='cn-hongkong',
            additional_certificate_ids=[
                '12640467-cn-hangzhou',  # 账号26
                '12620470-cn-hangzhou',  # 账号26
                '12620471-cn-hangzhou',  # 账号26
                '12620878-cn-hangzhou',  # 账号26
            ],
            listener_id=listen_https_id,
        )
        try:
            resp = client.associate_additional_certificates_with_listener_with_options(associate_additional_certificates_with_listener_request, runtime).body
            # print(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def create_additional_ca3(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        runtime = util_models.RuntimeOptions()
        while True:
            https_listen = self.get_https_listen_status(listen_https_id)
            for li in https_listen[:1]:
                if li.listener_status == 'Running':
                    break
            else:
                time.sleep(3)
                continue
            break
        associate_additional_certificates_with_listener_request = nlb_20220430_models.AssociateAdditionalCertificatesWithListenerRequest(
            region_id='cn-hongkong',
            additional_certificate_ids=[
                '12640468-cn-hangzhou',  # 账号26
                '12640469-cn-hangzhou',  # 账号26
                '12643351-cn-hangzhou',  # 账号26
                '12645888-cn-hangzhou',  # 账号26
            ],
            listener_id=listen_https_id,
        )
        try:
            resp = client.associate_additional_certificates_with_listener_with_options(associate_additional_certificates_with_listener_request, runtime).body
            # print(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)
    def create_additional_ca4(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        runtime = util_models.RuntimeOptions()
        while True:
            https_listen = self.get_https_listen_status(listen_https_id)
            for li in https_listen[:1]:
                if li.listener_status == 'Running':
                    break
            else:
                time.sleep(3)
                continue
            break
        associate_additional_certificates_with_listener_request = nlb_20220430_models.AssociateAdditionalCertificatesWithListenerRequest(
            region_id='cn-hongkong',
            additional_certificate_ids=[
                '12646510-cn-hangzhou',  # 账号26
                '12693060-cn-hangzhou',  # 账号26
                '12694524-cn-hangzhou',  # 账号26

            ],
            listener_id=listen_https_id,
        )
        try:
            resp = client.associate_additional_certificates_with_listener_with_options(associate_additional_certificates_with_listener_request, runtime).body
            # print(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def delete_nlb(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        ## 删除最早一个nlb实例，如果大于等于6个可用域名，拿到最旧域名id并执行删除
        domain = self.get_nlb_domain()
        pre_delete_id = ''
        count = 0
        for i, li in enumerate(domain):
            if li.get('nlb_status') == 'Active':
                count+=1
            if i == len(domain) - 1:    ## 拿到最后一个域名id
                pre_delete_id = li.get('LoadBalancerId')
        if count >= 4:
            print("当前域名数：" + str(count) + " |执行删除：" + pre_delete_id)
            delete_load_balancer_request = nlb_20220430_models.DeleteLoadBalancerRequest(
                load_balancer_id=pre_delete_id
            )
            runtime = util_models.RuntimeOptions()
            try:
                resp = client.delete_load_balancer_with_options(delete_load_balancer_request, runtime).status_code
                print("return code:" + str(resp))
            except Exception as error:
                UtilClient.assert_as_string(error)
        else:
            print("当前nlb实例个数:" + str(count) + ", 不再进行删除！")
            print(domain)

    def main(self, operation):
        if operation == 'create':
            ## 创建nlb实例和监听端口
            nlb_id = self.create_nlb()
            http_port = self.create_listen(nlb_id).split('@')[1] # 创建80端口监听
            listen_https_id = self.create_listen_https(nlb_id)  # 拿到https监听的id
            # nlb_port = listen_https_id.split('@')[1]            # 拿到https监听端口
            self.create_additional_ca(listen_https_id)          # 创建扩展证书，一次最多关联15个
            time.sleep(3)
            self.create_additional_ca2(listen_https_id)          # 创建扩展证书，一次最多关联15个
            time.sleep(3)
            self.create_additional_ca3(listen_https_id)          # 创建扩展证书，一次最多关联15个
            self.create_additional_ca4(listen_https_id)          # 创建扩展证书，一次最多关联15个
            print("http://" + nlb_id + self.DNS)                # 输出创建好的域名
            # print("http://" + nlb_id + self.DNS + ":" + nlb_port)
        elif operation == 'select':
            ## 获取存活的nlb实例信息
            lis = self.get_nlb_domain()
            print(lis)
            with open('available_domain.txt', 'w') as f:
                for i, li in enumerate(lis):
                    domain = li.get('domain')
                    f.write(str(domain))
                    if i != len(lis) - 1:  # 当前不是最后一个元素
                        f.write('\n')
        elif operation == 'delete':
            self.delete_nlb()
        else:
            print("请输入需要操作的动作: create,select, delete")


if __name__ == '__main__':
    if len(sys.argv) > 1:
        arg1 = sys.argv[1]
    else:
        arg1 = 'select'
    AliyunNlb().main(str(arg1))
