<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名提交系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .info {
            margin-top: 20px;
            padding: 10px;
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
        }
        .success {
            margin-top: 20px;
            padding: 10px;
            background-color: #ddffdd;
            border-left: 6px solid #4CAF50;
            display: none;
        }
    </style>
</head>
<body>
    <h1>域名提交系统</h1>
    
    <div class="form-container">
        <form id="domainForm">
            <div class="form-group">
                <label for="domains">请输入域名（每行一个）：</label>
                <textarea id="domains" name="domains" placeholder="例如：
example.com
test.example.org
another-domain.net"></textarea>
            </div>
            <button type="submit">提交域名</button>
        </form>
    </div>
    
    <div class="success" id="successMessage">
        域名提交成功！系统将自动处理您提交的域名。
    </div>
    
    <div class="info">
        <p><strong>说明：</strong></p>
        <p>1. 每行输入一个域名</p>
        <p>2. 不需要包含http://或https://前缀</p>
        <p>3. 提交后，系统将自动处理域名并申请SSL证书</p>
    </div>

    <script>
        document.getElementById('domainForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const domainsText = document.getElementById('domains').value;
            if (!domainsText.trim()) {
                alert('请输入至少一个域名');
                return;
            }
            
            // 发送域名数据到服务器
            fetch('submit_domains', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ domains: domainsText }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('successMessage').style.display = 'block';
                    document.getElementById('domains').value = '';
                    
                    // 5秒后隐藏成功消息
                    setTimeout(() => {
                        document.getElementById('successMessage').style.display = 'none';
                    }, 5000);
                } else {
                    alert('提交失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('提交出错:', error);
                alert('提交时发生错误，请稍后再试');
            });
        });
    </script>
</body>
</html> 