# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import sys
import os
from typing import List
from alibabacloud_vpc20160428.client import Client as Vpc20160428Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_darabonba_env.client import Client as EnvClient
from alibabacloud_vpc20160428 import models as vpc_20160428_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_nlb20220430.client import Client as Nlb20220430Client
from alibabacloud_nlb20220430 import models as nlb_20220430_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_ecs20140526.client import Client as Ecs20140526Client
from alibabacloud_ecs20140526 import models as ecs_20140526_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def initialization(
        region_id: str,
    ) -> Vpc20160428Client:
        config = open_api_models.Config()
        # 您的AccessKey ID
        config.access_key_id = 'LTAI5t63gW8MKMzibjBTBRv7'
        # 您的AccessKey Secret
        config.access_key_secret = '******************************'
        # 您的地域ID
        config.region_id = 'cn-hongkong'
        return Vpc20160428Client(config)

    @staticmethod
    def create_client() -> Nlb20220430Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html。
        config = open_api_models.Config(
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。,
            access_key_id='LTAI5t63gW8MKMzibjBTBRv7',
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。,
            access_key_secret='******************************'
        )
        # Endpoint 请参考 https://api.aliyun.com/product/Nlb
        config.endpoint = f'nlb.cn-hongkong.aliyuncs.com'
        return Nlb20220430Client(config)

    @staticmethod
    def create_security_client() -> Ecs20140526Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        # 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378659.html。
        config = open_api_models.Config(
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。,
            access_key_id='LTAI5t63gW8MKMzibjBTBRv7',
            # 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。,
            access_key_secret='******************************'
        )
        # Endpoint 请参考 https://api.aliyun.com/product/Ecs
        config.endpoint = f'ecs.cn-hongkong.aliyuncs.com'
        return Ecs20140526Client(config)


    @staticmethod
    def create_vpc_sample(
        client: Vpc20160428Client,
        region_id: str,
        cidr_block: str,
    ) -> str:
        request = vpc_20160428_models.CreateVpcRequest()
        request.region_id = region_id
        request.cidr_block = cidr_block
        request.vpc_name = 'create_vpc_sample'
        response = client.create_vpc(request)
        return response.body.vpc_id

    @staticmethod
    def create_vswitch_sample(
        client: Vpc20160428Client,
        region_id: str,
        vpc_id: str,
        cidr_block: str,
    ) -> str:
        # 查询该地域下的可用区列表
        query_zone_request = vpc_20160428_models.DescribeZonesRequest()
        query_zone_request.region_id = region_id
        query_zone_response = client.describe_zones(query_zone_request)
        zone = query_zone_response.body.zones.zone[0]
        zone_id = zone.zone_id
        # 创建虚拟交换机
        request = vpc_20160428_models.CreateVSwitchRequest()
        request.region_id = region_id
        request.zone_id = zone_id
        request.vpc_id = vpc_id
        request.cidr_block = cidr_block
        request.v_switch_name = 'create_vswitch_sample'
        response = client.create_vswitch(request)
        return response.body.v_switch_id, zone_id

    @staticmethod
    def wait_vpc_available(
        client: Vpc20160428Client,
        region_id: str,
        vpc_id: str,
    ) -> None:
        request = vpc_20160428_models.DescribeVpcAttributeRequest()
        request.region_id = region_id
        request.vpc_id = vpc_id
        vpc_status = ''
        while not UtilClient.equal_string('Available', vpc_status):
            # 循环等待
            UtilClient.sleep(1000)
            response = client.describe_vpc_attribute(request)
            vpc_status = response.body.status

    @staticmethod
    def wait_vswitch_available(
        client: Vpc20160428Client,
        region_id: str,
        vswitch_id: str,
    ) -> None:
        request = vpc_20160428_models.DescribeVSwitchAttributesRequest()
        request.region_id = region_id
        request.v_switch_id = vswitch_id
        vswitch_status = ''
        while not UtilClient.equal_string('Available', vswitch_status):
            # 循环等待
            UtilClient.sleep(1000)
            response = client.describe_vswitch_attributes(request)
            vswitch_status = response.body.status

    @staticmethod
    def create_http_group(
        args: List[str],
    ) -> None:
        vpc_id = args[0]
        client = Sample.create_client()
        health_check_config = nlb_20220430_models.CreateServerGroupRequestHealthCheckConfig(
            health_check_enabled=False
        )
        create_server_group_request = nlb_20220430_models.CreateServerGroupRequest(
            region_id='cn-hongkong',
            server_group_name='http',
            vpc_id=vpc_id,
            health_check_config=health_check_config
        )
        runtime = util_models.RuntimeOptions()
        try:
            # 复制代码运行请自行打印 API 的返回值
            res = client.create_server_group_with_options(create_server_group_request, runtime)
            return res.body.server_group_id
        except Exception as error:
            UtilClient.assert_as_string(str(error))

    @staticmethod
    def create_security_group(
            args: List[str],
    ) -> None:
        vpc_id = args[0]
        client = Sample.create_security_client()
        create_security_group_request = ecs_20140526_models.CreateSecurityGroupRequest(
            region_id='cn-hongkong',
            security_group_type='normal',
            vpc_id=vpc_id
        )
        runtime = util_models.RuntimeOptions()
        try:
            res = client.create_security_group_with_options(create_security_group_request, runtime)
            return res.body.security_group_id
        except Exception as error:
            UtilClient.assert_as_string(str(error))

    @staticmethod
    def add_security_rule(
            args: List[str],
    ) -> None:
        security_id = args[0]
        client = Sample.create_security_client()
        permissions_0 = ecs_20140526_models.AuthorizeSecurityGroupRequestPermissions(
            policy='accept',
            priority='100',
            ip_protocol='TCP',
            source_cidr_ip='0.0.0.0/0',
            port_range='22/22',
            nic_type='intranet'
        )
        authorize_security_group_request = ecs_20140526_models.AuthorizeSecurityGroupRequest(
            region_id='cn-hongkong',
            security_group_id=security_id,
            permissions=[
                permissions_0
            ]
        )
        runtime = util_models.RuntimeOptions()
        try:
            client.authorize_security_group_with_options(authorize_security_group_request, runtime)
        except Exception as error:
            UtilClient.assert_as_string(str(error))

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        try:
            region_id = args[0]
            client = Sample.initialization(region_id)
            # 创建VPC
            vpc_id = Sample.create_vpc_sample(client, region_id, '***********/16')
            # 等待VPC创建完成
            Sample.wait_vpc_available(client, region_id, vpc_id)
            # 创建第一个虚拟交换机
            vswitch_id_1, zone_id = Sample.create_vswitch_sample(client, region_id, vpc_id, '192.168.1.0/24')
            # 创建第二个虚拟交换机
            vswitch_id_2, zone_id = Sample.create_vswitch_sample(client, region_id, vpc_id, '192.168.2.0/24')
            # 等待虚拟交换机创建完成
            Sample.wait_vswitch_available(client, region_id, vswitch_id_1)
            Sample.wait_vswitch_available(client, region_id, vswitch_id_2)
            # 创建服务器组http
            http_group = Sample.create_http_group([vpc_id])
            # 创建安全组
            security_id = Sample.create_security_group([vpc_id])
            # 增加允许端口rule
            Sample.add_security_rule([security_id])
            ConsoleClient.log(f'VPC:{vpc_id} vsw:{vswitch_id_1}, {vswitch_id_2} http_group:{http_group}')
            ConsoleClient.log(f'http:{http_group} https:')
        except Exception as error:
            ConsoleClient.log(str(error))


if __name__ == '__main__':
    Sample.main(['cn-hongkong'])
