#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import tempfile
import subprocess
import redis
import logging
import time
from config import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cert_generator.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("CertGenerator")

class CertGenerator:
    def __init__(self):
        """初始化证书生成器"""
        self.redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        logger.info("证书生成器初始化完成")

    def monitor_queue(self):
        """监测Redis队列，处理证书生成任务"""
        logger.info("开始监测证书生成队列...")
        
        while True:
            try:
                # 从队列右侧取出一个站点ID（阻塞操作，超时时间为10秒）
                result = self.redis_client.blpop(REDIS_CERT_QUEUE, timeout=10)
                
                if result is None:
                    logger.debug("队列暂无任务，继续等待...")
                    continue
                
                # 获取站点ID和对应的域名
                _, site_id = result
                domains = self.redis_client.hget(REDIS_DOMAIN_HASH, site_id)
                
                if not domains:
                    logger.warning(f"站点 {site_id} 没有关联的域名，跳过")
                    continue
                
                logger.info(f"从队列获取站点 {site_id} 进行证书生成，域名: {domains}")
                
                # 创建临时文件保存域名
                temp_file = tempfile.mktemp()
                try:
                    # 将域名写入临时文件
                    with open(temp_file, 'w') as f:
                        f.write(domains)
                    
                    # 调用证书生成脚本
                    cmd = f"{ISSUE_CERT_SCRIPT} {site_id}"
                    logger.info(f"执行证书生成命令: {cmd}")
                    
                    result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    if result.returncode == 0:
                        logger.info(f"站点 {site_id} 证书生成成功")
                        # 可以在这里添加成功后的其他操作
                    else:
                        error_msg = result.stderr.decode('utf-8')
                        logger.error(f"站点 {site_id} 证书生成失败: {error_msg}")
                        # 这里可以添加失败后的重试逻辑，暂不实现
                        
                finally:
                    # 清理临时文件
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        
            except redis.RedisError as e:
                logger.error(f"Redis连接错误: {str(e)}")
                time.sleep(5)  # 发生错误时等待5秒再重试
                
            except Exception as e:
                logger.error(f"证书生成过程中出错: {str(e)}")
                time.sleep(5)  # 发生错误时等待5秒再重试

def main():
    generator = CertGenerator()
    generator.monitor_queue()

if __name__ == "__main__":
    main() 