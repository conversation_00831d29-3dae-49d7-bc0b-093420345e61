#!/bin/bash
#### 统计网卡中每个ip流量情况情况，按流量从大到小排序，取前20条。

# 检查是否安装了tcpdump和tshark
if ! command -v tcpdump &> /dev/null || ! command -v tshark &> /dev/null; then
    echo "正在安装必要的网络工具(tcpdump和wireshark)..."
    yum install -y tcpdump wireshark
    if [ $? -ne 0 ]; then
        echo "安装失败，请检查yum配置或网络连接"
        exit 1
    fi
fi

# 执行网络流量捕获和分析
timeout 60 tcpdump -i "$(ip route get ******* |awk '{print $5;exit}')" -w traffic.pcap
tshark -r traffic.pcap -Y 'ip' -T fields -e ip.src -e ip.dst -e frame.len | \
awk '{
    # 统计源 IP 发送的流量
    send[$1] += $3;
    # 统计目的 IP 接收的流量
    recv[$2] += $3;
  } END {
    # 合并每个 IP 的总流量（发送+接收）
    for (ip in send) total[ip] = send[ip];
    for (ip in recv) total[ip] += recv[ip];
    # 输出结果并转换为 MB
    for (ip in total) printf "%s\t%.2f MB\n", ip, total[ip]/1024/1024;
  }' | \
sort -k2 -nr | \
head -n 20
