from DrissionPage import Chromium, ChromiumOptions
import time


class TempMailClient:
    def __init__(self):
        self.options = ChromiumOptions()
        self.options.incognito()
        # self.options.headless()
        self.browser = Chromium(self.options)
        self.tab = self.browser.latest_tab
        
    def get_temp_email(self):
        """获取邮箱地址"""
        try:
            self.tab.get("https://smailpro.com/temporary-email?ver=old")
            email_element = self.tab.ele('xpath://*[@id="app"]/main/div[1]/div[7]/div[2]/div/div[1]/div[1]/div[2]/div[2]')
            if email_element:
                email = email_element.text
                # print(f"获取到邮箱地址: {email}")
                return email
        
        except Exception as e:
            print(f"获取邮箱地址失败: {str(e)}")
            return None

    def check_new_emails(self):
        """检查新邮件"""
        try:

            email_ele = self.tab.ele('xpath://*[@id="app"]/main/div[1]/div[7]/div[2]/div/div[3]/div/div/div[1]/div/div/div')
            if email_ele:
                email_ele.click()
                
                time.sleep(2)
                
                iframe = self.tab.ele('tag:iframe')
                if iframe:
                    html_content = iframe.attr('srcdoc')
                    
                    import re
                    
                    code_match = re.search(r'code is (\d{6})', html_content)
                    code = code_match.group(1) if code_match else None
                    
                    email_match = re.search(r'verify your email address ([^\s<]+@[^\s<]+)', html_content, re.IGNORECASE)
                    email = email_match.group(1) if email_match else None
                    content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL)
                    content = re.sub(r'<[^>]+>', ' ', content)
                    content = re.sub(r'\s+', ' ', content)
                    content = re.sub(r'\{[^\}]*\}', '', content)
                    content_match = re.search(r'verify your email.*?email address by mistake\.', content, re.DOTALL)
                    main_content = content_match.group(0) if content_match else content
                    
                    if code:
                        return {
                            "subject": "Verify your email",
                            "from": "Cursor",
                            "content": main_content.strip()
                        }
                
                return None
                
        except Exception as e:
            print(f"检查新邮件失败: {str(e)}")
            return None

    def close(self):
        """关闭浏览器"""
        self.browser.quit()

def main():
    client = TempMailClient()
    processed_count = 0
    retry_count = 0
    max_retries = 10
    
    try:
        email = client.get_temp_email()
        if email:
            print(f"临时邮箱地址: {email}") 
            
        print("开始监控新邮件...")
 
        while retry_count < max_retries:
            try:
                email_content = client.check_new_emails()
                
                if email_content and processed_count == 0:
                    print("\n收到新邮件:")
                    print(f"发件人: {email_content['from']}")
                    print(f"主题: {email_content['subject']}")
                    print(f"内容: {email_content['content']}")
                    processed_count += 1
                    break
                
                retry_count += 1
                print(f"第 {retry_count} 次检查,等待3秒...")
                time.sleep(3)
                
            except KeyboardInterrupt:
                print("\n程序已停止")
                break
                
        if retry_count >= max_retries:
            print("\n达到最大重试次数,程序退出")
                
    finally:
        client.close()

if __name__ == "__main__":
    main()