:root {
    --primary-color: #007AFF;
    --background-color: #F5F5F7;
    --surface-color: #FFFFFF;
    --text-color: #1D1D1F;
    --secondary-text: #86868B;
    --border-color: #D2D2D7;
    --hover-color: #0051FF;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 3rem;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--secondary-text);
    font-size: 1.1rem;
}

.upload-area {
    background-color: var(--surface-color);
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: var(--primary-color);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.upload-icon {
    fill: var(--primary-color);
}

.upload-hint {
    color: var(--secondary-text);
    font-size: 0.9rem;
}

.compression-controls {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 12px;
    margin: 2rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quality-control {
    display: flex;
    align-items: center;
    gap: 1rem;
}

input[type="range"] {
    flex: 1;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: var(--surface-color);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.preview-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 2rem 0;
}

.preview-box {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-container {
    margin: 1rem 0;
    text-align: center;
}

.image-container img {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
}

.image-info {
    display: flex;
    justify-content: space-between;
    color: var(--secondary-text);
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.primary-button, .secondary-button {
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
}

.primary-button:hover {
    background-color: var(--hover-color);
}

.secondary-button {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
}

.secondary-button:hover {
    background-color: var(--background-color);
}

@media (max-width: 768px) {
    .preview-container {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 1rem;
    }
} 