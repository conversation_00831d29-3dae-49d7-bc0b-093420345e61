#!/bin/bash
#### 用于删除某个站点的脚本2025.1.15

SITE=$1
CONFD="/www/cloud_waf/nginx/conf.d"
VHOST="/www/cloud_waf/vhost"

# 检查是否安装jq
if ! command -v jq &> /dev/null; then
    echo "正在安装必要JSON处理器jq..."
    yum install -y jq
    if [ $? -ne 0 ]; then
        echo "安装失败，请检查yum配置或网络连接"
        exit 1
    fi
fi

# 构建文件路径
file_path="${CONFD}/vhost/${SITE}_com.conf"
# 检查执行携带参数是否正常
if [ $# -lt 1 ]; then
    echo "Usage: $0 <arg1>"
    exit 1
fi
# 检查文件是否存在
if [ ! -f "$file_path" ]; then
    echo "站点不存在!! 不再进行删除。"
    exit 1
fi

#### 处理conf.d目录下相关的配置文件 #/www/cloud_waf/nginx/conf.d/
## /www/cloud_waf/nginx/conf.d/other/siteid.json
sed -i -E "s/,?\"(${SITE}_com)\":\"\\1\"//; s/,,/,/g; s/\{,/\{/; s/,\}/\}/g" ${CONFD}/other/siteid.json
## /www/cloud_waf/nginx/conf.d/vhost/muban666_com.conf
mv -f ${CONFD}/vhost/${SITE}_com.conf /tmp/
# /www/cloud_waf/nginx/conf.d/waf/config/domains.json
#### 过虑domains.json
#### (\{"domains":\[")(.*?)("\],"name":")(\w+)(_com"\})
####$1$4.com$3$4$5
#### {"domains":["*.com"],"name":"001_com"}
sed -i "s/{\"domains\":\[\"${SITE}.com\"\],\"name\":\"${SITE}_com\"}//; s/,,/,/; s/,]/]/" ${CONFD}/waf/config/domains.json
## /www/cloud_waf/nginx/conf.d/waf/config/site.json
##yum install -y jq
\cp ${CONFD}/waf/config/site.json /tmp/site.json
jq -c "del(.${SITE}_com)" /tmp/site.json > ${CONFD}/waf/config/site.json
# /www/cloud_waf/nginx/conf.d/user/muban666_com.conf
mv -f ${CONFD}/user/${SITE}_com.conf /tmp/
# /www/cloud_waf/nginx/conf.d/cert/muban666_com/
\cp -rf ${CONFD}/cert/${SITE}_com /tmp/ && rm -rf ${CONFD}/cert/${SITE}_com

#### 处理vhost目录下相关的配置文件
# /www/cloud_waf/vhost/site_json/muban666_com.json
mv -f ${VHOST}/site_json/${SITE}_com.json /tmp/
# /www/cloud_waf/vhost/site_json/domains.json
sed -i "s/{\"domains\":\[\"${SITE}.com\"\],\"name\":\"${SITE}_com\"}//; s/,,/,/; s/,]/]/" ${VHOST}/site_json/domains.json
## /www/cloud_waf/vhost/site_json/site.json
jq -c "del(.${SITE}_com)" /tmp/site.json > ${VHOST}/site_json/site.json
# /www/cloud_waf/vhost/slice_log_json/muban666_com.json
mv -f ${VHOST}/slice_log_json/${SITE}_com.json /tmp/slice_log_json_${SITE}_com.json

#### 执行重载btw 7
#btw 7
