#!/bin/bash
# 脚本注释
# 作者：Your Name
# 创建日期：2023-07-14
# 版本：1.0
key1=");\/\/使用域名标记注释"
insert_file="/root/ajie/check.php"

function create_nlb() {
    ## 创建阿里nlb实例
    domain=`/usr/bin/python /root/ajie/yajian91_create_nlb/alibabacloud_sample/aliyun_nlb.py create`
    #domain='http://nlb-jgdlou734kws5mpuro.cn-chengdu.nlb.aliyuncs.com:61037'
    ## 文件中最后一条记录
    last_num=`grep -B1 "${key1}" ${insert_file} |grep -v "${key1}" |awk -F '=' '{print $1}'`
    ## 最后一个数字加1,再输出完整的记录
    #echo "                        $((${last_num}+1)) => '${domain}',"
    full_record="$((${last_num}+1)) => '${domain}',"
    if [ -n "$full_record" ]; then
        ## 在key1前面插入一条数据
        sed -i "/$key1/i \        ${full_record}" $insert_file
        echo "${full_record}"
    else
        echo "未找到关键字"
    fi
}

function delete_nlb() {
    ## 删除阿里nlb实例
    result=`/usr/bin/python /root/ajie/yajian91_create_nlb/alibabacloud_sample/aliyun_nlb.py delete`
    echo $result
    ## * * * * * root /usr/bin/php /root/ajie/check.php >> /root/ajie/excute.log
    echo "先休息10秒，再执行检测...."
    sleep 10
    /usr/bin/php /root/ajie/check.php

}
create_nlb
delete_nlb
