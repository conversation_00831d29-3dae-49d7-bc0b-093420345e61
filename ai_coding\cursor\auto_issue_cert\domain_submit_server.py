#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 域名提交服务器
# 提供Web界面接收域名并存入Redis

from flask import Flask, Blueprint, request, jsonify, render_template, redirect, url_for
import redis
import logging
import os

# Redis配置 - 与domain_processor.py保持一致
# REDIS_HOST = '***************'   ##同步X
# REDIS_HOST = '**************'   ##小舅1
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB = 1
REDIS_PASSWORD = 'BBMFqw8uqw65'
# 新增Redis键名，用于存储用户提交的域名
REDIS_DOMAINS_KEY = 'get_domains'  # 存储用户提交的域名列表

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("domain_submit_server.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("DomainSubmitServer")

# 创建Flask应用
app = Flask(__name__)
# 配置URL规则，不区分尾部斜杠
app.url_map.strict_slashes = False

# 初始化Redis客户端
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=True
)

# 创建域名提交蓝图
domains_bp = Blueprint('domains', __name__)

# 处理域名提交请求的函数
def process_domains_submission():
    """处理域名提交请求"""
    try:
        # 获取提交的域名
        data = request.get_json()
        if not data or 'domains' not in data:
            return jsonify({'success': False, 'message': '未提供域名数据'}), 400
        
        domains_text = data['domains']
        # 按行分割，并过滤空行
        domains = [domain.strip() for domain in domains_text.split('\n') if domain.strip()]
        
        if not domains:
            return jsonify({'success': False, 'message': '未提供有效域名'}), 400
        
        # 将域名添加到Redis
        # 使用空格分隔的字符串格式存储，与原系统保持一致
        domains_str = ' '.join(domains)
        # 检查键是否存在，决定是追加还是创建
        if redis_client.exists(REDIS_DOMAINS_KEY):
            # 键已存在，追加数据（前面加空格分隔）
            redis_client.append(REDIS_DOMAINS_KEY, ' ' + domains_str)
            logger.info(f"成功追加并存储域名: {domains_str}")
        else:
            # 键不存在，创建新键
            redis_client.set(REDIS_DOMAINS_KEY, domains_str)
            logger.info(f"成功创建并存储域名: {domains_str}")
        
        return jsonify({'success': True, 'message': '域名提交成功', 'count': len(domains)})
        
    except Exception as e:
        logger.error(f"处理域名提交请求时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

# 根路径：显示域名提交页面
@domains_bp.route('/')
def index():
    """提供域名提交页面"""
    logger.info("访问域名提交页面")
    return render_template('domain_submit.html')

# 处理域名提交
@domains_bp.route('/submit_domains', methods=['POST'])
def submit_domains():
    """处理submit_domains路径的请求"""
    logger.info("接收域名提交请求")
    return process_domains_submission()

# 注册根路径的蓝图
app.register_blueprint(domains_bp)

# 注册/domains路径的蓝图
app.register_blueprint(domains_bp, url_prefix='/domains', name='domains_with_prefix')

if __name__ == '__main__':
    # 确保模板目录存在
    os.makedirs('templates', exist_ok=True)
    
    # 启动Flask应用
    logger.info("启动域名提交服务器，监听端口: 50004")
    app.run(host='0.0.0.0', port=50004, debug=True) 
