#!/bin/bash
# 脚本注释
# 作者：Your Name
# 创建日期：2023-07-14
# 版本：1.0
key1=");\/\/使用域名标记注释"

function create_nlb() {
    for i in 1 3 4 5 6 7
    do
	insert_file="/root/ajie/check_data${i}.php"
        domain_file="/usr/share/nginx/html/ddomain${i}.txt"
        ## 创建阿里nlb实例
        domain=`/usr/bin/python /root/ajie/yajian91_create_nlb/alibabacloud_sample/aliyun_nlb${i}.py create`
        #domain='http://nlb-jgdlou734kws5mpuro.cn-chengdu.nlb.aliyuncs.com:61037'
        ## 文件中最后一条记录
        last_num=`grep -B1 "${key1}" ${insert_file} |grep -v "${key1}" |awk -F '=' '{print $1}'`
        ## 最后一个数字加1,再输出完整的记录
        #echo "                        $((${last_num}+1)) => '${domain}',"
        full_record="$((${last_num}+1)) => '${domain}',"
        if [ -n "$full_record" ] && [ -n "$last_num" ] && [ -n "$domain" ]; then
            ## 在key1前面插入一条数据
            sed -i "/$key1/i \        ${full_record}" $insert_file
            echo "${full_record}"
#	    echo -n "${domain}" > ${domain_file}
#	    cat ${domain_file}
        else
            echo "出现空字符串：last_num='${last_num}', domain='${domain}'"
    	exit 222;
        fi
    done
}
create_nlb
