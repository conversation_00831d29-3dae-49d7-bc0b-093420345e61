document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    const qualitySlider = document.getElementById('quality');
    const qualityValue = document.getElementById('qualityValue');
    const originalPreview = document.getElementById('originalPreview');
    const compressedPreview = document.getElementById('compressedPreview');
    const originalSize = document.getElementById('originalSize');
    const compressedSize = document.getElementById('compressedSize');
    const originalDimensions = document.getElementById('originalDimensions');
    const compressedDimensions = document.getElementById('compressedDimensions');
    const downloadBtn = document.getElementById('downloadBtn');
    const resetBtn = document.getElementById('resetBtn');

    let originalFile = null;
    let compressedFile = null;

    // 拖放功能
    dropZone.addEventListener('click', () => fileInput.click());
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.style.borderColor = 'var(--primary-color)';
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.style.borderColor = 'var(--border-color)';
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.style.borderColor = 'var(--border-color)';
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });

    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFile(e.target.files[0]);
        }
    });

    qualitySlider.addEventListener('input', (e) => {
        qualityValue.textContent = `${e.target.value}%`;
        if (originalFile) {
            compressImage(originalFile, e.target.value / 100);
        }
    });

    resetBtn.addEventListener('click', () => {
        location.reload();
    });

    async function handleFile(file) {
        if (!file.type.startsWith('image/')) {
            alert('请上传图片文件！');
            return;
        }

        originalFile = file;
        
        // 显示原始图片预览
        const originalUrl = URL.createObjectURL(file);
        originalPreview.src = originalUrl;
        originalSize.textContent = `大小: ${formatFileSize(file.size)}`;

        // 获取图片尺寸
        const img = new Image();
        img.onload = () => {
            originalDimensions.textContent = `尺寸: ${img.width}x${img.height}`;
        };
        img.src = originalUrl;

        // 显示控制区域
        document.querySelector('.compression-controls').style.display = 'block';
        document.querySelector('.preview-container').style.display = 'grid';
        document.querySelector('.action-buttons').style.display = 'flex';

        // 进行初始压缩
        await compressImage(file, qualitySlider.value / 100);
    }

    async function compressImage(file, quality) {
        try {
            const options = {
                maxSizeMB: 1,
                maxWidthOrHeight: 1920,
                useWebWorker: true,
                quality: quality
            };

            compressedFile = await imageCompression(file, options);
            
            // 显示压缩后的预览
            const compressedUrl = URL.createObjectURL(compressedFile);
            compressedPreview.src = compressedUrl;
            compressedSize.textContent = `大小: ${formatFileSize(compressedFile.size)}`;

            // 获取压缩后的图片尺寸
            const img = new Image();
            img.onload = () => {
                compressedDimensions.textContent = `尺寸: ${img.width}x${img.height}`;
            };
            img.src = compressedUrl;

            // 更新下载按钮
            downloadBtn.onclick = () => {
                const link = document.createElement('a');
                link.href = compressedUrl;
                link.download = `compressed_${file.name}`;
                link.click();
            };
        } catch (error) {
            console.error('压缩失败:', error);
            alert('图片压缩失败，请重试！');
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}); 