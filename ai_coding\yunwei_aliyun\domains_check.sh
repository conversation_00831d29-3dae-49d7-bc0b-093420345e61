#!/bin/bash

# 定义输入文件和输出文件
SITE="$1"

# 检查执行携带参数是否正常
if [ $# -lt 1 ]; then
    echo "Usage: $0 <arg1>"
    exit 1
fi
failed_file="failed_${SITE}"
temp_file="$(mktemp)"
# 当前IP列表有 x6 x11 x13 m3 h6 x43
valid_ips=("************" "**************" "***********" "**************" "***********" "***********" "***********" "***************" "***********" "**************" "***********" "************" "**************" "**************" "**************" "**************" "**************" "*************" "*************" "*************" "*************" "*************" "*************" "*************" "*************" "**************" "**************" "**************" "**************" "**************" "**************" "**************" "**************" "**************" "**************" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "**************" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "*************0" "*************1" "*************2" "*************3" "*************4" "*************5" "*************6" "*************7" "*************8" "*************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "**************0" "**************1" "**************2" "**************3" "**************4" "**************5" "**************6" "**************7" "**************8" "**************9" "*************00" "*************01" "*************02" "*************03" "*************04" "*************05" "*************06" "*************07" "*************08" "*************09" "*************10" "*************11" "*************12" "*************13" "*************14" "*************15" "*************16" "*************17" "*************18" "*************19" "*************20" "*************21" "*************22" "*************23" "*************24" "*************25" "*************26" "*************27" "*************28" "*************29" "*************30" "*************31" "*************32" "*************33" "*************34" "*************35" "*************36" "*************37" "*************38" "*************39" "*************40" "*************41" "*************42" "*************43" "*************44" "*************45" "*************46" "*************47" "*************48" "*************49" "*************50" "*************51" "*************52" "*************53" "*************54" "38.145.216.14" "107.163.221.2" "107.163.221.3" "107.163.221.4" "107.163.221.5" "107.163.221.6" "107.163.221.7" "107.163.221.8" "107.163.221.9" "107.163.221.10" "107.163.221.11" "107.163.221.12" "107.163.221.13" "107.163.221.14" "107.163.221.15" "107.163.221.16" "107.163.221.17" "107.163.221.18" "107.163.221.19" "107.163.221.20" "107.163.221.21" "107.163.221.22" "107.163.221.23" "107.163.221.24" "107.163.221.25" "107.163.221.26" "107.163.221.27" "107.163.221.28" "107.163.221.29" "107.163.221.30" "107.163.221.31" "107.163.221.32" "107.163.221.33" "107.163.221.34" "107.163.221.35" "107.163.221.36" "107.163.221.37" "107.163.221.38" "107.163.221.39" "107.163.221.40" "107.163.221.41" "107.163.221.42" "107.163.221.43" "107.163.221.44" "107.163.221.45" "107.163.221.46" "107.163.221.47" "107.163.221.48" "107.163.221.49" "107.163.221.50" "107.163.221.51" "107.163.221.52" "107.163.221.53" "107.163.221.54" "107.163.221.55" "107.163.221.56" "107.163.221.57" "107.163.221.58" "107.163.221.59" "107.163.221.60" "107.163.221.61" "107.163.221.62" "107.163.221.63" "107.163.221.64" "107.163.221.65" "107.163.221.66" "107.163.221.67" "107.163.221.68" "107.163.221.69" "107.163.221.70" "107.163.221.71" "107.163.221.72" "107.163.221.73" "107.163.221.74" "107.163.221.75" "107.163.221.76" "107.163.221.77" "107.163.221.78" "107.163.221.79" "107.163.221.80" "107.163.221.81" "107.163.221.82" "107.163.221.83" "107.163.221.84" "107.163.221.85" "107.163.221.86" "107.163.221.87" "107.163.221.88" "107.163.221.89" "107.163.221.90" "107.163.221.91" "107.163.221.92" "107.163.221.93" "107.163.221.94" "107.163.221.95" "107.163.221.96" "107.163.221.97" "107.163.221.98" "107.163.221.99" "107.163.221.100" "107.163.221.101" "107.163.221.102" "107.163.221.103" "107.163.221.104" "107.163.221.105" "107.163.221.106" "107.163.221.107" "107.163.221.108" "107.163.221.109" "107.163.221.110" "107.163.221.111" "107.163.221.112" "107.163.221.113" "107.163.221.114" "107.163.221.115" "107.163.221.116" "107.163.221.117" "107.163.221.118" "107.163.221.119" "107.163.221.120" "107.163.221.121" "107.163.221.122" "107.163.221.123" "107.163.221.124" "107.163.221.125" "107.163.221.126" "107.163.221.127" "107.163.221.128" "107.163.221.129" "107.163.221.130" "107.163.221.131" "107.163.221.132" "107.163.221.133" "107.163.221.134" "107.163.221.135" "107.163.221.136" "107.163.221.137" "107.163.221.138" "107.163.221.139" "107.163.221.140" "107.163.221.141" "107.163.221.142" "107.163.221.143" "107.163.221.144" "107.163.221.145" "107.163.221.146" "107.163.221.147" "107.163.221.148" "107.163.221.149" "107.163.221.150" "107.163.221.151" "107.163.221.152" "107.163.221.153" "107.163.221.154" "107.163.221.155" "107.163.221.156" "107.163.221.157" "107.163.221.158" "107.163.221.159" "107.163.221.160" "107.163.221.161" "107.163.221.162" "107.163.221.163" "107.163.221.164" "107.163.221.165" "107.163.221.166" "107.163.221.167" "107.163.221.168" "107.163.221.169" "107.163.221.170" "107.163.221.171" "107.163.221.172" "107.163.221.173" "107.163.221.174" "107.163.221.175" "107.163.221.176" "107.163.221.177" "107.163.221.178" "107.163.221.179" "107.163.221.180" "107.163.221.181" "107.163.221.182" "107.163.221.183" "107.163.221.184" "107.163.221.185" "107.163.221.186" "107.163.221.187" "107.163.221.188" "107.163.221.189" "107.163.221.190" "107.163.221.191" "107.163.221.192" "107.163.221.193" "107.163.221.194" "107.163.221.195" "107.163.221.196" "107.163.221.197" "107.163.221.198" "107.163.221.199" "107.163.221.200" "107.163.221.201" "107.163.221.202" "107.163.221.203" "107.163.221.204" "107.163.221.205" "107.163.221.206" "107.163.221.207" "107.163.221.208" "107.163.221.209" "107.163.221.210" "107.163.221.211" "107.163.221.212" "107.163.221.213" "107.163.221.214" "107.163.221.215" "107.163.221.216" "107.163.221.217" "107.163.221.218" "107.163.221.219" "107.163.221.220" "107.163.221.221" "107.163.221.222" "107.163.221.223" "107.163.221.224" "107.163.221.225" "107.163.221.226" "107.163.221.227" "107.163.221.228" "107.163.221.229" "107.163.221.230" "107.163.221.231" "107.163.221.232" "107.163.221.233" "107.163.221.234" "107.163.221.235" "107.163.221.236" "107.163.221.237" "107.163.221.238" "107.163.221.239" "107.163.221.240" "107.163.221.241" "107.163.221.242" "107.163.221.243" "107.163.221.244" "107.163.221.245" "107.163.221.246" "107.163.221.247" "107.163.221.248" "107.163.221.249" "107.163.221.250" "107.163.221.251" "107.163.221.252" "107.163.221.253" "107.163.221.254" "211.174.59.136" "211.174.59.154" "103.80.134.27" "211.233.25.170" "211.174.59.152" "211.174.59.222" "211.174.59.223" "103.211.125.242" "103.211.125.243" "103.211.125.244" "103.211.125.245" "103.211.125.246" "107.163.142.130" "107.163.142.131" "107.163.142.132" "107.163.142.133" "107.163.142.134" "107.163.142.135" "107.163.142.136" "107.163.142.137" "107.163.142.138" "107.163.142.139" "107.163.142.140" "107.163.142.141" "107.163.142.142" "107.163.142.143" "107.163.142.144" "107.163.142.145" "107.163.142.146" "107.163.142.147" "107.163.142.148" "107.163.142.149" "107.163.142.150" "107.163.142.151" "107.163.142.152" "107.163.142.153" "107.163.142.154" "107.163.142.155" "107.163.142.156" "107.163.142.157" "107.163.142.158" "107.163.142.159" "107.163.142.160" "107.163.142.161" "107.163.142.162" "107.163.142.163" "107.163.142.164" "107.163.142.165" "107.163.142.166" "107.163.142.167" "107.163.142.168" "107.163.142.169" "107.163.142.170" "107.163.142.171" "107.163.142.172" "107.163.142.173" "107.163.142.174" "107.163.142.175" "107.163.142.176" "107.163.142.177" "107.163.142.178" "107.163.142.179" "107.163.142.180" "107.163.142.181" "107.163.142.182" "107.163.142.183" "107.163.142.184" "107.163.142.185" "107.163.142.186" "107.163.142.187" "107.163.142.188" "107.163.142.189" "107.163.142.190" "107.163.137.130" "107.163.137.131" "107.163.137.132" "107.163.137.133" "107.163.137.134" "107.163.137.135" "107.163.137.136" "107.163.137.137" "107.163.137.138" "107.163.137.139" "107.163.137.140" "107.163.137.141" "107.163.137.142" "107.163.137.143" "107.163.137.144" "107.163.137.145" "107.163.137.146" "107.163.137.147" "107.163.137.148" "107.163.137.149" "107.163.137.150" "107.163.137.151" "107.163.137.152" "107.163.137.153" "107.163.137.154" "107.163.137.155" "107.163.137.156" "107.163.137.157" "107.163.137.158" "107.163.137.159" "107.163.137.160" "107.163.137.161" "107.163.137.162" "107.163.137.163" "107.163.137.164" "107.163.137.165" "107.163.137.166" "107.163.137.167" "107.163.137.168" "107.163.137.169" "107.163.137.170" "107.163.137.171" "107.163.137.172" "107.163.137.173" "107.163.137.174" "107.163.137.175" "107.163.137.176" "107.163.137.177" "107.163.137.178" "107.163.137.179" "107.163.137.180" "107.163.137.181" "107.163.137.182" "107.163.137.183" "107.163.137.184" "107.163.137.185" "107.163.137.186" "107.163.137.187" "107.163.137.188" "107.163.137.189" "107.163.137.190" "23.231.169.130" "23.231.169.131" "23.231.169.132" "23.231.169.133" "23.231.169.134" "23.231.169.135" "23.231.169.136" "23.231.169.137" "23.231.169.138" "23.231.169.139" "23.231.169.140" "23.231.169.141" "23.231.169.142" "23.231.169.143" "23.231.169.144" "23.231.169.145" "23.231.169.146" "23.231.169.147" "23.231.169.148" "23.231.169.149" "23.231.169.150" "23.231.169.151" "23.231.169.152" "23.231.169.153" "23.231.169.154" "23.231.169.155" "23.231.169.156" "23.231.169.157" "23.231.169.158" "23.231.169.159" "23.231.169.160" "23.231.169.161" "23.231.169.162" "23.231.169.163" "23.231.169.164" "23.231.169.165" "23.231.169.166" "23.231.169.167" "23.231.169.168" "23.231.169.169" "23.231.169.170" "23.231.169.171" "23.231.169.172" "23.231.169.173" "23.231.169.174" "23.231.169.175" "23.231.169.176" "23.231.169.177" "23.231.169.178" "23.231.169.179" "23.231.169.180" "23.231.169.181" "23.231.169.182" "23.231.169.183" "23.231.169.184" "23.231.169.185" "23.231.169.186" "23.231.169.187" "23.231.169.188" "23.231.169.189" "23.231.169.190" "23.231.172.130" "23.231.172.131" "23.231.172.132" "23.231.172.133" "23.231.172.134" "23.231.172.135" "23.231.172.136" "23.231.172.137" "23.231.172.138" "23.231.172.139" "23.231.172.140" "23.231.172.141" "23.231.172.142" "23.231.172.143" "23.231.172.144" "23.231.172.145" "23.231.172.146" "23.231.172.147" "23.231.172.148" "23.231.172.149" "23.231.172.150" "23.231.172.151" "23.231.172.152" "23.231.172.153" "23.231.172.154" "23.231.172.155" "23.231.172.156" "23.231.172.157" "23.231.172.158" "23.231.172.159" "23.231.172.160" "23.231.172.161" "23.231.172.162" "23.231.172.163" "23.231.172.164" "23.231.172.165" "23.231.172.166" "23.231.172.167" "23.231.172.168" "23.231.172.169" "23.231.172.170" "23.231.172.171" "23.231.172.172" "23.231.172.173" "23.231.172.174" "23.231.172.175" "23.231.172.176" "23.231.172.177" "23.231.172.178" "23.231.172.179" "23.231.172.180" "23.231.172.181" "**************" "**************" "**************" "**************" "**************" "**************" "**************" "**************" "**************")
# 清空输出文件
> "$failed_file"
> "$temp_file"
## 拉取bt的nginx配置文件
if ! sh fetch_domains.sh ${SITE};then
    echo "fetch_domains.sh failed. exiting..."
    exit 1
fi

# 读取域名文本并处理
while IFS=' ' read -r -a domains; do
    for domain in "${domains[@]}"; do
        # 使用nslookup解析域名，收集所有解析到的IP
        resolved_ips=($(nslookup "$domain" 2>/dev/null | awk '/^Address: / {print $2}'))
        echo "$domain ${resolved_ips[@]}"
        # 如果没有解析到IP，记录失败
        if [[ ${#resolved_ips[@]} -eq 0 ]]; then
            echo "$domain 解析失败" >> "$failed_file"
        else
            # 拼接所有解析的IP地址
            ip_string=""
            valid_ip_found=false
            for ip in "${resolved_ips[@]}"; do
                # 如果解析到的IP在允许的列表中
                if [[ " ${valid_ips[@]} " =~ " $ip " ]]; then
                    valid_ip_found=true
                fi
                ip_string+=" $ip"
            done

            # 如果解析成功且IP在有效列表中，保存到临时文件
            if $valid_ip_found; then
                echo -n "$domain " >> "$temp_file"
            else
                # 如果解析成功但IP不在有效列表中，记录失败
                echo "$domain$ip_string" >> "$failed_file"
            fi
        fi
    done
done < "${SITE}"

# 去除末尾多余的空格并保存到最终输出文件
sed 's/[[:space:]]*$//' "$temp_file" > "${SITE}"

# 清理临时文件
rm -f "$temp_file"

echo "可申请的域名:"
echo "================"
sed 's/ /\n/g' "${SITE}"
echo -e "\n================"


echo "不可申请的域名:"
echo "================"
cat $failed_file
echo "================"

