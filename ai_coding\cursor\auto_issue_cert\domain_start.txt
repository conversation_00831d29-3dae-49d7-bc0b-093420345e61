
######## cat /etc/systemd/system/domain_submit_server.service 
[Unit]
Description=Domain Submit Server Service
After=network.target

[Service]
User=root
WorkingDirectory=/root/ajie/yunwei/
ExecStart=/usr/bin/python3.8 /root/ajie/yunwei/domain_submit_server.py
Restart=always

[Install]
WantedBy=multi-user.target


######## cat /etc/systemd/system/domain_check.service 
[Unit]
Description=Domain check Service
After=network.target

[Service]
User=root
WorkingDirectory=/root/ajie/yunwei/
ExecStart=/usr/bin/python3.8 /root/ajie/yunwei/domain_check.py
Restart=always

[Install]
WantedBy=multi-user.target


######## cat /etc/systemd/system/domain_generate.service 
[Unit]
Description=Domain Submit generate Service
After=network.target

[Service]
User=root
WorkingDirectory=/root/ajie/yunwei/
ExecStart=/usr/bin/python3.8 /root/ajie/yunwei/domain_generate.py
Restart=always

[Install]
WantedBy=multi-user.target

