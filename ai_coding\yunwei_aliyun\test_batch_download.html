<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ACME批量下载功能测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .file-list { font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>ACME HTTP验证文件批量下载功能测试</h1>
    
    <div class="test-section">
        <h2>测试场景：模拟100个域名的HTTP验证</h2>
        <p>这个测试将模拟为100个域名生成HTTP验证文件并打包下载。</p>
        <button class="btn" onclick="testBatchDownload()">开始测试批量下载</button>
        <div id="testResult" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h2>功能说明</h2>
        <ul>
            <li><strong>原始方式</strong>：需要点击100次下载按钮，管理100个单独文件</li>
            <li><strong>优化方式</strong>：点击1次按钮，下载1个ZIP包含所有文件</li>
            <li><strong>目录结构</strong>：自动创建 .well-known/acme-challenge/ 目录</li>
            <li><strong>说明文档</strong>：包含详细的使用说明README.txt</li>
        </ul>
    </div>

    <script>
        // 模拟ACME验证数据
        function generateMockHttpFiles(count) {
            var files = [];
            for(var i = 1; i <= count; i++) {
                var domain = `example${i}.com`;
                var token = generateRandomToken();
                var content = `${token}.mock_thumbprint_${i}`;
                
                files.push({
                    domain: domain,
                    token: token,
                    content: content
                });
            }
            return files;
        }

        // 生成随机token（模拟ACME token）
        function generateRandomToken() {
            var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
            var result = '';
            for(var i = 0; i < 43; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // 生成README内容
        function generateReadmeContent(httpFiles) {
            var content = [];
            content.push("ACME HTTP验证文件批量下载包 - 测试版本");
            content.push("=====================================");
            content.push("");
            content.push("本ZIP包包含了SSL证书申请所需的HTTP验证文件。");
            content.push("");
            content.push("使用说明：");
            content.push("1. 解压本ZIP文件到您的网站根目录");
            content.push("2. 确保文件路径为：网站根目录/.well-known/acme-challenge/[验证文件]");
            content.push("3. 确保这些文件可以通过HTTP访问（80端口）");
            content.push("4. 返回ACME客户端点击\"开始验证\"按钮");
            content.push("");
            content.push("包含的验证文件：");
            httpFiles.forEach(function(file, index) {
                if(index < 10 || index >= httpFiles.length - 5) {
                    content.push(`- ${file.domain} → .well-known/acme-challenge/${file.token}`);
                } else if(index === 10) {
                    content.push(`... (省略 ${httpFiles.length - 15} 个文件) ...`);
                }
            });
            content.push("");
            content.push("注意事项：");
            content.push("- 文件必须通过HTTP（80端口）访问，不能是HTTPS");
            content.push("- 确保服务器没有阻止访问.well-known目录");
            content.push("- 验证完成后可以删除这些文件");
            content.push("");
            content.push("Generated by ACME Web Browser Client - Test Version");
            content.push("Time: " + new Date().toLocaleString());
            
            return content.join('\n');
        }

        // 测试批量下载功能
        function testBatchDownload() {
            var resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>正在生成测试数据...</p>';

            try {
                // 生成100个模拟HTTP验证文件
                var httpFiles = generateMockHttpFiles(100);
                
                resultDiv.innerHTML = `<p>已生成 ${httpFiles.length} 个模拟HTTP验证文件，正在打包...</p>`;

                // 创建ZIP文件
                var zip = new JSZip();
                var wellKnownFolder = zip.folder(".well-known").folder("acme-challenge");

                // 添加所有HTTP验证文件到ZIP
                httpFiles.forEach(function(file) {
                    wellKnownFolder.file(file.token, file.content);
                });

                // 添加README说明文件
                var readmeContent = generateReadmeContent(httpFiles);
                zip.file("README.txt", readmeContent);

                // 生成ZIP文件并下载
                zip.generateAsync({type:"blob"}).then(function(content) {
                    var url = URL.createObjectURL(content);
                    var downA = document.createElement("A");
                    downA.href = url;
                    downA.download = "acme-http-verification-files-test.zip";
                    downA.click();

                    // 显示结果
                    var fileListHtml = '<div class="file-list">';
                    fileListHtml += '<h3>ZIP包内容预览：</h3>';
                    fileListHtml += '<p>📁 .well-known/acme-challenge/</p>';
                    
                    httpFiles.slice(0, 5).forEach(function(file) {
                        fileListHtml += `<p>&nbsp;&nbsp;📄 ${file.token} (${file.domain})</p>`;
                    });
                    
                    if(httpFiles.length > 5) {
                        fileListHtml += `<p>&nbsp;&nbsp;... 还有 ${httpFiles.length - 5} 个文件</p>`;
                    }
                    
                    fileListHtml += '<p>📄 README.txt</p>';
                    fileListHtml += '</div>';

                    resultDiv.innerHTML = `
                        <h3>✅ 测试成功！</h3>
                        <p><strong>原始方式</strong>：需要点击 ${httpFiles.length} 次下载按钮</p>
                        <p><strong>优化方式</strong>：只需点击 1 次下载按钮</p>
                        <p><strong>效率提升</strong>：减少了 ${((httpFiles.length - 1) / httpFiles.length * 100).toFixed(1)}% 的操作次数</p>
                        <p><strong>文件大小</strong>：约 ${(content.size / 1024).toFixed(1)} KB</p>
                        ${fileListHtml}
                        <p><em>ZIP文件已自动下载，请检查下载文件夹。</em></p>
                    `;

                }).catch(function(err) {
                    console.error("ZIP generation failed:", err);
                    resultDiv.innerHTML = '<p style="color:red;">❌ ZIP文件生成失败: ' + err.message + '</p>';
                });

            } catch(e) {
                console.error("Test failed:", e);
                resultDiv.innerHTML = '<p style="color:red;">❌ 测试失败: ' + e.message + '</p>';
            }
        }
    </script>
</body>
</html>
