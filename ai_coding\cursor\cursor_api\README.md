
根据作者项目修改成一个比较系统的内容，不多说，直接上实操：
【秘】Cursor 的神秘代码
一、相关容器文件
1、docker-compose.yml内容：

2、.env文件（后边步骤二在windows操作生成）

二、在VPS或者本机windows运行如下python文件自动生成.env文件，代码如下：

三、手动操作替换docker-compose.yml当前目录下的.env文件，重新调用
docker-compose restart && docker-compose pull && docker-compose up -d && docker-compose logs -f --tail=100

该项目会在容器中自动刷新生成x-cursor-checksum并自动调用

基础配置

接口地址：http://localhost:3998/v1/chat/completions
请求方法：POST
认证方式：Bearer Token（使用.env里边AUTH_TOKENS=后边的值）
实际调用(已经写死.env里边的token了，所以调用token随便输入)

curl -X POST http://localhost:3998/v1/chat/completions \
     -H "Content-Type: application/json" \
     -d '{
           "model": "claude-3-5-sonnet-20241022",
           "messages": [{"role": "user", "content": "你好"}],
           "stream": false
         }'