#!/bin/bash

# 检查是否提供了站点名称作为参数
if [ -z "$1" ]; then
    echo "使用方法: $0 <site_name>"
    exit 1
fi

site_name=$1

# 同步 nginx 配置文件和证书到 /tmp 目录
rsync -avz root@**************:/www/server/panel/vhost/nginx/${site_name}.com.conf /tmp/
#rsync -avz --address=************** root@**************:/www/server/panel/vhost/nginx/${site_name}.com.conf /tmp/

NGINX_CONF="/tmp/${site_name}.com.conf"
OUTPUT_FILE="/root/${site_name}"

# 检查配置文件是否存在
if [ ! -f "$NGINX_CONF" ]; then
    echo "配置文件 $NGINX_CONF 不存在。"
    exit 1
fi

# 提取 server_name 指令中的域名，支持多行，并排除指定域名
awk -v site="${site_name}.com" '
/server_name/ {
    # 移除 "server_name" 关键字
    sub(/server_name\s+/, "")
    # 将当前行内容保存
    names = $0
    # 检查是否包含分号 ";"
    if ($0 ~ /;/) {
        # 移除分号
        sub(/;/, "", names)
    } else {
        # 如果当前行不包含分号，继续读取下一行
        while (getline && $0 !~ /;/) {
            names = names " " $0
        }
        # 移除分号
        sub(/;/, "", names)
    }
    # 打印提取的域名
    print names
}
' "$NGINX_CONF" | \
# 将域名分隔为单独的行
tr ' ' '\n' | \
# 排除指定的域名
grep -v "^${site_name}.com$" | \
# 过滤出有效的域名格式（避免包含 index.php 等无用行）
grep -E '^[a-zA-Z0-9.-]+$' | \
# 将域名重新用空格连接
paste -sd " " - | \
# 去除行首和行尾的空格，并压缩多余的空格
sed 's/^ *//; s/ *$//' | \
tr -s ' ' > "$OUTPUT_FILE"

# 检查是否成功生成输出文件
if [ $? -eq 0 ]; then
    echo "$OUTPUT_FILE"
else
    echo "域名提取失败。"
    exit 1
fi

