# Context
Filename: acme_analysis_task.md
Created On: 2025-01-18
Created By: AI
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
结合acme.sh脚本，请重新梳理acme_product.md功能开发文档。
主要体现出执行`sh /root/.acme.sh/acme.sh --issue --server letsencrypt -w /usr/share/nginx/html --cert-home /root/.acme.sh/longxia108 --force -d ih.a12kt6.shop -d ih.3fgbzv.shop`时的流程，同时标记出关键函数名，方便后续再次开发时快速复盘。

# Project Overview
ACME自动化申请SSL证书系统，基于acme.sh脚本实现，支持多服务器环境下的证书申请和同步。当前版本包含了多服务器同步逻辑和批量域名处理功能。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 脚本结构分析
- 脚本总行数：8166行
- 主要配置区域：1-200行，包含常量定义、CA服务器配置、同步服务器配置
- 核心函数区域：2000-5000行
- 参数处理区域：7800-8166行

## 关键配置项发现
1. **多服务器同步配置**（第3-6行）：
   ```bash
   SYNC_SERVERS="root@************ root@*********** root@*************** root@*************"
   SYNC_SSH_PORT="17888"
   SYNC_SSH_OPTS="-o StrictHostKeyChecking=no"
   ```

2. **CA服务器配置**（第28-48行）：
   - Let's Encrypt生产环境：CA_LETSENCRYPT_V2
   - Let's Encrypt测试环境：CA_LETSENCRYPT_V2_TEST
   - 其他CA：ZeroSSL、BuyPass、SSL.com、Google等

## 关键函数识别

### 1. 主执行函数
- `main()` (第8154行): 脚本入口点
- `_process()` (参数处理): 解析命令行参数
- `issue()` (第4429行): 证书申请核心函数

### 2. 同步相关函数
- `_sync_to_remote_servers()` (第2524行): 同步单个文件到远程服务器
- `_sync_challenge_dir()` (第2552行): 批量同步验证目录到远程服务器

### 3. 账户管理函数
- `_regAccount()`: 注册ACME账户
- `_create_account_key()` (第1513行): 创建账户密钥
- `__calcAccountKeyHash()`: 计算账户密钥哈希

### 4. 密钥和证书管理函数
- `createDomainKey()`: 创建域名密钥
- `_createkey()` (第1136行): 通用密钥创建函数
- `_createcsr()` (第1244行): 创建证书签名请求

### 5. API交互函数
- `_initAPI()` (第2816行): 初始化ACME API
- `_send_signed_request()` (第2160行): 发送签名请求
- `_post()` (第1936行): HTTP POST请求
- `_get()` (第2068行): HTTP GET请求

### 6. 配置管理函数
- `_savedomainconf()` (第2410行): 保存域名配置
- `_readdomainconf()` (第2420行): 读取域名配置
- `_initpath()` (第2884行): 初始化路径配置

## 特定命令参数分析
目标命令：`sh /root/.acme.sh/acme.sh --issue --server letsencrypt -w /usr/share/nginx/html --cert-home /root/.acme.sh/longxia108 --force -d ih.a12kt6.shop -d ih.3fgbzv.shop`

解析结果：
- `--issue`: 触发issue()函数
- `--server letsencrypt`: 使用Let's Encrypt CA
- `-w /usr/share/nginx/html`: webroot验证方式，验证路径
- `--cert-home /root/.acme.sh/longxia108`: 证书保存目录
- `--force`: 强制更新，忽略现有证书
- `-d ih.a12kt6.shop -d ih.3fgbzv.shop`: 两个待申请域名

## 执行流程追踪
1. main() -> _process() 解析参数
2. case语句匹配"issue"命令
3. 调用issue()函数，传入解析后的参数
4. issue()函数内部将执行完整的证书申请流程

## 多服务器同步机制发现
- 使用SSH + SCP/rsync进行文件同步
- 支持自定义SSH端口和选项
- 在HTTP-01验证过程中自动同步验证文件
- 支持批量同步整个目录结构

# Proposed Solution (Populated by INNOVATE mode)

## 文档重构方案分析

### 方案一：基于实际执行流程的线性重构
按照具体命令的真实执行序列重新组织文档结构，每个步骤直接映射到具体函数调用。
- **优势**：完全对应实际代码执行路径，便于问题排查和代码理解
- **不足**：打破传统模块化结构，初次阅读适应成本较高

### 方案二：双层架构文档设计  
保持现有模块化概述，在每个模块下增加"实际执行追踪"子章节，标注函数名和代码行数。
- **优势**：平衡了整体可读性和技术细节，保持良好的文档层次
- **挑战**：需要平衡概念描述和技术细节的比重

### 方案三：交互式流程图 + 函数映射
将每个流程节点标注对应的核心函数名，形成可直接用于代码导航的交互式文档。
- **优势**：结合可视化理解与精确代码定位
- **限制**：流程图可能因函数标注而过于复杂

### 推荐的混合方案
采用混合设计，包含三个核心部分：
1. **执行概览**：基于具体命令的端到端执行流程追踪
2. **功能模块详解**：保持模块化结构，增加函数映射和代码定位
3. **开发复盘指南**：提供快速定位关键代码的索引表

这种设计能同时满足系统架构理解、执行流程追踪、快速代码定位三种使用场景。

# Implementation Plan (Generated by PLAN mode)

## 详细实施规格

### 文档结构重新设计
新的acme_product.md将采用三层结构设计：
- **第一层：执行概览** - 完整追踪目标命令的端到端执行流程
- **第二层：功能模块详解** - 保持现有模块化结构，增强技术细节  
- **第三层：开发复盘指南** - 专门的快速定位索引

### 具体修改计划

#### 第一部分：新增"命令执行全链路追踪"章节
- 详细分解命令：`sh /root/.acme.sh/acme.sh --issue --server letsencrypt -w /usr/share/nginx/html --cert-home /root/.acme.sh/longxia108 --force -d ih.a12kt6.shop -d ih.3fgbzv.shop`
- 按执行顺序列出关键函数调用链：`main()` -> `_process()` -> `issue()` -> 各子功能函数
- 每个步骤标注对应的acme.sh代码行数和函数名
- 添加参数解析详解，映射每个参数到具体的配置变量

#### 第二部分：现有功能模块增强
- **命令行解析模块**：增加 `_process()` 函数详解（第7800-8166行）
- **账户管理模块**：增加 `_regAccount()`, `_create_account_key()` (第1513行) 函数说明
- **域名验证模块**：增加HTTP-01验证具体实现函数标注
- **证书申请模块**：增加 `_createcsr()` (第1244行), `_send_signed_request()` (第2160行) 函数详解
- **多服务器同步模块**：详解 `_sync_to_remote_servers()` (第2524行) 和 `_sync_challenge_dir()` (第2552行)

#### 第三部分：新增"开发复盘快速索引"章节
- 按功能分类的函数索引表
- 常见问题排查的代码定位指南
- 关键配置项速查表

#### 第四部分：流程图优化
- 在现有流程图的每个节点添加对应的函数名标注
- 特别针对HTTP-01验证流程，详细标注同步相关的函数调用
- 为多域名场景（ih.a12kt6.shop 和 ih.3fgbzv.shop）添加专门的处理流程说明

### 技术实现细节

#### 代码块格式标准化
所有函数说明都将使用统一格式：
```
函数名: function_name()
位置: acme.sh第X行  
功能: 具体功能描述
调用时机: 在执行流程中的位置
关键参数: 重要参数说明
```

#### 配置项映射表
创建命令行参数到内部变量的完整映射：
- `--issue` -> `_CMD="issue"`
- `--server letsencrypt` -> `ACME_DIRECTORY=$CA_LETSENCRYPT_V2`
- `-w /usr/share/nginx/html` -> `_web_roots`
- `--cert-home /root/.acme.sh/longxia108` -> 证书保存路径配置
- `--force` -> `FORCE="1"`
- `-d` -> 域名列表处理逻辑

Implementation Checklist:
1. 备份原始acme_product.md文件
2. 创建新的文档结构框架
3. 添加"命令执行全链路追踪"章节，包含完整的函数调用链
4. 重写"产品概述"部分，增加关键函数的概述说明
5. 增强"技术架构"部分，为每个模块添加函数映射表
6. 更新现有的工作流程图，在每个节点添加函数名标注
7. 重构"使用示例"部分，详细分解目标命令的执行过程
8. 新增"HTTP-01验证流程"部分，详细说明多服务器同步机制
9. 创建"开发复盘快速索引"章节，包含函数索引和问题排查指南
10. 更新所有流程图，添加针对多域名场景的处理说明
11. 验证所有函数名和行数的准确性
12. 检查文档的完整性和一致性 

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "完成acme_product.md文档重构"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2025-01-18
    *   Step: 完成acme_product.md文档重构的完整实施
    *   Modifications: 
        - 备份原始acme_product.md文件
        - 重新设计文档结构为三层架构（执行概览+功能模块详解+开发复盘指南）
        - 添加命令执行全链路追踪章节，详细标注关键函数
        - 重写技术架构部分，增加系统模块架构图和函数映射表
        - 更新工作流程图，在每个节点添加函数名标注
        - 创建详细的使用示例分解，包含13个执行阶段追踪
        - 新增开发复盘快速索引章节，包含函数索引表和问题排查指南
        - 特别优化了多域名场景(ih.a12kt6.shop + ih.3fgbzv.shop)的处理流程说明
        - 标注了所有关键函数的代码行数和调用时机
    *   Change Summary: 完全重构了acme_product.md文档，实现了从概念描述到具体代码实现的完整映射
    *   Reason: 执行计划步骤1-12的完整实施
    *   Blockers: None
    *   Status: Pending Confirmation 