#!/bin/bash

## 用于替换waf中nginx转发时增加header
## 需要替换两个目录下的文件：/www/cloud_waf/nginx/conf.d/vhost/ 和 /www/cloud_waf/vhost/site_json/

## 替换json文件中的内容，文件会有两种格式所以需要跑两次
sed -i 's|proxy_add_x_forwarded_for"]|proxy_add_x_forwarded_for","X-Forwarded-Proto $scheme"]|g' /www/cloud_waf/vhost/site_json/*
sed -i '/^[[:space:]]*"X-Forwarded-For \$proxy_add_x_forwarded_for"/{N; s/^\([[:space:]]*"X-Forwarded-For \$proxy_add_x_forwarded_for"\n[[:space:]]*]\)/                            "X-Forwarded-For \$proxy_add_x_forwarded_for",\n                            "X-Forwarded-Proto \$scheme"\n                        ]/;}' /www/cloud_waf/vhost/site_json/*

## 替换各站点nginx文件内容
sed -i '/^[[:space:]]*proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;/{N; s/^\([[:space:]]*proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n[[:space:]]*proxy_cache off;\)/                proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\n                proxy_set_header X-Forwarded-Proto \$scheme;\n                proxy_cache off;/;}' /www/cloud_waf/nginx/conf.d/vhost/*

