# 证书自动化申请系统设计文档

## 项目概述

本项目旨在实现证书申请的自动化流程，通过Python 3.8和Shell脚本在CentOS 7服务器上完成域名获取、检测、申请证书及同步部署的全流程自动化操作。

## 技术栈

- 编程语言：Python 3.8+、Shell脚本
- 运行环境：CentOS 7
- 数据存储：Redis 3.2.12
- 通知机制：Telegram机器人（先不实现）
- 同步工具：rsync

## 系统架构

系统分为三个主要任务模块：

1. 域名接收与处理模块
2. 证书生成模块（先不实现）
3. 证书同步模块（先不实现）

## 详细设计

### 任务一：域名接收与处理模块

#### 1. 接收并保存域名

- **功能描述**：通过接口获取需要申请证书的域名文本
- **实现方式**：
  - 读取接口数据，接口格式示例：`a.com b.com c.com d.com e.com`
  - 将域名保存到临时文件：`temp_file="$(mktemp)"`
  - 域名格式示例：`a.com b.com c.com d.com e.com`

#### 2. 域名过滤与解析检测

- **功能描述**：对域名进行两轮过滤，确保只处理有效且需要申请证书的域名
- **实现方式**：
  - **第一轮过滤**：
    - 检查nginx配置中是否已存在该域名，需要精确匹配
    - 过滤命令：`grep -roP "(^|\s)domain.com(\s|;|$)" /www/cloud_waf/nginx/conf.d/vhost/`
    - 如已存在则剔除该域名
  - **第二轮过滤**：
    - 对剩余域名进行dig解析检测
    - 如解析失败或IP不在解析列表中则剔除
    - 使用已实现的`domains_check.sh`脚本
  - 过滤后的域名保存到`${SITE}`文件中

#### 3. Redis队列管理

- **功能描述**：使用redis存放两组数据，分别使用Hash和List来管理站点ID、队列
- **实现方式**：
  - 从Redis的Hash中取出可用的站点ID(key)及域名(values)
  - 将`${SITE}`文件中的域名循环添加到站点ID对应的values中
  - 域名数量控制逻辑：
    - 当域名数量达到100个上限时：
      - 将当前站点ID写入Redis队列准备生成证书
      - 同时创建新站点ID（命名规则：auto1.com → auto2.com）
      - 继续添加剩余域名
    - 所有域名添加完成后：
      - 最后一个站点ID也要写入Redis队列准备生成证书
      - 同时标记该最后一个站点ID，存放在Hash表中下次继续使用
    - 如添加域名后刚好满100个：
      - 最后一个站点ID也是写入Redis队列准备生成证书
      - 同时新建新站点ID，并标记到Hash表下次使用

### 任务二：证书生成模块

#### 1. 取Redis参数生成证书

- **功能描述**：监测Redis队列，取出队列任务生成证书
- **实现方式**：
  - 实时监测Redis队列
  - 发现新任务时，从右取出一条队列中的key和values
  - 将values中的域名写入`${SITE}`临时文件
  - 使用key作为参数执行申请证书脚本`sh issue_cert.sh key`（已实现）
  - 成功后去除队列标记

#### 2. 写入WAF（先不生成）

#### 3. 写入宝塔（先不生成）

#### 4. 失败处理

- 生成失败时发送Telegram机器人消息告警（先不生成）
- 重新加入到Redis队列后面等待下一次生成（先不生成）

### 任务三：证书同步模块（先不生成）

#### 1. 执行同步

- **功能描述**：将生成的证书同步到各主机
- **实现方式**：
  - 证书成功生成并配置好WAF和宝塔后
  - 执行rsync同步命令到各主机（已实现）

## 已实现功能

- `domains_check.sh`：域名解析检测
- `issue_cert.sh`：申请证书脚本
- rsync同步功能

## 先不生成功能

- Telegram机器人告警逻辑
- 写入WAF功能
- 写入宝塔功能
- 证书生成失败后重新加入队列的逻辑

## 流程图

```
接口获取域名 → 域名过滤与检测 → 写入Redis队列 → 生成证书 → 配置WAF和宝塔 → 同步到各主机
```

## 注意事项

- Redis队列管理需要考虑并发情况
- 域名数量上限为每个站点100个
- 证书生成失败需要有完善的告警和重试机制