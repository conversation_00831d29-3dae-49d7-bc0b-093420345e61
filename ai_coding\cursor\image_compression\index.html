<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片压缩工具</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 引入图片压缩库 -->
    <script src="https://cdn.jsdelivr.net/npm/browser-image-compression@2.0.2/dist/browser-image-compression.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>图片压缩工具</h1>
            <p class="subtitle">简单、高效的在线图片压缩工具</p>
        </header>

        <main>
            <div class="upload-area" id="dropZone">
                <input type="file" id="fileInput" accept="image/*" hidden>
                <div class="upload-content">
                    <svg class="upload-icon" width="40" height="40" viewBox="0 0 24 24">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    <p>拖放图片到这里或点击上传</p>
                    <p class="upload-hint">支持 PNG、JPG 等格式</p>
                </div>
            </div>

            <div class="compression-controls" style="display: none;">
                <div class="quality-control">
                    <label for="quality">压缩质量</label>
                    <input type="range" id="quality" min="0" max="100" value="75">
                    <span id="qualityValue">75%</span>
                </div>
            </div>

            <div class="preview-container" style="display: none;">
                <div class="preview-box">
                    <h3>原始图片</h3>
                    <div class="image-container">
                        <img id="originalPreview" alt="原始图片预览">
                    </div>
                    <div class="image-info">
                        <span id="originalSize"></span>
                        <span id="originalDimensions"></span>
                    </div>
                </div>

                <div class="preview-box">
                    <h3>压缩后</h3>
                    <div class="image-container">
                        <img id="compressedPreview" alt="压缩后预览">
                    </div>
                    <div class="image-info">
                        <span id="compressedSize"></span>
                        <span id="compressedDimensions"></span>
                    </div>
                </div>
            </div>

            <div class="action-buttons" style="display: none;">
                <button id="downloadBtn" class="primary-button">下载压缩后的图片</button>
                <button id="resetBtn" class="secondary-button">重新开始</button>
            </div>
        </main>
    </div>
    <script src="script.js"></script>
</body>
</html> 