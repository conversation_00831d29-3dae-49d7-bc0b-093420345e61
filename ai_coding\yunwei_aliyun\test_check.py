#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 域名接收与处理模块

import os
import sys
import tempfile
import subprocess
import redis
import requests
import logging
import time
import socket

# 证书自动化申请系统配置文件
# Redis配置
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = 'BBMFqw8uqw65'
# 队列配置
REDIS_DOMAIN_HASH = 'auto_cert_domains'  # 存储站点ID和域名的Hash
REDIS_CERT_QUEUE = 'auto_cert_queue'  # 存储待处理站点ID的List
REDIS_CURRENT_SITE_KEY = 'current_site_id'  # 存储当前使用的站点ID
# 域名配置
MAX_DOMAINS_PER_SITE = 100  # 每个站点最大域名数量
NGINX_CONF_PATH = '/www/cloud_waf/nginx/conf.d/vhost/'  # Nginx配置路径
# 脚本配置
DOMAINS_CHECK_SCRIPT = '/root/ajie/yunwei/domains_check_auto.sh'  # 域名检测脚本路径
ISSUE_CERT_SCRIPT = '/path/to/issue_cert.sh'  # 证书申请脚本路径
# API配置
DOMAIN_API_URL = 'http://*************/222.txt'  # 域名获取API地址

# 定义有效IP列表（从shell脚本中复制）
valid_ips = ["************", "**************", "***************", "**************", "*************", "**************",
             "***********", "***************", "***************", "**************", "***************",
             "***************", "**************", "**************", "**************", "**************",
             "**************", "*************", "*************", "*************", "*************", "*************",
             "*************", "*************", "*************", "**************", "**************", "**************",
             "**************", "**************", "**************", "**************", "**************", "**************",
             "**************", "*************0", "*************1", "*************2", "*************3", "*************4",
             "*************5", "*************6", "*************7", "*************8", "*************9", "*************0",
             "*************1", "*************2", "**************", "*************4", "*************5", "*************6",
             "*************7", "*************8", "*************9", "*************0", "*************1", "*************2",
             "*************3", "*************4", "*************5", "*************6", "*************7", "*************8",
             "*************9", "*************0", "*************1", "*************2", "*************3", "*************4",
             "*************5", "*************6", "*************7", "*************8", "*************9", "*************0",
             "*************1", "*************2", "*************3", "*************4", "*************5", "*************6",
             "*************7", "*************8", "*************9", "*************0", "*************1", "*************2",
             "*************3", "*************4", "*************5", "*************6", "*************7", "*************8",
             "*************9", "*************0", "*************1", "*************2", "*************3", "*************4",
             "*************5", "*************6", "*************7", "*************8", "*************9", "*************0",
             "*************1", "*************2", "*************3", "*************4", "*************5", "*************6",
             "*************7", "*************8", "*************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "**************0", "**************1",
             "**************2", "**************3", "**************4", "**************5", "**************6",
             "**************7", "**************8", "**************9", "*************00", "*************01",
             "*************02", "*************03", "*************04", "*************05", "*************06",
             "*************07", "*************08", "*************09", "*************10", "*************11",
             "*************12", "*************13", "*************14", "*************15", "*************16",
             "*************17", "*************18", "*************19", "*************20", "*************21",
             "*************22", "*************23", "*************24", "*************25", "*************26",
             "*************27", "*************28", "*************29", "*************30", "*************31",
             "*************32", "*************33", "*************34", "*************35", "*************36",
             "*************37", "*************38", "*************39", "*************40", "*************41",
             "*************42", "*************43", "*************44", "*************45", "*************46",
             "*************47", "*************48", "*************49", "*************50", "*************51",
             "*************52", "*************53", "*************54", "*************", "107.163.221.2", "107.163.221.3",
             "107.163.221.4", "107.163.221.5", "107.163.221.6", "107.163.221.7", "107.163.221.8", "107.163.221.9",
             "107.163.221.10", "107.163.221.11", "107.163.221.12", "107.163.221.13", "107.163.221.14", "107.163.221.15",
             "107.163.221.16", "107.163.221.17", "107.163.221.18", "107.163.221.19", "107.163.221.20", "107.163.221.21",
             "107.163.221.22", "107.163.221.23", "107.163.221.24", "107.163.221.25", "107.163.221.26", "107.163.221.27",
             "107.163.221.28", "107.163.221.29", "107.163.221.30", "107.163.221.31", "107.163.221.32", "107.163.221.33",
             "107.163.221.34", "107.163.221.35", "107.163.221.36", "107.163.221.37", "107.163.221.38", "107.163.221.39",
             "107.163.221.40", "107.163.221.41", "107.163.221.42", "107.163.221.43", "107.163.221.44", "107.163.221.45",
             "107.163.221.46", "107.163.221.47", "107.163.221.48", "107.163.221.49", "107.163.221.50", "107.163.221.51",
             "107.163.221.52", "107.163.221.53", "107.163.221.54", "107.163.221.55", "107.163.221.56", "107.163.221.57",
             "107.163.221.58", "107.163.221.59", "107.163.221.60", "107.163.221.61", "107.163.221.62", "107.163.221.63",
             "107.163.221.64", "107.163.221.65", "107.163.221.66", "107.163.221.67", "107.163.221.68", "107.163.221.69",
             "107.163.221.70", "107.163.221.71", "107.163.221.72", "107.163.221.73", "107.163.221.74", "107.163.221.75",
             "107.163.221.76", "107.163.221.77", "107.163.221.78", "107.163.221.79", "107.163.221.80", "107.163.221.81",
             "107.163.221.82", "107.163.221.83", "107.163.221.84", "107.163.221.85", "107.163.221.86", "107.163.221.87",
             "107.163.221.88", "107.163.221.89", "107.163.221.90", "107.163.221.91", "107.163.221.92", "107.163.221.93",
             "107.163.221.94", "107.163.221.95", "107.163.221.96", "107.163.221.97", "107.163.221.98", "107.163.221.99",
             "107.163.221.100", "107.163.221.101", "107.163.221.102", "107.163.221.103", "107.163.221.104",
             "107.163.221.105", "107.163.221.106", "107.163.221.107", "107.163.221.108", "107.163.221.109",
             "107.163.221.110", "107.163.221.111", "107.163.221.112", "107.163.221.113", "107.163.221.114",
             "107.163.221.115", "107.163.221.116", "107.163.221.117", "107.163.221.118", "107.163.221.119",
             "107.163.221.120", "107.163.221.121", "107.163.221.122", "107.163.221.123", "107.163.221.124",
             "107.163.221.125", "107.163.221.126", "107.163.221.127", "107.163.221.128", "107.163.221.129",
             "107.163.221.130", "107.163.221.131", "107.163.221.132", "107.163.221.133", "107.163.221.134",
             "107.163.221.135", "107.163.221.136", "107.163.221.137", "107.163.221.138", "107.163.221.139",
             "107.163.221.140", "107.163.221.141", "107.163.221.142", "107.163.221.143", "107.163.221.144",
             "107.163.221.145", "107.163.221.146", "107.163.221.147", "107.163.221.148", "107.163.221.149",
             "107.163.221.150", "107.163.221.151", "107.163.221.152", "107.163.221.153", "107.163.221.154",
             "107.163.221.155", "107.163.221.156", "107.163.221.157", "107.163.221.158", "107.163.221.159",
             "107.163.221.160", "107.163.221.161", "107.163.221.162", "107.163.221.163", "107.163.221.164",
             "107.163.221.165", "107.163.221.166", "107.163.221.167", "107.163.221.168", "107.163.221.169",
             "107.163.221.170", "107.163.221.171", "107.163.221.172", "107.163.221.173", "107.163.221.174",
             "107.163.221.175", "107.163.221.176", "107.163.221.177", "107.163.221.178", "107.163.221.179",
             "107.163.221.180", "107.163.221.181", "107.163.221.182", "107.163.221.183", "107.163.221.184",
             "107.163.221.185", "107.163.221.186", "107.163.221.187", "107.163.221.188", "107.163.221.189",
             "107.163.221.190", "107.163.221.191", "107.163.221.192", "107.163.221.193", "107.163.221.194",
             "107.163.221.195", "107.163.221.196", "107.163.221.197", "107.163.221.198", "107.163.221.199",
             "107.163.221.200", "107.163.221.201", "107.163.221.202", "107.163.221.203", "107.163.221.204",
             "107.163.221.205", "107.163.221.206", "107.163.221.207", "107.163.221.208", "107.163.221.209",
             "107.163.221.210", "107.163.221.211", "107.163.221.212", "107.163.221.213", "107.163.221.214",
             "107.163.221.215", "107.163.221.216", "107.163.221.217", "107.163.221.218", "107.163.221.219",
             "107.163.221.220", "107.163.221.221", "107.163.221.222", "107.163.221.223", "107.163.221.224",
             "107.163.221.225", "107.163.221.226", "107.163.221.227", "107.163.221.228", "107.163.221.229",
             "107.163.221.230", "107.163.221.231", "107.163.221.232", "107.163.221.233", "107.163.221.234",
             "107.163.221.235", "107.163.221.236", "107.163.221.237", "107.163.221.238", "107.163.221.239",
             "107.163.221.240", "107.163.221.241", "107.163.221.242", "107.163.221.243", "107.163.221.244",
             "107.163.221.245", "107.163.221.246", "107.163.221.247", "107.163.221.248", "107.163.221.249",
             "107.163.221.250", "107.163.221.251", "107.163.221.252", "107.163.221.253", "107.163.221.254",
             "211.174.59.136", "211.174.59.154", "211.174.59.221", "**************", "**************", "**************",
             "**************"]

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("domain_processor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("DomainProcessor")


class DomainProcessor:
    def __init__(self):
        """初始化域名处理器"""
        self.redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        logger.info("域名处理器初始化完成")

    def fetch_domains(self):
        """从API获取域名列表"""
        try:
            response = requests.get(DOMAIN_API_URL, timeout=30)
            response.raise_for_status()
            domains = response.text.strip()  # 移除字符串首尾空白字符（包括空格、换行符 \n、制表符 \t 等）
            logger.info(f"成功从API获取域名: {domains}")
            return domains
        except Exception as e:
            logger.error(f"获取域名失败: {str(e)}")
            return None

    def filter_domains(self, domains):
        """过滤域名，去除已存在和解析失败的域名"""
        if not domains:
            logger.warning("没有域名需要处理")
            return []
        # 创建临时文件保存域名
        temp_file = tempfile.mktemp()
        try:
            # 第一轮过滤：检查nginx配置中是否已存在该域名
            filtered_domains = []
            domain_list = domains.split()
            for domain in domain_list:
                # 执行grep命令检查域名是否已存在
                cmd = f'grep -roP "(^|\s){domain}(\s|;|$)" {NGINX_CONF_PATH}'
                result = subprocess.run(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if result.returncode != 0:
                    # 域名不存在，可以添加
                    filtered_domains.append(domain)
                else:
                    logger.info(f"域名 {domain} 已存在于nginx配置中，跳过")
            if not filtered_domains:
                logger.warning("第一轮过滤nginx后没有需要添加的域名")
                return []
            # 将第一轮过滤后的域名写入临时文件
            ## ['m31.jieruitech.info', 'm32.jieruitech.info', 'm33.jieruitech.info', 'm34.jieruitech.info']
            with open(temp_file, 'w') as f:
                f.write(' '.join(filtered_domains))

            # 第二轮过滤：直接在Python中实现域名解析检测
            # 创建成功和失败的临时文件
            failed_file = tempfile.mktemp()
            success_domains = []
            failed_domains = []

            # 遍历域名并进行解析检测
            for domain in domains:
                try:
                    # 使用socket.gethostbyname_ex获取域名的所有IP
                    _, _, resolved_ips = socket.gethostbyname_ex(domain)

                    if not resolved_ips:
                        # 解析失败
                        failed_domains.append(f"{domain} 解析失败")
                        logger.info(f"域名 {domain} 解析失败")
                        continue

                    # 检查解析的IP是否在有效IP列表中
                    valid_ip_found = False
                    ip_string = ""

                    for ip in resolved_ips:
                        ip_string += f" {ip}"
                        if ip in valid_ips:
                            valid_ip_found = True
                            break

                    if valid_ip_found:
                        # IP在白名单中，添加到成功列表
                        success_domains.append(domain)
                        logger.info(f"域名 {domain} 解析成功且IP在白名单中")
                    else:
                        # IP不在白名单中，添加到失败列表
                        failed_domains.append(f"{domain}{ip_string}")
                        logger.info(f"域名 {domain} 解析成功但IP不在白名单中: {ip_string}")

                except (socket.gaierror, socket.herror) as e:
                    # 解析出错
                    failed_domains.append(f"{domain} 解析错误: {str(e)}")
                    logger.info(f"域名 {domain} 解析出错: {str(e)}")

            # 将成功的域名写入原文件
            with open(temp_file, 'w') as f:
                f.write(' '.join(success_domains))

            # 将失败的域名写入失败文件
            with open(failed_file, 'w') as f:
                f.write('\n'.join(failed_domains))

            # 打印结果
            logger.info("可申请的域名:")
            logger.info("================")
            logger.info('\n'.join(success_domains))
            logger.info("================")

            logger.info("不可申请的域名:")
            logger.info("================")
            logger.info('\n'.join(failed_domains))
            logger.info("================")

            # 读取过滤后的域名文件
            final_domains = success_domains

            logger.info(f"域名过滤完成，有效域名: {final_domains}")
            return final_domains

        except Exception as e:
            logger.error(f"过滤域名时出错: {str(e)}")
            return []
        # finally:
        #     # 清理临时文件
        #     if os.path.exists(temp_file):
        #         os.remove(temp_file)

    def manage_domain_queue(self, domains):
        """管理域名队列，将域名添加到Redis中"""
        if not domains:
            logger.warning("没有域名需要添加到队列")
            return

        try:
            # 从Redis获取当前站点ID
            current_site_id = self.redis_client.get(REDIS_CURRENT_SITE_KEY)

            if not current_site_id:
                # 站点ID不存在，记录错误并退出
                logger.error("Redis中不存在站点ID，请手动创建站点ID并写入Redis的REDIS_CURRENT_SITE_KEY键")
                return

            # 获取当前站点已有的域名数量
            current_domains = self.redis_client.hget(REDIS_DOMAIN_HASH, current_site_id)
            current_domains_list = current_domains.split() if current_domains else []
            current_count = len(current_domains_list)

            logger.info(f"当前站点ID: {current_site_id}, 已有域名数量: {current_count}")

            for domain in domains:
                # 检查当前站点域名是否达到上限
                if current_count >= MAX_DOMAINS_PER_SITE:
                    # 将当前站点ID写入队列准备生成证书
                    self.redis_client.rpush(REDIS_CERT_QUEUE, current_site_id)
                    logger.info(f"站点 {current_site_id} 域名数量已达上限，加入证书生成队列")

                    # 创建新站点ID
                    site_num = int(current_site_id.replace("auto", "").replace(".com", ""))
                    current_site_id = f"auto{site_num + 1}.com"
                    self.redis_client.set(REDIS_CURRENT_SITE_KEY, current_site_id)

                    current_domains_list = []
                    current_count = 0
                    logger.info(f"创建新站点ID: {current_site_id}")

                # 添加域名到当前站点
                current_domains_list.append(domain)
                current_count += 1

                # 更新Redis中的域名列表
                self.redis_client.hset(REDIS_DOMAIN_HASH, current_site_id, ' '.join(current_domains_list))
                logger.info(f"域名 {domain} 已添加到站点 {current_site_id}, 当前域名数量: {current_count}")

            # 如果添加完所有域名后，当前站点有域名，将其加入队列
            if current_count > 0:
                self.redis_client.rpush(REDIS_CERT_QUEUE, current_site_id)
                logger.info(f"所有域名处理完毕，站点 {current_site_id} 加入证书生成队列")

                # 如果域名刚好满了100个，需要创建新站点ID以备下次使用
                if current_count >= MAX_DOMAINS_PER_SITE:
                    site_num = int(current_site_id.replace("auto", "").replace(".com", ""))
                    new_site_id = f"auto{site_num + 1}.com"
                    self.redis_client.set(REDIS_CURRENT_SITE_KEY, new_site_id)
                    logger.info(f"当前站点域名已满，创建新站点ID: {new_site_id} 以备下次使用")

        except Exception as e:
            logger.error(f"管理域名队列时出错: {str(e)}")

    def process(self):
        """执行完整的域名处理流程"""
        logger.info("开始处理域名...")
        domains = self.fetch_domains()
        if domains:
            filtered_domains = self.filter_domains(domains)
            sys.exit('100')
            if filtered_domains:
                self.manage_domain_queue(filtered_domains)
            else:
                logger.info("过滤后没有有效域名，跳过队列管理")
        else:
            logger.info("没有获取到域名，跳过后续处理")
        logger.info("域名处理完成")


def main():
    processor = DomainProcessor()
    processor.process()


if __name__ == "__main__":
    main()