# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import copy
import json
import os
import random
import sys
import time

from typing import List

from alibabacloud_nlb20220430.client import Client as Nlb20220430Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_nlb20220430 import models as nlb_20220430_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_util.client import Client as UtilClient


class AliyunNlb:
    def __init__(self):
        self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t8H92ANTArLFsxbJuzk'   # 账号3
        self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号3
        # self.ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5t8oKcEWDdM2G4gf1Tyh'   # 账号4
        # self.ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************' # 账号4
        self.DNS = '.cn-hongkong.nlb.aliyuncs.com'

    @staticmethod
    def create_client(
            access_key_id: str,
            access_key_secret: str,
    ) -> Nlb20220430Client:
        """
        使用AK&SK初始化账号Client
        @param access_key_id:
        @param access_key_secret:
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config(
            # 必填，您的 AccessKey ID,
            access_key_id=access_key_id,
            # 必填，您的 AccessKey Secret,
            access_key_secret=access_key_secret
        )
        # 访问的域名
        config.endpoint = f'nlb.cn-hongkong.aliyuncs.com'
        return Nlb20220430Client(config)

    def get_nlb_list(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        list_load_balancers_request = nlb_20220430_models.ListLoadBalancersRequest(
            load_balancer_type='network'
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.list_load_balancers_with_options(list_load_balancers_request, runtime).body.load_balancers
            nlb_active = []
            nlb_info = {}
            for li in resp:
                nlb_info['LoadBalancerId'] = li.load_balancer_id
                nlb_info['domain'] = li.dnsname
                nlb_info['nlb_status'] = li.load_balancer_status
                nlb_active.append(copy.deepcopy(nlb_info))
            return nlb_active
        except Exception as error:
            # 如有需要，请打印 error
            ConsoleClient.log(UtilClient.to_jsonstring(error))

    def get_nlb_domain(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        list_listeners_request = nlb_20220430_models.ListListenersRequest(
            # load_balancer_ids=[
            #     'nlb-x8lbrtcco1zcgb894c'
            # ]
        )
        runtime = util_models.RuntimeOptions()
        try:
            nlb_active = self.get_nlb_list()
            resp = client.list_listeners_with_options(list_listeners_request, runtime).body.listeners
            for li in resp:
                for id in nlb_active:
                    if id.get('LoadBalancerId') == li.load_balancer_id:
                        id['domain'] = "http://" + id.get('domain') + ":" + str(li.listener_port)
            return nlb_active
        except Exception as error:
            UtilClient.assert_as_string(error)

    # 创建nlb实例
    def create_nlb(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        load_balancer_billing_config = nlb_20220430_models.CreateLoadBalancerRequestLoadBalancerBillingConfig(
            # String, 可选, 网络型负载均衡实例的计费类型。  仅取值**PostPay**：表示按量计费。,
            pay_type='PostPay'
        )
        zone_mappings_0 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            # String, 必填, 可用区对应的交换机，每个可用区只能使用一台交换机和一个子网。至少需要添加2个可用区，最多支持添加10个可用区。,
            # need modify
            v_switch_id='vsw-j6cazcymayuyuy32mje97',    # 账号3-1
            # v_switch_id='vsw-j6cvmcbx1py1tvj9sygip',  # 账号4-A
            # String, 必填, 网络型负载均衡实例的可用区ID。至少需要添加2个可用区，最多支持添加10个可用区。  您可以通过调用[DescribeZones](~~443890~~)接口获取可用区ID。,
            zone_id='cn-hongkong-b'
        )
        zone_mappings_1 = nlb_20220430_models.CreateLoadBalancerRequestZoneMappings(
            v_switch_id='vsw-j6cp42pmqagx2j1icaui4',    # 账号3-1
            # v_switch_id='vsw-j6cnnjy3x2e73pkjjcqkh',    # 账号4-A
            zone_id='cn-hongkong-c'
        )
        create_load_balancer_request = nlb_20220430_models.CreateLoadBalancerRequest(
            load_balancer_type='network',
            load_balancer_name='客户1',
            address_type='Internet',
            address_ip_version='ipv4',
            vpc_id='vpc-j6cgar6b7f8pmi98iqzhu', # 账号3-1
            # vpc_id='vpc-j6cl2ehrwuxq61tnz8xz9', # 账号4-A
            # Array, 可选,
            zone_mappings=[
                zone_mappings_0,
                zone_mappings_1
            ],
            # Object, 可选,
            load_balancer_billing_config=load_balancer_billing_config,
            region_id='cn-hongkong'
        )
        runtime = util_models.RuntimeOptions()
        nlb_id = client.create_load_balancer_with_options(create_load_balancer_request, runtime).body.loadbalancer_id
        # 循环等待nlb, 直到状态为Active
        while True:
            lis = self.get_nlb_list()
            for li in lis:
                if li.get('LoadBalancerId') == nlb_id and li.get('nlb_status') == 'Active':
                    break
            else:
                time.sleep(9)
                continue
            break
        return str(nlb_id)

    def create_listen(self, nlb_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        create_listener_request = nlb_20220430_models.CreateListenerRequest(
            listener_protocol='TCP',
            listener_port=80,
            # listener_port=random.randint(5000, 65500),
            load_balancer_id=str(nlb_id),
            server_group_id='sgp-pth8xtvb7w5ok3fj6w' # 账号3-1 服务器组3-1-http 
            # server_group_id='sgp-pnuwzzhevdd1o34o44' # 账号4-A 香港服务器组A 
            # server_group_id='sgp-eu8ertn00ysa9acy6v' # 账号4-A 香港服务器组B
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.create_listener_with_options(create_listener_request, runtime).body.listener_id
            return str(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def create_listen_https(self, nlb_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        create_listener_request = nlb_20220430_models.CreateListenerRequest(
            region_id='cn-hongkong',
            listener_protocol='TCPSSL',
            listener_port=443,
            # load_balancer_id='nlb-2cf3jud0qxofc9s7zl',
            load_balancer_id=str(nlb_id),
            server_group_id='sgp-fpc4l7adk7iwyxgyaf', # 账号3-1 服务器组3-1-https 
            # server_group_id='sgp-0p0y0tbzel1atej655', # 账号4-A 服务器A_443
            security_policy_id='tls_cipher_policy_1_0',
            certificate_ids=[
                '10922726-cn-hangzhou'  # 账号3-1 382l.pw
                # '10820998-cn-hangzhou'  # 账号4-A 382l.pw
            ]
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.create_listener_with_options(create_listener_request, runtime).body.listener_id
            # print("listener_id: " + resp)
            return str(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def get_https_listen_status(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        ## 查询443端口监听status
        runtime = util_models.RuntimeOptions()
        list_listeners_request = nlb_20220430_models.ListListenersRequest(
            region_id='cn-hongkong',
            listener_ids=[listen_https_id])
        lis = client.list_listeners_with_options(list_listeners_request, runtime).body.listeners
        return lis

    def create_additional_ca(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)

        runtime = util_models.RuntimeOptions()
        while True:
            https_listen = self.get_https_listen_status(listen_https_id)
            for li in https_listen[:1]:
                if li.listener_status == 'Running':
                    break
            else:
                time.sleep(3)
                continue
            break

        associate_additional_certificates_with_listener_request = nlb_20220430_models.AssociateAdditionalCertificatesWithListenerRequest(
            region_id='cn-hongkong',
            additional_certificate_ids=[
                '10922776-cn-hangzhou',     # 账号3-1 5755.tv
                '10922827-cn-hangzhou',     # 账号3-1 382.tv
                '10922844-cn-hangzhou',     # 账号3-1 149.tv
                '10922855-cn-hangzhou',     # 账号3-1 pokjs.nl_sadas.nl
                '10922873-cn-hangzhou',     # 账号3-1 382811.cn_575811.cn

                # '10838944-cn-hangzhou',   # 账号4-A 5755.tv
                # '10838957-cn-hangzhou',   # 账号4-A 382.tv
                # '10838963-cn-hangzhou',   # 账号4-A 149.tv
                # '10856441-cn-hangzhou',   # 账号4-A pokjs.nl_sadas.nl
                # '10860125-cn-hangzhou',   # 账号4-A 382811.cn_575811.cn
                # '10867201-cn-hangzhou',   # 账号4-A 382z.tv
                # '10915148-cn-hangzhou',   # 账号4-A 62xf.cc
            ],
            listener_id=listen_https_id,
        )
        try:
            resp = client.associate_additional_certificates_with_listener_with_options(associate_additional_certificates_with_listener_request, runtime).body
            # print(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def create_additional_ca2(self, listen_https_id):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)

        runtime = util_models.RuntimeOptions()
        while True:
            https_listen = self.get_https_listen_status(listen_https_id)
            for li in https_listen[:1]:
                if li.listener_status == 'Running':
                    break
            else:
                time.sleep(3)
                continue
            break

        associate_additional_certificates_with_listener_request = nlb_20220430_models.AssociateAdditionalCertificatesWithListenerRequest(
            region_id='cn-hongkong',
            additional_certificate_ids=[
                '10922875-cn-hangzhou',     # 账号3-1 382z.tv
                '10922896-cn-hangzhou',     # 账号3-1 62xf.cc

            ],
            listener_id=listen_https_id,
        )
        try:
            resp = client.associate_additional_certificates_with_listener_with_options(associate_additional_certificates_with_listener_request, runtime).body
            # print(resp)
        except Exception as error:
            UtilClient.assert_as_string(error)

    def delete_nlb(self):
        client = self.create_client(self.ALIBABA_CLOUD_ACCESS_KEY_ID, self.ALIBABA_CLOUD_ACCESS_KEY_SECRET)
        ## 删除最早一个nlb实例，如果大于等于5个可用域名，拿到最旧域名id并执行删除
        domain = self.get_nlb_domain()
        pre_delete_id = ''
        count = 0
        for i, li in enumerate(domain):
            if li.get('nlb_status') == 'Active':
                count+=1
            if i == len(domain) - 1:    ## 拿到最后一个域名id
                pre_delete_id = li.get('LoadBalancerId')
        if count >= 5:
            print("当前域名数：" + str(count) + " |执行删除：" + pre_delete_id)
            delete_load_balancer_request = nlb_20220430_models.DeleteLoadBalancerRequest(
                load_balancer_id=pre_delete_id
            )
            runtime = util_models.RuntimeOptions()
            try:
                resp = client.delete_load_balancer_with_options(delete_load_balancer_request, runtime).status_code
                print("return code:" + str(resp))
            except Exception as error:
                UtilClient.assert_as_string(error)
        else:
            print("当前nlb实例个数:" + str(count) + ", 不再进行删除！")
            print(domain)

    def main(self, operation):
        if operation == 'create':
            ## 创建nlb实例和监听端口
            nlb_id = self.create_nlb()
            http_port = self.create_listen(nlb_id).split('@')[1] # 创建80端口监听
            listen_https_id = self.create_listen_https(nlb_id)  # 拿到https监听的id
            # nlb_port = listen_https_id.split('@')[1]            # 拿到https监听端口
            self.create_additional_ca(listen_https_id)          # 创建扩展证书，一次最多关联15个
            time.sleep(3)
            self.create_additional_ca2(listen_https_id)          # 创建扩展证书，一次最多关联15个
            print("http://" + nlb_id + self.DNS)                # 输出创建好的域名
            # print("http://" + nlb_id + self.DNS + ":" + nlb_port)
        elif operation == 'select':
            ## 获取存活的nlb实例信息
            lis = self.get_nlb_domain()
            print(lis)
            with open('available_domain.txt', 'w') as f:
                for i, li in enumerate(lis):
                    domain = li.get('domain')
                    f.write(str(domain))
                    if i != len(lis) - 1:  # 当前不是最后一个元素
                        f.write('\n')
        elif operation == 'delete':
            self.delete_nlb()
        else:
            print("请输入需要操作的动作：create,select, delete")


if __name__ == '__main__':
    if len(sys.argv) > 1:
        arg1 = sys.argv[1]
    else:
        arg1 = 'select'
    AliyunNlb().main(str(arg1))
