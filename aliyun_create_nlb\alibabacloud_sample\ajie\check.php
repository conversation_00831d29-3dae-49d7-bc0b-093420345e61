<?php
//循环检测指定网址
date_default_timezone_set("Asia/Shanghai");
set_time_limit(300);
$list = array(
	'1' => array(
		'api_url' => 'https://aly.zwtch.com/api.php',//接口地址
		'api_password' => 'hao123.com',//接口密码
	),
	'2' => array(
		'api_url' => 'https://aly.zgkjzh.com/api.php',//接口地址
		'api_password' => 'hao123.com',//接口密码
	),
	'3' => array(
		'api_url' => 'https://69xf.co/api.php',//接口地址
		'api_password' => 'hao123.com',//接口密码
	),
);
$check_url = array(
	335 => 'http://nlb-ds81hj2rta0kynz7ti.cn-chengdu.nlb.aliyuncs.com:18881',
	336 => 'http://nlb-b0vox9c11eej1jpowp.cn-chengdu.nlb.aliyuncs.com:19856',
	337 => 'http://nlb-f1ajq7nzbohs3ms0yz.cn-chengdu.nlb.aliyuncs.com:15933',
	338 => 'http://nlb-qe22q26x39ywmph71m.cn-chengdu.nlb.aliyuncs.com:12780',
        339 => 'http://nlb-59n5b9mu2115yeln91.cn-chengdu.nlb.aliyuncs.com:55448',
        340 => 'http://nlb-4304vtbj0lpww3n9yr.cn-chengdu.nlb.aliyuncs.com:46436',
        341 => 'http://nlb-cgdhkbets2ms1n33vt.cn-chengdu.nlb.aliyuncs.com:31255',
        342 => 'http://nlb-wjf6m797rtvbu4l4c5.cn-chengdu.nlb.aliyuncs.com:27412',
        343 => 'http://nlb-v7ty9zuwt19er1248m.cn-chengdu.nlb.aliyuncs.com:28904',
        344 => 'http://nlb-nkms7uz3w968nriybc.cn-chengdu.nlb.aliyuncs.com:53504',
        345 => 'http://nlb-8huhp2j9uodevo7p4q.cn-chengdu.nlb.aliyuncs.com:5644',
        346 => 'http://nlb-b58ntcbdngo35np2x3.cn-chengdu.nlb.aliyuncs.com:37916',
        347 => 'http://nlb-6dfvhemfkhyvutt8nd.cn-chengdu.nlb.aliyuncs.com:36287',
        348 => 'http://nlb-k4127om18jpjslz30o.cn-chengdu.nlb.aliyuncs.com:42477',
        349 => 'http://nlb-leuy8i68t1d8t4fyod.cn-chengdu.nlb.aliyuncs.com:10583',
        350 => 'http://nlb-f2yp823secudjpu7f7.cn-chengdu.nlb.aliyuncs.com:57847',
);//使用域名标记注释
$file = explode("/", $_SERVER['PHP_SELF']);
$filesubstr = strlen($file[count($file) - 1]);
$path = str_replace('\\', '/', substr(__FILE__, 0, -$filesubstr));
$cache_name = $path.'check_url_data.txt';
$cache_data = json_decode(file_get_contents($cache_name), true);
$update_cache = 0;
if(!empty($list)){
	if(empty($cache_data['num'])){
		$cache_data = array('num' => 1, 'push_time' => 0);//默认第一个开始
	}
	$status = curl_post($check_url[$cache_data['num']].'/123.txt');
	if($status['status'] == 2){
		//域名被封，通知客户换域名
		foreach($list as $k => $v){
			$post_url = $v['api_url'].'?api_update='.$v['api_password'].'&api_domain='.urlencode($check_url[$cache_data['num']+1]);
			$post_data = curl_post($post_url);
		}
                echo "check domain ".$cache_data['num']." error\r\n";
		$cache_data['num'] += 1;
		$update_cache = 1;
	}else{
		echo "check domain ".$cache_data['num']." ok\r\n";
	}
}
if($update_cache == 1){
	file_put_contents($cache_name, json_encode($cache_data));
}
//echo "run ok ".date("Y-m-d H:i:s", time())."\r\n";
echo "run ok ".$cache_data['num'].' - '.date("Y-m-d H:i:s", time())."\r\n";

function push_sms($k, $info, $cache_data) {
	if(empty($cache_data[$k]['push_time']) || $cache_data[$k]['push_time'] + 3600 < time()){
		$sms_url = 'http://www.pushplus.plus/send?token=27a922b683484da39f1f268b4d6f526a&topic=durl';
		$sms_post_data = array('title' => '客户'.$k.'告警', 'content' => $info, 'template' => 'txt');
		$data = curl_post($sms_url, $sms_post_data);
		if($data['status'] == 1){
			$data['body'] = json_decode($data['body'], true);
			if(!empty($data['body']['code']) && $data['body']['code'] == 200){
				$cache_data[$k]['push_time'] = time();
				echo "post sms to ".$k." ok\r\n";
			}else{
				echo "post sms to ".$k." error ".json_encode($data['body'])."\r\n";
			}
		}
	}
	return $cache_data;
}
function curl_post($url, $data = array(), $num = 1) {
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_HEADER, 0);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0 );
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0 );
	curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1); // 302 redirect
	curl_setopt($ch, CURLOPT_TIMEOUT, 5 );
	if(!empty($data)){
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
	}
	$body = curl_exec($ch);
	$Headers =  curl_getinfo($ch);
	curl_close($ch);
	//print_r($Headers);exit;
	if($Headers['http_code'] >= 200 && $Headers['http_code'] < 300){
		return array('status' => 1, 'header' => $Headers, 'body' => $body);
	}else if($num > 3){
		return array('status' => 2, 'header' => $Headers, 'body' => $body);
	}else{
		$num += 1;
		return curl_post($url, $data, $num);
	}
}
