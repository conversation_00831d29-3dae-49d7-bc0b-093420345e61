# ACME自动化申请SSL证书

## 产品概述

ACME自动化申请SSL证书系统是一个基于ACME协议的SSL/TLS证书管理工具，旨在简化证书的申请流程。
本系统适用于多服务器环境下工作的场景，减少手动操作和人为错误。

## ACME 证书申请流程图
                                 +------------------------+
                                 |      启动 acme.sh      |
                                 +------------------------+
                                             |
                                             v
+------------------------+      +------------------------+      +------------------------+
|     解析命令行参数     |----->|     初始化路径和配置    |----->|     创建/检查账户     |
+------------------------+      +------------------------+      +------------------------+
                                             |                              |
                                             v                              v
+------------------------+      +------------------------+      +------------------------+
|   准备域名验证环境     |<-----|    创建域名密钥/CSR    |<-----|    注册/检查账户信息   |
+------------------------+      +------------------------+      +------------------------+
        |                                                                   
        v                                                                   
+------------------------+      +------------------------+      +------------------------+
|   创建验证文件/目录    |----->|   向 ACME 服务器下单   |----->|    获取验证挑战信息    |
+------------------------+      +------------------------+      +------------------------+
                                             |                              |
                                             v                              v
+------------------------+      +------------------------+      +------------------------+
|  写入验证文件到webroot |----->|  同步验证文件到其他服务器 |--->| 触发域名所有权验证过程 |
+------------------------+      +------------------------+      +------------------------+
                                             |
                                             v
+------------------------+      +------------------------+      +------------------------+
|  轮询检查验证是否完成  |----->|     完成订单并签发     |----->|     下载和安装证书     |
+------------------------+      +------------------------+      +------------------------+
                                             |
                                             v
                                 +------------------------+
                                 |     清理验证文件      |
                                 +------------------------+

### 核心功能

1. 命令行解析模块
处理命令行参数：--issue（表示申请证书）
设置证书颁发机构：--server letsencrypt
指定验证方式：-w /usr/share/nginx/html（使用 webroot 方式验证）
指定证书保存路径：--cert-home /root/.acme.sh/autob9
强制模式：--force（即使证书有效也强制更新）
设置域名：-d grpph.cc
2. 账户管理模块
检查本地是否有账户密钥
如果没有则创建新的账户密钥对
向 Let's Encrypt 注册或验证账户信息
3. 域名验证模块
HTTP-01 验证方式（通过 webroot）：
在 webroot 目录下创建 .well-known/acme-challenge/ 路径
在该路径下创建包含特定内容的验证文件
同步验证文件到其他服务器（代码中的 _sync_to_remote_servers 函数）
Let's Encrypt 服务器通过 HTTP 访问验证文件来确认域名所有权
4. 证书签发与安装模块
向 ACME 服务器发送 CSR（证书签名请求）
获取已签名的证书
保存证书到指定位置（--cert-home 参数指定）
组装完整证书链

### 执行流程详解
1. 初始化：解析命令行参数，设置工作目录和配置
2. 账户检查：检查或创建账户，向 Let's Encrypt 注册
3. 域名密钥创建：为域名生成私钥（如果不存在）
4. CSR 创建：生成证书签名请求
5. 验证准备：
创建 webroot 验证目录结构
准备验证所需的令牌和密钥授权
6. 域名验证：
在 /usr/share/nginx/html/.well-known/acme-challenge/ 创建验证文件
将验证文件同步到其他服务器（如配置了多服务器）
通知 Let's Encrypt 可以开始验证
Let's Encrypt 服务器访问验证文件确认域名所有权
7. 签发证书：
验证通过后，完成订单
获取已签名的证书
8. 安装与配置：
保存证书到 /root/.acme.sh/auto{a,b}{n}/ 目录
生成完整证书链
应用后续操作（如果配置了 post-hook）

### 核心验证机制
对于 HTTP-01 验证（webroot 方式），核心步骤是：
创建特定文件：在 /.well-known/acme-challenge/ 目录下创建一个文件，文件名为 ACME 服务器提供的令牌
文件内容：令牌+账户密钥指纹的组合（keyAuthorization）
验证过程：Let's Encrypt 服务器通过 HTTP 请求访问该文件，验证其内容是否正确
验证成功：表示申请者控制该域名，可以颁发证书
这种方法确保了只有真正控制域名的人才能获得该域名的 SSL 证书，防止了证书欺诈行为。

## 技术架构

系统采用模块化设计，主要包括以下模块：

1. **命令行解析模块**：处理用户输入的命令和参数
2. **账户管理模块**：管理ACME账户的创建和注册
3. **域名验证模块**：实现多种域名验证方式
4. **证书申请模块**：生成CSR并获取证书
5. **证书安装模块**：保存和安装证书
6. **多服务器同步模块**：将验证文件同步到多台服务器
7. **定时任务模块**：自动检查和续期证书

### 工作流程

```
flowchart TD
    START[开始执行 acme.sh] --> PARSE[解析命令行参数]
    PARSE --> INIT[初始化环境和配置]
    INIT --> CHECK_ACCOUNT{检查账户\n是否存在}
    CHECK_ACCOUNT -- 否 --> CREATE_ACCOUNT[创建新账户并注册]
    CHECK_ACCOUNT -- 是 --> CHECK_KEY{检查域名\n密钥是否存在}
    CREATE_ACCOUNT --> CHECK_KEY
    
    CHECK_KEY -- 否 --> CREATE_KEY[创建域名密钥]
    CHECK_KEY -- 是 --> CHECK_CSR{检查CSR\n是否存在}
    CREATE_KEY --> CHECK_CSR
    
    CHECK_CSR -- 否 --> CREATE_CSR[创建证书签名请求]
    CHECK_CSR -- 是 --> PREPARE[准备域名验证]
    CREATE_CSR --> PREPARE
    
    PREPARE --> NEW_ORDER[向ACME服务器创建新订单]
    NEW_ORDER --> GET_CHALLENGE[获取域名验证挑战]
    
    GET_CHALLENGE --> HTTP_01[执行HTTP-01验证]
    
    HTTP_01 --> POLL{轮询检查\n验证状态}
    POLL -- 验证中 --> POLL
    POLL -- 验证失败 --> ERROR[验证失败处理]
    POLL -- 验证成功 --> FINALIZE[完成订单]
    
    FINALIZE --> GET_CERT[获取证书]
    GET_CERT --> SAVE_CERT[保存证书到指定位置]
    SAVE_CERT --> CLEANUP[清理验证文件]
    CLEANUP --> END[结束]
    
    ERROR --> CLEANUP_ERR[清理错误和临时文件]
    CLEANUP_ERR --> END_ERR[错误结束]
```

### HTTP-01验证流程

1. 在Webroot路径下创建`.well-known/acme-challenge/`目录
2. 创建包含特定内容的令牌文件
3. 同步验证文件到其他服务器（如配置）
4. 通知CA开始验证
5. CA通过HTTP访问验证文件
6. 验证成功后，继续证书申请流程

```
flowchart TD
    HTTP_START[开始HTTP-01验证] --> CHECK_PATH{检查webroot\n路径是否存在}
    CHECK_PATH -- 不存在 --> CREATE_PATH[创建目录结构]
    CHECK_PATH -- 存在 --> CREATE_CHALLENGE[创建验证文件]
    CREATE_PATH --> CREATE_CHALLENGE
    
    CREATE_CHALLENGE --> |在webroot/.well-known/acme-challenge/下创建token文件| WRITE_TOKEN[写入密钥授权到文件]
    WRITE_TOKEN --> CHMOD[设置文件权限为可公开读取]
    
    CHMOD --> SYNC{是否配置了\n多服务器同步}
    SYNC -- 是 --> SYNC_FILE[同步验证文件到其他服务器]
    SYNC -- 否 --> NOTIFY_READY
    SYNC_FILE --> NOTIFY_READY[通知ACME服务器验证准备就绪]
    
    NOTIFY_READY --> WAIT[等待Let's Encrypt服务器验证]
    WAIT --> HTTP_END[HTTP-01验证完成]
```

## 使用示例

当执行 sh /root/.acme.sh/acme.sh --issue --server letsencrypt -w /usr/share/nginx/html --cert-home /root/.acme.sh/autob9 --force -d grpph.cc 时:
```bash
flowchart TD
    START[开始执行命令] --> PARSE[解析命令参数]
    
    PARSE --> SET_CMD[设置命令类型: issue]
    PARSE --> SET_SERVER[设置CA服务器: Let's Encrypt]
    PARSE --> SET_WEBROOT[设置验证方式: webroot]
    PARSE --> SET_WEBROOT_PATH[设置webroot路径: /usr/share/nginx/html]
    PARSE --> SET_CERT_HOME[设置证书目录: /root/.acme.sh/autob9]
    PARSE --> SET_FORCE[设置强制更新标志]
    PARSE --> SET_DOMAIN[设置域名: grpph.cc]
    
    SET_CMD & SET_SERVER & SET_WEBROOT & SET_WEBROOT_PATH & SET_CERT_HOME & SET_FORCE & SET_DOMAIN --> INIT[初始化环境]
    
    INIT --> EXEC_ISSUE[执行issue函数]
    EXEC_ISSUE --> DOMAIN_VALIDATION[进行域名验证\n(HTTP-01 webroot方式)]
    
    DOMAIN_VALIDATION --> |创建验证文件:| VALIDATION_PATH[/usr/share/nginx/html/.well-known/acme-challenge/token文件]
    VALIDATION_PATH --> SYNC[同步验证文件到其他服务器(如配置)]
    
    SYNC --> CHECK_VALIDATION[Let's Encrypt服务器检查验证文件]
    CHECK_VALIDATION --> ISSUE_CERT[颁发证书]
    
    ISSUE_CERT --> SAVE_CERT[保存证书到指定路径:\n/root/.acme.sh/autob9/]
    SAVE_CERT --> SAVE_FILES[生成并保存相关文件:\ncert.pem (证书)\nprivkey.pem (私钥)\nchain.pem (CA链)\nfullchain.pem (完整证书链)]
    
    SAVE_FILES --> CLEANUP[清理验证文件]
    CLEANUP --> END[完成]
```



## Let's Encrypt 证书申请完整流程

```
flowchart TD
    START[开始] --> PARSE[解析命令行参数\n--issue --server letsencrypt -w /usr/share/nginx/html --cert-home /root/.acme.sh/autob9 --force -d grpph.cc]
    PARSE --> INIT[初始化环境\n设置工作目录和配置]
    
    subgraph 账户管理阶段
        INIT --> CHECK_ACCOUNT{检查账户密钥\n是否存在}
        CHECK_ACCOUNT -- 否 --> CREATE_ACCOUNT[创建新的账户密钥]
        CREATE_ACCOUNT --> REGISTER[向Let's Encrypt注册账户]
        CHECK_ACCOUNT -- 是 --> ACCOUNT_OK[使用现有账户]
        REGISTER --> ACCOUNT_OK
    end
    
    subgraph 密钥和CSR准备阶段
        ACCOUNT_OK --> CHECK_DOMAIN_KEY{域名密钥\n是否存在}
        CHECK_DOMAIN_KEY -- 否 --> CREATE_DOMAIN_KEY[创建域名密钥]
        CHECK_DOMAIN_KEY -- 是 --> DOMAIN_KEY_OK[使用现有域名密钥]
        CREATE_DOMAIN_KEY --> DOMAIN_KEY_OK
        DOMAIN_KEY_OK --> CREATE_CSR[创建证书签名请求]
    end
    
    subgraph ACME协议交互阶段
        CREATE_CSR --> NEW_ORDER[创建新的证书订单]
        NEW_ORDER --> GET_CHALLENGES[获取域名验证挑战]
        GET_CHALLENGES --> PREPARE_HTTP01[准备HTTP-01验证]
    end
    
    subgraph HTTP-01验证阶段
        PREPARE_HTTP01 --> CREATE_WELLKNOWN[创建.well-known/acme-challenge目录]
        CREATE_WELLKNOWN --> CREATE_TOKEN_FILE[创建验证令牌文件]
        CREATE_TOKEN_FILE --> WRITE_KEYAUTH[写入密钥授权到文件]
        WRITE_KEYAUTH --> SYNC_FILES[同步验证文件到其他服务器]
        SYNC_FILES --> TRIGGER_VALIDATION[触发域名验证]
        TRIGGER_VALIDATION --> POLL_STATUS[轮询验证状态]
        POLL_STATUS -- 验证中 --> POLL_STATUS
        POLL_STATUS -- 验证失败 --> VALIDATION_FAILED[验证失败]
        POLL_STATUS -- 验证成功 --> VALIDATION_OK[验证成功]
    end
    
    subgraph 证书签发和安装阶段
        VALIDATION_OK --> FINALIZE_ORDER[完成证书订单]
        FINALIZE_ORDER --> DOWNLOAD_CERT[下载证书]
        DOWNLOAD_CERT --> SAVE_CERT[保存证书到指定位置\n/root/.acme.sh/autob9/]
        SAVE_CERT --> CLEANUP[清理临时文件和验证文件]
    end
    
    CLEANUP --> END[结束]
    VALIDATION_FAILED --> CLEANUP_ERROR[清理错误]
    CLEANUP_ERROR --> END_ERROR[错误结束]
```




